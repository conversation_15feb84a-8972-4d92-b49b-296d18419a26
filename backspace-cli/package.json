{"name": "backspace-cli", "version": "1.0.0", "description": "CLI to interact with backspace", "main": "index.js", "bin": {"backspace": "./bin/backspace.js"}, "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "build": "node scripts/build.js", "build:linux": "node scripts/build.js --platform linux", "build:windows": "node scripts/build.js --platform windows", "build:macos": "node scripts/build.js --platform darwin"}, "keywords": ["cli", "backspace"], "author": "", "license": "ISC", "engines": {"node": ">=14.0.0"}, "preferGlobal": true}