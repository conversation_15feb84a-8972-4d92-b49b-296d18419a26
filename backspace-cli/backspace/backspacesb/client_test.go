package backspacesb

import (
	"fmt"
	"testing"
)

func TestGetClient(t *testing.T) {
	client := GetClient()

	fmt.Println(client.Repository.ID)
}

func TestScanCache(t *testing.T) {
	fmt.Println("=== Testing Modular Scan Cache ===")
	
	// Get client
	client := GetClient()
	
	// Check if repo exists
	if client.Repository == nil {
		fmt.Println("No repository found")
		return
	}
	
	fmt.Printf("Repository: %s (ID: %d)\n", client.Repository.Name, client.Repository.ID)
	
	// Test cache operations
	fmt.Println("\n--- Cache Operations ---")
	allScans := client.GetCachedScans()
	fmt.Printf("Total scans in cache: %d\n", len(allScans))
	
	summaries := client.GetScanSummaries()
	fmt.Println("\nScan Summaries (ID, Branch):")
	for _, s := range summaries {
		fmt.Printf("  - ID: %d, Branch: %s\n", s.ID, s.Branch)
	}
	
	// Test database operations
	fmt.Println("\n--- Database Operations ---")
	dbScans, err := client.GetCurrentRepoScans()
	if err != nil {
		fmt.Printf("Error getting scans from DB: %v\n", err)
	} else {
		fmt.Printf("Scans from database: %d\n", len(dbScans))
	}
	
	// Test branch filtering
	if len(summaries) > 0 {
		testBranch := summaries[0].Branch
		cachedBranchScans := client.GetCachedScansByBranch(testBranch)
		fmt.Printf("\nCached scans for branch '%s': %d\n", testBranch, len(cachedBranchScans))
		
		dbBranchScans, err := client.GetScansByBranch(testBranch)
		if err != nil {
			fmt.Printf("Error getting branch scans from DB: %v\n", err)
		} else {
			fmt.Printf("DB scans for branch '%s': %d\n", testBranch, len(dbBranchScans))
		}
	}
	
	// Test cache management
	fmt.Println("\n--- Cache Management ---")
	err = client.RefreshCache()
	if err != nil {
		fmt.Printf("Error refreshing cache: %v\n", err)
	} else {
		fmt.Printf("Cache refreshed. Total scans: %d\n", len(client.GetCachedScans()))
	}
	
	// Test clear cache
	client.ClearCache()
	fmt.Printf("Cache cleared. Total scans: %d\n", len(client.GetCachedScans()))
	
	// Reload cache
	client.LoadScansCache()
	fmt.Printf("Cache reloaded. Total scans: %d\n", len(client.GetCachedScans()))
}

func TestBranchScanCrossReference(t *testing.T) {
	fmt.Println("=== Testing Branch-Scan Cross Reference ===")
	
	client := GetClient()
	if client.Repository == nil {
		fmt.Println("No repository found")
		return
	}
	
	// Simulate local branches (in real usage, this comes from git)
	localBranches := []string{"main", "feature-branch", "dev", "backspace-cli-list"}
	
	fmt.Printf("Local branches to check: %v\n", localBranches)
	
	// Get scan info for all local branches
	branchInfos := client.GetBranchScanInfo(localBranches)
	
	fmt.Println("\nBranch scan information:")
	for _, info := range branchInfos {
		status := "❌ No scans"
		if info.HasScans {
			status = fmt.Sprintf("✅ %d scans", info.ScanCount)
			if info.LatestScan != nil {
				status += fmt.Sprintf(" (latest: %s)", info.LatestScan.CreatedAt.Format("2006-01-02"))
			}
		}
		fmt.Printf("  %s: %s\n", info.Branch, status)
	}
	
	// Test individual branch checks
	fmt.Println("\nIndividual branch checks:")
	for _, branch := range localBranches {
		hasScans := client.HasScansForBranch(branch)
		fmt.Printf("  %s has scans: %t\n", branch, hasScans)
		
		if hasScans {
			latest := client.GetLatestScanForBranch(branch)
			if latest != nil {
				fmt.Printf("    Latest scan: ID=%d, Status=%s, Created=%s\n", 
					latest.ID, latest.Status, latest.CreatedAt.Format("2006-01-02 15:04"))
			}
		}
	}
}
