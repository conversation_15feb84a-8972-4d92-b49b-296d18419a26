package tables

import "time"

type Scan struct {
	ID        int                    `json:"id"`
	CreatedAt time.Time              `json:"created_at"`
	Trigger   string                 `json:"trigger"`
	Status    string                 `json:"status"`
	Duration  string                 `json:"duration"`
	TokenCost string                 `json:"token_cost"`
	RepoID    int                    `json:"repo_id" fk:"repositories.id"`
	Summary   string                 `json:"summary"`
	CreatedBy int                    `json:"created_by"`
	Branch    string                 `json:"branch"`
	Metrics   map[string]interface{} `json:"metrics"`
}
