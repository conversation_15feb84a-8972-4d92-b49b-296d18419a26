package tables

import "time"

type Issue struct {
	ID              int       `json:"id"`
	CreatedAt       time.Time `json:"created_at"`
	Name            string    `json:"name"`
	Description     string    `json:"description"`
	SectionID       int       `json:"section_id"`
	SectionName     string    `json:"section_name"`
	Status          string    `json:"status"`
	PRLink          string    `json:"pr_link"`
	Branch          string    `json:"branch"`
	Hash            string    `json:"hash"`
	DependenciesIDs []int     `json:"dependencies_ids"`
}
