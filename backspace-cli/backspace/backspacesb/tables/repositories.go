package tables

import "time"

type Repository struct {
	ID            int       `json:"id"`
	CreatedAt     time.Time `json:"created_at"`
	RepoUrl       string    `json:"url"`
	DaytonaImage  string    `json:"daytona_image"`
	OrgID         int       `json:"org_id"`
	Name          string    `json:"name"`
	RepoCreatedAt time.Time `json:"repo_created_at"`
	PushedAt      time.Time `json:"pushed_at"`
	IntegrationID int       `json:"integration_id"`
}
