package backspacesb

import (
	"fmt"
	"sync"

	"backspace/backspacesb/tables"
)

// Cache handles in-memory caching of database entities
type Cache struct {
	scans []tables.Scan
	mu    sync.RWMutex
}

// NewCache creates a new cache instance
func NewCache() *Cache {
	return &Cache{
		scans: make([]tables.Scan, 0),
	}
}

// SetScans replaces all cached scans
func (c *Cache) SetScans(scans []tables.Scan) {
	c.mu.Lock()
	defer c.mu.Unlock()
	c.scans = make([]tables.Scan, len(scans))
	copy(c.scans, scans)
}

// GetScans returns a copy of all cached scans
func (c *Cache) GetScans() []tables.Scan {
	c.mu.RLock()
	defer c.mu.RUnlock()
	
	scansCopy := make([]tables.Scan, len(c.scans))
	copy(scansCopy, c.scans)
	return scansCopy
}

// GetScansByBranch returns cached scans filtered by branch name
func (c *Cache) GetScansByBranch(branch string) []tables.Scan {
	c.mu.RLock()
	defer c.mu.RUnlock()
	
	var branchScans []tables.Scan
	for _, scan := range c.scans {
		if scan.Branch == branch {
			branchScans = append(branchScans, scan)
		}
	}
	return branchScans
}

// GetScanByID returns a specific scan from cache by ID
func (c *Cache) GetScanByID(id int) (*tables.Scan, error) {
	c.mu.RLock()
	defer c.mu.RUnlock()
	
	for _, scan := range c.scans {
		if scan.ID == id {
			scanCopy := scan
			return &scanCopy, nil
		}
	}
	return nil, fmt.Errorf("scan with ID %d not found in cache", id)
}

// GetScanSummaries returns a slice of ScanSummary with just ID and branch name
func (c *Cache) GetScanSummaries() []ScanSummary {
	c.mu.RLock()
	defer c.mu.RUnlock()
	
	summaries := make([]ScanSummary, len(c.scans))
	for i, scan := range c.scans {
		summaries[i] = ScanSummary{
			ID:     scan.ID,
			Branch: scan.Branch,
		}
	}
	return summaries
}

// Clear removes all cached data
func (c *Cache) Clear() {
	c.mu.Lock()
	defer c.mu.Unlock()
	c.scans = c.scans[:0]
}

// Count returns the number of cached scans
func (c *Cache) Count() int {
	c.mu.RLock()
	defer c.mu.RUnlock()
	return len(c.scans)
}