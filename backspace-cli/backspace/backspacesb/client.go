package backspacesb

import (
	"fmt"
	"log"
	"sync"

	"backspace/backspacesb/tables"

	"github.com/supabase-community/supabase-go"
)

var (
	once          sync.Once
	defaultClient *BackspaceClient
)

// ScanSummary holds just the essential scan information
type ScanSummary struct {
	ID     int    `json:"id"`
	Branch string `json:"branch"`
}

// BackspaceClient is the main client that orchestrates all services
type BackspaceClient struct {
	*supabase.Client
	Repository *tables.Repository
	
	// Services
	repositoryService *RepositoryService
	scanService       *ScanService
	
	// Cache
	cache *Cache
}

// GetClient returns the singleton client instance
func GetClient() *BackspaceClient {
	once.Do(func() {
		config, err := LoadConfig()
		if err != nil {
			log.Fatalf("Error loading configuration: %v", err)
		}

		sbClient, err := supabase.NewClient(config.SupabaseURL, config.SupabaseKey, &supabase.ClientOptions{})
		if err != nil {
			log.Fatalf("Error creating Supabase client: %v", err)
		}

		defaultClient = &BackspaceClient{
			Client:            sbClient,
			repositoryService: NewRepositoryService(sbClient),
			scanService:       NewScanService(sbClient),
			cache:             NewCache(),
		}

		// Load repository and cache
		defaultClient.Repository = defaultClient.repositoryService.GetCurrentRepositoryWithWarnings()
		if defaultClient.Repository != nil {
			if err := defaultClient.LoadScansCache(); err != nil {
				log.Printf("Warning: Failed to load scans cache: %v", err)
			}
		}
	})

	return defaultClient
}

// Database operations (delegates to services)

// GetScansByRepoID retrieves scans from database by repository ID
func (c *BackspaceClient) GetScansByRepoID(repoID int) ([]tables.Scan, error) {
	return c.scanService.GetScansByRepoID(repoID)
}

// GetScansByBranch retrieves scans from database by branch
func (c *BackspaceClient) GetScansByBranch(branch string) ([]tables.Scan, error) {
	if c.Repository == nil {
		return nil, fmt.Errorf("no repository found for current directory")
	}
	return c.scanService.GetScansByBranch(c.Repository.ID, branch)
}

// GetCurrentRepoScans retrieves all scans for the current repository
func (c *BackspaceClient) GetCurrentRepoScans() ([]tables.Scan, error) {
	if c.Repository == nil {
		return nil, fmt.Errorf("no repository found for current directory")
	}
	return c.scanService.GetScansByRepoID(c.Repository.ID)
}

// Cache operations (delegates to cache)

// LoadScansCache loads all scans for the current repository into cache
func (c *BackspaceClient) LoadScansCache() error {
	if c.Repository == nil {
		return fmt.Errorf("no repository found for current directory")
	}

	scans, err := c.scanService.GetScansByRepoID(c.Repository.ID)
	if err != nil {
		return fmt.Errorf("failed to load scans: %w", err)
	}

	c.cache.SetScans(scans)
	return nil
}

// GetCachedScans returns all cached scans
func (c *BackspaceClient) GetCachedScans() []tables.Scan {
	return c.cache.GetScans()
}

// GetCachedScansByBranch returns cached scans filtered by branch
func (c *BackspaceClient) GetCachedScansByBranch(branch string) []tables.Scan {
	return c.cache.GetScansByBranch(branch)
}

// GetScanSummaries returns scan summaries with just ID and branch
func (c *BackspaceClient) GetScanSummaries() []ScanSummary {
	return c.cache.GetScanSummaries()
}

// GetScanByID returns a specific scan from cache by ID
func (c *BackspaceClient) GetScanByID(id int) (*tables.Scan, error) {
	return c.cache.GetScanByID(id)
}

// RefreshCache reloads the cache from database
func (c *BackspaceClient) RefreshCache() error {
	return c.LoadScansCache()
}

// ClearCache clears all cached data
func (c *BackspaceClient) ClearCache() {
	c.cache.Clear()
}

// Branch cross-reference operations

// BranchScanInfo holds scan information for a specific branch
type BranchScanInfo struct {
	Branch      string    `json:"branch"`
	HasScans    bool      `json:"has_scans"`
	ScanCount   int       `json:"scan_count"`
	LatestScan  *tables.Scan `json:"latest_scan,omitempty"`
}

// GetBranchScanInfo returns scan information for a list of local branches
func (c *BackspaceClient) GetBranchScanInfo(localBranches []string) []BranchScanInfo {
	result := make([]BranchScanInfo, len(localBranches))
	
	for i, branch := range localBranches {
		branchScans := c.cache.GetScansByBranch(branch)
		info := BranchScanInfo{
			Branch:    branch,
			HasScans:  len(branchScans) > 0,
			ScanCount: len(branchScans),
		}
		
		// Find the latest scan for this branch
		if len(branchScans) > 0 {
			latest := &branchScans[0]
			for j := 1; j < len(branchScans); j++ {
				if branchScans[j].CreatedAt.After(latest.CreatedAt) {
					latest = &branchScans[j]
				}
			}
			info.LatestScan = latest
		}
		
		result[i] = info
	}
	
	return result
}

// HasScansForBranch quickly checks if a branch has any scans
func (c *BackspaceClient) HasScansForBranch(branch string) bool {
	return len(c.cache.GetScansByBranch(branch)) > 0
}

// GetLatestScanForBranch returns the most recent scan for a branch
func (c *BackspaceClient) GetLatestScanForBranch(branch string) *tables.Scan {
	branchScans := c.cache.GetScansByBranch(branch)
	if len(branchScans) == 0 {
		return nil
	}
	
	latest := &branchScans[0]
	for i := 1; i < len(branchScans); i++ {
		if branchScans[i].CreatedAt.After(latest.CreatedAt) {
			latest = &branchScans[i]
		}
	}
	return latest
}
