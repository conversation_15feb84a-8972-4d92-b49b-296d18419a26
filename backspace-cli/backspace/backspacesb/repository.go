package backspacesb

import (
	"encoding/json"
	"fmt"
	"log"

	"backspace/backspacesb/tables"
	"backspace/model/utils"

	"github.com/supabase-community/supabase-go"
)

// RepositoryService handles repository-related operations
type RepositoryService struct {
	client *supabase.Client
}

// NewRepositoryService creates a new repository service
func NewRepositoryService(client *supabase.Client) *RepositoryService {
	return &RepositoryService{
		client: client,
	}
}

// GetCurrentRepository gets the repository for the current git directory
func (r *RepositoryService) GetCurrentRepository() (*tables.Repository, error) {
	repoURL, err := utils.GetRepoURL()
	if err != nil {
		return nil, fmt.Errorf("could not detect git repository: %w", err)
	}

	var repos []tables.Repository
	result, _, err := r.client.From("repositories").
		Select("*", "", false).
		Eq("url", repoURL).
		Execute()
	if err != nil {
		return nil, fmt.Errorf("failed to find repository in database: %w", err)
	}

	if err := json.Unmarshal(result, &repos); err != nil {
		return nil, fmt.Errorf("failed to unmarshal repository: %w", err)
	}

	if len(repos) == 0 {
		return nil, fmt.Errorf("repository not found in database")
	}

	return &repos[0], nil
}

// GetCurrentRepositoryWithWarnings gets the repository with warning logs instead of errors
func (r *RepositoryService) GetCurrentRepositoryWithWarnings() *tables.Repository {
	repo, err := r.GetCurrentRepository()
	if err != nil {
		log.Printf("Warning: %v", err)
		return nil
	}
	return repo
}