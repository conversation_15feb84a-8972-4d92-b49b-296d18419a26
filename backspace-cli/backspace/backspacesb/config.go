package backspacesb

import (
	"log"
	"os"

	"github.com/joho/godotenv"
)

// Config holds database configuration
type Config struct {
	SupabaseURL string
	SupabaseKey string
}

// LoadConfig loads configuration from environment variables
func LoadConfig() (*Config, error) {
	err := godotenv.Load()
	if err != nil {
		return nil, err
	}

	config := &Config{
		SupabaseURL: os.Getenv("SUPABASE_URL"),
		SupabaseKey: os.Getenv("SUPABASE_KEY"),
	}

	if config.SupabaseURL == "" || config.SupabaseKey == "" {
		log.Fatal("SUPABASE_URL and SUPABASE_KEY environment variables are required")
	}

	return config, nil
}