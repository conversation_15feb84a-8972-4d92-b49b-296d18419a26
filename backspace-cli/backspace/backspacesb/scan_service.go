package backspacesb

import (
	"encoding/json"
	"fmt"

	"backspace/backspacesb/tables"

	"github.com/supabase-community/supabase-go"
)

// ScanService handles scan-related database operations
type ScanService struct {
	client *supabase.Client
}

// NewScanService creates a new scan service
func NewScanService(client *supabase.Client) *ScanService {
	return &ScanService{
		client: client,
	}
}

// GetScansByRepoID retrieves all scans for a given repository ID
func (s *ScanService) GetScansByRepoID(repoID int) ([]tables.Scan, error) {
	var scans []tables.Scan

	result, _, err := s.client.From("scans").
		Select("*", "", false).
		Eq("repo_id", fmt.Sprintf("%d", repoID)).
		Execute()
	if err != nil {
		return nil, fmt.Errorf("failed to get scans: %w", err)
	}

	if err := json.Unmarshal(result, &scans); err != nil {
		return nil, fmt.Errorf("failed to unmarshal scans: %w", err)
	}

	return scans, nil
}

// GetScansByBranch retrieves scans for a specific branch and repository
func (s *ScanService) GetScansByBranch(repoID int, branch string) ([]tables.Scan, error) {
	var scans []tables.Scan

	result, _, err := s.client.From("scans").
		Select("*", "", false).
		Eq("repo_id", fmt.Sprintf("%d", repoID)).
		Eq("branch", branch).
		Execute()
	if err != nil {
		return nil, fmt.Errorf("failed to get scans: %w", err)
	}

	if err := json.Unmarshal(result, &scans); err != nil {
		return nil, fmt.Errorf("failed to unmarshal scans: %w", err)
	}

	return scans, nil
}

// GetScanByID retrieves a specific scan by ID
func (s *ScanService) GetScanByID(id int) (*tables.Scan, error) {
	var scans []tables.Scan

	result, _, err := s.client.From("scans").
		Select("*", "", false).
		Eq("id", fmt.Sprintf("%d", id)).
		Execute()
	if err != nil {
		return nil, fmt.Errorf("failed to get scan: %w", err)
	}

	if err := json.Unmarshal(result, &scans); err != nil {
		return nil, fmt.Errorf("failed to unmarshal scan: %w", err)
	}

	if len(scans) == 0 {
		return nil, fmt.Errorf("scan with ID %d not found", id)
	}

	return &scans[0], nil
}