package main

import (
	"backspace/model"
	"backspace/model/utils"
	"fmt"
	"log"
	"os"

	tea "github.com/charmbracelet/bubbletea"
)

func main() {
	// Check if we're in a git repository
	_, _, _, err := utils.GetRepoInfo()
	if err != nil {
		fmt.Println("Error: Backspace CLI must be run inside a git repository")
		os.Exit(1)
	}

	// Get branches

	m := model.New()

	p := tea.NewProgram(&m, tea.WithAltScreen(), tea.WithMouseCellMotion())
	if _, err := p.Run(); err != nil {
		log.Fatal(err)
	}
}
