package model

import (
	"fmt"
	"strings"

	"backspace/model/utils"

	"github.com/charmbracelet/lipgloss"
)

const sidebarWidth = 35

var (
	// Color palette
	primaryColor    = lipgloss.Color("99")  // Blue
	selectedColor   = lipgloss.Color("42")  // Green
	unselectedColor = lipgloss.Color("240") // Gray
	accentColor     = lipgloss.Color("205") // Pink
	textColor       = lipgloss.Color("252") // Light gray
	dimTextColor    = lipgloss.Color("244") // Dimmer gray

	// Sidebar styles
	sidebarStyle = lipgloss.NewStyle().
			Border(lipgloss.RoundedBorder()).
			BorderForeground(primaryColor).
			Padding(1, 1)

	sidebarTitleStyle = lipgloss.NewStyle().
				Foreground(primaryColor).
				Bold(true).
				MarginBottom(1).
				Align(lipgloss.Center)

	// Branch card styles
	branchCardStyle = lipgloss.NewStyle().
			Border(lipgloss.RoundedBorder()).
			BorderForeground(unselectedColor).
			Padding(0, 1).
			MarginBottom(1).
			Width(sidebarWidth - 4)

	selectedBranchCardStyle = branchCardStyle.
				BorderForeground(selectedColor)

	branchNameStyle = lipgloss.NewStyle().
			Bold(true).
			Foreground(textColor)

	selectedBranchNameStyle = lipgloss.NewStyle().
				Bold(true).
				Foreground(lipgloss.Color("255"))

	branchInfoStyle = lipgloss.NewStyle().
			Foreground(dimTextColor)

	selectedBranchInfoStyle = lipgloss.NewStyle().
				Foreground(lipgloss.Color("250"))

	// Pagination styles
	paginationStyle = lipgloss.NewStyle().
			Foreground(dimTextColor).
			Align(lipgloss.Center).
			MarginTop(1)

	// Button styles
	buttonStyle = lipgloss.NewStyle().
			Padding(0, 2).
			Border(lipgloss.RoundedBorder()).
			BorderForeground(unselectedColor)

	activeButtonStyle = buttonStyle.
				BorderForeground(primaryColor).
				Foreground(primaryColor)

	hoveredButtonStyle = buttonStyle.
				BorderForeground(selectedColor).
				Foreground(selectedColor).
				Background(lipgloss.Color("236"))

	// Simple tab styles
	tabStyle = lipgloss.NewStyle().
			Padding(0, 2).
			Foreground(dimTextColor).
			Background(lipgloss.Color("236")).
			MarginRight(1)

	selectedTabStyle = lipgloss.NewStyle().
				Padding(0, 2).
				Foreground(primaryColor).
				Background(lipgloss.Color("255")).
				Bold(true).
				MarginRight(1)

	hoveredTabStyle = lipgloss.NewStyle().
			Padding(0, 2).
			Foreground(selectedColor).
			Background(lipgloss.Color("240")).
			MarginRight(1)

	// Main content styles
	mainContentStyle = lipgloss.NewStyle().
				Border(lipgloss.RoundedBorder()).
				BorderForeground(primaryColor).
				Padding(1, 2)

	mainContentWithTabsStyle = lipgloss.NewStyle().
					Border(lipgloss.RoundedBorder()).
					BorderForeground(primaryColor).
					Padding(1, 2)
)

func (m Model) View() string {
	if m.width == 0 || m.height == 0 {
		return "Loading..."
	}

	sidebar := m.renderSidebar()
	mainContent := m.renderMainContentWithTabs()

	return m.zone.Scan(lipgloss.JoinHorizontal(
		lipgloss.Top,
		sidebar,
		mainContent,
	))
}

func (m Model) renderSidebar() string {
	// Calculate available height for content
	// availableHeight := m.height - 7 // Account for borders, title, and pagination

	// Render title
	title := sidebarTitleStyle.Width(sidebarWidth - 4).Render("🌿 Branches")

	// Render branch list
	branches := m.renderBranchList()

	// Render pagination
	pagination := m.renderPagination()

	// Combine all parts
	content := lipgloss.JoinVertical(
		lipgloss.Left,
		title,
		branches,
		pagination,
	)

	// Apply sidebar style with calculated height
	return sidebarStyle.
		Width(sidebarWidth).
		Height(m.height - 2).
		Render(content)
}

func (m Model) renderBranchList() string {
	if len(m.Branches) == 0 {
		return lipgloss.NewStyle().
			Foreground(dimTextColor).
			Italic(true).
			Render("No branches found")
	}

	// Calculate which branches to show
	start := m.CurrentPage * m.BranchesPerPage
	end := start + m.BranchesPerPage
	if end > len(m.Branches) {
		end = len(m.Branches)
	}

	var cards []string
	for i := start; i < end; i++ {
		card := m.renderBranchCard(i)
		cards = append(cards, card)
	}

	return lipgloss.JoinVertical(lipgloss.Left, cards...)
}

func (m Model) renderBranchCard(index int) string {
	branch := m.Branches[index]
	isSelected := index == m.SelectedBranch

	// Choose styles based on selection and hover
	cardStyle := branchCardStyle
	nameStyle := branchNameStyle
	infoStyle := branchInfoStyle
	contentBgStyle := lipgloss.NewStyle()
	isHovered := m.HoveredZone == branchZoneID(index)

	if isSelected {
		cardStyle = selectedBranchCardStyle
		nameStyle = selectedBranchNameStyle
		infoStyle = selectedBranchInfoStyle
		contentBgStyle = lipgloss.NewStyle().
			Background(lipgloss.Color("235")).
			Width(sidebarWidth - 6) // Account for padding and borders
	} else if isHovered {
		cardStyle = branchCardStyle.BorderForeground(lipgloss.Color("245"))
		contentBgStyle = lipgloss.NewStyle().
			Background(lipgloss.Color("237")).
			Width(sidebarWidth - 6)
	}

	// Format branch name (truncate if too long)
	branchName := branch
	maxLen := sidebarWidth - 8
	if len(branchName) > maxLen {
		branchName = branchName[:maxLen-3] + "..."
	}

	// Get real branch status
	var statusIcon string
	var statusText string

	if utils.IsMainBranch(branch) {
		statusIcon = "🌟"
		statusText = "main branch"
	} else if status, exists := m.BranchStatuses[branch]; exists {
		statusText = utils.FormatBranchStatus(status)
		if isSelected {
			statusIcon = "✓"
		} else if status.MainComparison {
			// Use main comparison for icons
			if status.AheadOfMain > 0 || status.BehindMain > 0 {
				statusIcon = "[!]" // Warning for branches diverged from main
			} else {
				statusIcon = "✓" // Check mark for up-to-date with main
			}
		} else if status.HasUpstream {
			// Fallback to upstream comparison
			if status.Ahead > 0 || status.Behind > 0 {
				statusIcon = "[!]" // Warning for diverged branches
			} else {
				statusIcon = "✓" // Check mark for up-to-date
			}
		} else {
			statusIcon = "📤" // Outbox for no upstream
		}
	} else {
		// Fallback if status not available
		statusIcon = "○"
		statusText = "status unknown"
	}

	// Build the card content with proper background
	nameRow := statusIcon + " " + nameStyle.Render(branchName)
	infoRow := infoStyle.Render(statusText)

	// Apply background to each row if selected or hovered
	if isSelected || isHovered {
		nameRow = contentBgStyle.Render(nameRow)
		infoRow = contentBgStyle.Render(infoRow)
	}

	content := lipgloss.JoinVertical(
		lipgloss.Left,
		nameRow,
		infoRow,
	)

	// Wrap in zone for click detection
	card := cardStyle.Render(content)
	return m.zone.Mark(branchZoneID(index), card)
}

func (m Model) renderPagination() string {
	if len(m.Branches) <= m.BranchesPerPage {
		return ""
	}

	totalPages := (len(m.Branches)-1)/m.BranchesPerPage + 1
	currentPage := m.CurrentPage + 1

	// Create prev/next buttons with hover effects
	var prevButton, nextButton string

	if m.CurrentPage > 0 {
		prevStyle := activeButtonStyle
		if m.HoveredZone == "prev-page" {
			prevStyle = hoveredButtonStyle
		}
		prevButton = m.zone.Mark("prev-page", prevStyle.Render("◀ Prev"))
	} else {
		prevButton = buttonStyle.Foreground(dimTextColor).Render("◀ Prev")
	}

	if m.CurrentPage < totalPages-1 {
		nextStyle := activeButtonStyle
		if m.HoveredZone == "next-page" {
			nextStyle = hoveredButtonStyle
		}
		nextButton = m.zone.Mark("next-page", nextStyle.Render("Next ▶"))
	} else {
		nextButton = buttonStyle.Foreground(dimTextColor).Render("Next ▶")
	}

	// Create page indicators
	var indicators []string
	for i := 0; i < totalPages; i++ {
		if i == m.CurrentPage {
			indicators = append(indicators, "●")
		} else {
			indicators = append(indicators, "○")
		}
	}

	// Page info
	pageInfo := fmt.Sprintf("Page %d/%d\n%s",
		currentPage,
		totalPages,
		strings.Join(indicators, " "),
	)

	// Combine buttons and info
	buttons := lipgloss.JoinHorizontal(
		lipgloss.Center,
		prevButton,
		"  ",
		nextButton,
	)

	return lipgloss.JoinVertical(
		lipgloss.Center,
		paginationStyle.Width(sidebarWidth-4).Render(pageInfo),
		lipgloss.PlaceHorizontal(sidebarWidth-4, lipgloss.Center, buttons),
	)
}

func (m Model) renderTabs() string {
	// Tab titles
	tabs := []struct {
		name    string
		tabType TabType
	}{
		{"📊 Scans", ScanTab},
		{"🐛 Issues", IssueTab},
	}

	var renderedTabs []string
	for _, tab := range tabs {
		isSelected := m.SelectedTab == tab.tabType
		isHovered := m.HoveredZone == fmt.Sprintf("tab-%d", int(tab.tabType))

		var style lipgloss.Style
		if isSelected {
			style = selectedTabStyle
		} else if isHovered {
			style = hoveredTabStyle
		} else {
			style = tabStyle
		}

		renderedTab := style.Render(tab.name)
		zonedTab := m.zone.Mark(fmt.Sprintf("tab-%d", int(tab.tabType)), renderedTab)
		renderedTabs = append(renderedTabs, zonedTab)
	}

	// Join tabs horizontally
	return lipgloss.JoinHorizontal(lipgloss.Bottom, renderedTabs...)
}

func (m Model) renderTabContent() string {
	if m.SelectedBranch >= len(m.Branches) || m.SelectedBranch < 0 {
		return "Select a branch to view details"
	}

	selectedBranch := m.Branches[m.SelectedBranch]

	switch m.SelectedTab {
	case ScanTab:
		return m.renderScanContent(selectedBranch)
	case IssueTab:
		return m.renderIssueContent(selectedBranch)
	default:
		return "Unknown tab"
	}
}

func (m Model) renderScanContent(branchName string) string {
	headerStyle := lipgloss.NewStyle().
		Foreground(primaryColor).
		Bold(true).
		MarginBottom(1)

	header := headerStyle.Render(fmt.Sprintf("📊 Scans for %s", branchName))

	infoStyle := lipgloss.NewStyle().
		Foreground(textColor)

	info := infoStyle.Render("Here you'll see scan results for this branch.\n\n• Code quality scans\n• Security scans\n• Performance analysis")

	return lipgloss.JoinVertical(
		lipgloss.Left,
		header,
		info,
	)
}

func (m Model) renderIssueContent(branchName string) string {
	headerStyle := lipgloss.NewStyle().
		Foreground(primaryColor).
		Bold(true).
		MarginBottom(1)

	header := headerStyle.Render(fmt.Sprintf("🐛 Issues for %s", branchName))

	infoStyle := lipgloss.NewStyle().
		Foreground(textColor)

	info := infoStyle.Render("Here you'll see issues found in this branch.\n\n• Code issues\n• Security vulnerabilities\n• Performance problems")

	return lipgloss.JoinVertical(
		lipgloss.Left,
		header,
		info,
	)
}

func (m Model) renderMainContentWithTabs() string {
	// Calculate dimensions
	mainWidth := m.width - sidebarWidth - 4
	mainHeight := m.height - 2

	// Render tabs at the top
	tabs := m.renderTabs()

	// Add some spacing
	spacer := lipgloss.NewStyle().Height(1).Render("")

	// Render tab content
	content := m.renderTabContent()

	// Combine everything
	fullContent := lipgloss.JoinVertical(
		lipgloss.Left,
		tabs,
		spacer,
		content,
	)

	return mainContentWithTabsStyle.
		Width(mainWidth).
		Height(mainHeight).
		Render(fullContent)
}
