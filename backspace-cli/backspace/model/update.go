package model

import (
	"fmt"
	
	tea "github.com/charmbracelet/bubbletea"
)

func (m *Model) Update(msg tea.Msg) (tea.Model, tea.Cmd) {
	switch msg := msg.(type) {
	case tea.KeyMsg:
		switch msg.String() {
		case "q", "ctrl+c":
			return m, tea.Quit
		case "up", "k":
			// Navigate within current page only
			start := m.CurrentPage * m.BranchesPerPage
			if m.SelectedBranch > start {
				m.SelectedBranch--
			}
		case "down", "j":
			// Navigate within current page only
			start := m.CurrentPage * m.BranchesPerPage
			end := start + m.BranchesPerPage
			if end > len(m.Branches) {
				end = len(m.Branches)
			}
			if m.SelectedBranch < end-1 {
				m.SelectedBranch++
			}
		}
	case tea.WindowSizeMsg:
		m.width = msg.Width
		m.height = msg.Height
		// Adjust branches per page based on height
		m.BranchesPerPage = (m.height - 10) / 5 // 5 lines per branch card, leave room for header/footer
		if m.BranchesPerPage < 1 {
			m.BranchesPerPage = 1
		}
	case tea.MouseMsg:
		switch msg.Action {
		case tea.MouseActionMotion:
			// Track hover state
			m.HoveredZone = ""
			
			// Check tab hovers
			if m.zone.Get("tab-0").InBounds(msg) {
				m.HoveredZone = "tab-0"
			} else if m.zone.Get("tab-1").InBounds(msg) {
				m.HoveredZone = "tab-1"
			} else if m.zone.Get("prev-page").InBounds(msg) {
				m.HoveredZone = "prev-page"
			} else if m.zone.Get("next-page").InBounds(msg) {
				m.HoveredZone = "next-page"
			}
			
			// Check branch card hovers
			start := m.CurrentPage * m.BranchesPerPage
			end := start + m.BranchesPerPage
			if end > len(m.Branches) {
				end = len(m.Branches)
			}
			for i := start; i < end; i++ {
				if m.zone.Get(branchZoneID(i)).InBounds(msg) {
					m.HoveredZone = branchZoneID(i)
					break
				}
			}
			
		case tea.MouseActionPress:
			// Handle tab clicks
			if m.zone.Get("tab-0").InBounds(msg) {
				m.SelectedTab = ScanTab
			} else if m.zone.Get("tab-1").InBounds(msg) {
				m.SelectedTab = IssueTab
			}
			
			// Handle branch card clicks
			for i := range m.Branches {
				if m.zone.Get(branchZoneID(i)).InBounds(msg) {
					m.SelectedBranch = i
					break
				}
			}
			
			// Handle pagination button clicks
			if m.zone.Get("prev-page").InBounds(msg) && m.CurrentPage > 0 {
				m.CurrentPage--
				m.SelectedBranch = m.CurrentPage * m.BranchesPerPage
			}
			
			if m.zone.Get("next-page").InBounds(msg) {
				totalPages := (len(m.Branches)-1)/m.BranchesPerPage + 1
				if m.CurrentPage < totalPages-1 {
					m.CurrentPage++
					m.SelectedBranch = m.CurrentPage * m.BranchesPerPage
				}
			}
		}
	}
	return m, nil
}

func branchZoneID(index int) string {
	return fmt.Sprintf("branch-%d", index)
}
