package model

import (
	"backspace/model/utils"

	tea "github.com/charmbracelet/bubbletea"
	zone "github.com/lrstanley/bubblezone"
)

type TabType int

const (
	ScanTab TabType = iota
	IssueTab
)

type Model struct {
	Branches        []string
	BranchStatuses  map[string]*utils.BranchStatus
	SelectedBranch  int
	CurrentPage     int
	BranchesPerPage int
	SelectedTab     TabType
	width           int
	height          int
	zone            *zone.Manager
	HoveredZone     string // Track which zone is hovered
}

func New() Model {
	return Model{
		SelectedBranch:  0,
		CurrentPage:     0,
		BranchesPerPage: 5,
		SelectedTab:     ScanTab,
		zone:            zone.New(),
	}
}

func (m *Model) Init() tea.Cmd {
	branches, err := utils.GetLocalBranches()
	if err != nil {
		return nil
	}
	m.Branches = branches
	
	// Load branch statuses
	statuses, err := utils.GetAllBranchStatuses()
	if err != nil {
		// Continue without statuses if there's an error
		m.BranchStatuses = make(map[string]*utils.BranchStatus)
	} else {
		m.BranchStatuses = statuses
	}
	
	return nil
}
