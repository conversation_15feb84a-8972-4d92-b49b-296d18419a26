package utils

import (
	"fmt"
	"os/exec"
	"strconv"
	"strings"
)

func GetCurrentBranch() (string, error) {
	cmd := exec.Command("git", "rev-parse", "--abbrev-ref", "HEAD")
	output, err := cmd.Output()
	if err != nil {
		return "", fmt.Errorf("failed to get current branch: %w", err)
	}

	branch := strings.TrimSpace(string(output))
	return branch, nil
}

func GetLocalBranches() ([]string, error) {
	cmd := exec.Command("git", "branch", "--list")
	output, err := cmd.Output()
	if err != nil {
		return nil, fmt.Errorf("failed to get local branches: %w", err)
	}

	branches := strings.Split(strings.TrimSpace(string(output)), "\n")
	var filteredBranches []string
	for _, branch := range branches {
		// Remove the * prefix and trim spaces
		branch = strings.TrimSpace(strings.TrimPrefix(branch, "*"))
		filteredBranches = append(filteredBranches, branch)
	}
	return filteredBranches, nil
}

func GetRepoURL() (string, error) {
	cmd := exec.Command("git", "config", "--get", "remote.origin.url")
	output, err := cmd.Output()
	if err != nil {
		return "", fmt.Errorf("failed to get repo URL: %w", err)
	}

	url := strings.TrimSpace(string(output))
	return url, nil
}

func GetRepoName() (string, error) {
	url, err := GetRepoURL()
	if err != nil {
		return "", err
	}

	parts := strings.Split(url, "/")
	if len(parts) < 2 {
		return "", fmt.Errorf("invalid repo URL format")
	}

	repoName := parts[len(parts)-1]
	repoName = strings.TrimSuffix(repoName, ".git")

	return repoName, nil
}

// Returns current branch, repo URL, and repo name
func GetRepoInfo() (branch string, repoURL string, repoName string, err error) {
	branch, err = GetCurrentBranch()
	if err != nil {
		return "", "", "", err
	}

	repoURL, err = GetRepoURL()
	if err != nil {
		return branch, "", "", err
	}

	repoName, err = GetRepoName()
	if err != nil {
		return branch, repoURL, "", err
	}

	return branch, repoURL, repoName, nil
}

// BranchStatus represents the status of a branch relative to remote main
type BranchStatus struct {
	Name            string
	Upstream        string
	Ahead           int
	Behind          int
	HasUpstream     bool
	AheadOfMain     int  // Commits ahead of remote main
	BehindMain      int  // Commits behind remote main
	MainComparison  bool // Whether main comparison was successful
}

// GetRemoteMainBranch finds the remote main branch (origin/main or origin/master)
func GetRemoteMainBranch() (string, error) {
	// Try origin/main first
	cmd := exec.Command("git", "rev-parse", "--verify", "origin/main")
	if err := cmd.Run(); err == nil {
		return "origin/main", nil
	}
	
	// Try origin/master
	cmd = exec.Command("git", "rev-parse", "--verify", "origin/master")
	if err := cmd.Run(); err == nil {
		return "origin/master", nil
	}
	
	return "", fmt.Errorf("neither origin/main nor origin/master found")
}

// GetBranchStatus returns the ahead/behind status for a specific branch
func GetBranchStatus(branchName string) (*BranchStatus, error) {
	status := &BranchStatus{
		Name: branchName,
	}

	// Get upstream branch info (for reference)
	cmd := exec.Command("git", "rev-parse", "--abbrev-ref", branchName+"@{upstream}")
	output, err := cmd.Output()
	if err == nil {
		upstream := strings.TrimSpace(string(output))
		status.Upstream = upstream
		status.HasUpstream = true
		
		// Get ahead/behind count relative to upstream
		cmd = exec.Command("git", "rev-list", "--left-right", "--count", upstream+"..."+branchName)
		output, err = cmd.Output()
		if err == nil {
			counts := strings.Fields(strings.TrimSpace(string(output)))
			if len(counts) == 2 {
				if behind, err := strconv.Atoi(counts[0]); err == nil {
					status.Behind = behind
				}
				if ahead, err := strconv.Atoi(counts[1]); err == nil {
					status.Ahead = ahead
				}
			}
		}
	}

	// Get ahead/behind count relative to remote main
	remoteMain, err := GetRemoteMainBranch()
	if err == nil {
		cmd = exec.Command("git", "rev-list", "--left-right", "--count", remoteMain+"..."+branchName)
		output, err = cmd.Output()
		if err == nil {
			status.MainComparison = true
			counts := strings.Fields(strings.TrimSpace(string(output)))
			if len(counts) == 2 {
				if behind, err := strconv.Atoi(counts[0]); err == nil {
					status.BehindMain = behind
				}
				if ahead, err := strconv.Atoi(counts[1]); err == nil {
					status.AheadOfMain = ahead
				}
			}
		}
	}

	return status, nil
}

// GetAllBranchStatuses returns status for all local branches
func GetAllBranchStatuses() (map[string]*BranchStatus, error) {
	branches, err := GetLocalBranches()
	if err != nil {
		return nil, err
	}

	statuses := make(map[string]*BranchStatus)
	for _, branch := range branches {
		status, err := GetBranchStatus(branch)
		if err != nil {
			// Skip branches with errors, continue with others
			continue
		}
		statuses[branch] = status
	}

	return statuses, nil
}

// FormatBranchStatus returns a human-readable status string relative to remote main
func FormatBranchStatus(status *BranchStatus) string {
	// Use main comparison if available
	if status.MainComparison {
		if status.AheadOfMain == 0 && status.BehindMain == 0 {
			return "up to date with main"
		}
		
		var parts []string
		if status.AheadOfMain > 0 {
			parts = append(parts, fmt.Sprintf("%d ahead of main", status.AheadOfMain))
		}
		if status.BehindMain > 0 {
			parts = append(parts, fmt.Sprintf("%d behind main", status.BehindMain))
		}
		
		return strings.Join(parts, ", ")
	}
	
	// Fallback to upstream comparison
	if !status.HasUpstream {
		return "no upstream"
	}

	if status.Ahead == 0 && status.Behind == 0 {
		return "up to date with upstream"
	}

	var parts []string
	if status.Ahead > 0 {
		parts = append(parts, fmt.Sprintf("%d ahead", status.Ahead))
	}
	if status.Behind > 0 {
		parts = append(parts, fmt.Sprintf("%d behind", status.Behind))
	}

	return strings.Join(parts, ", ")
}

// IsMainBranch checks if a branch is a main branch (main, master, develop)
func IsMainBranch(branchName string) bool {
	mainBranches := []string{"main", "master", "develop", "dev"}
	for _, main := range mainBranches {
		if branchName == main {
			return true
		}
	}
	return false
}
