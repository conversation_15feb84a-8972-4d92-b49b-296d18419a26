package utils

import (
	"fmt"
	"testing"
)

func TestGetCurrentBranch(t *testing.T) {
	// This is a basic test that just checks if we can get the branch name
	// without errors
	branch, url, name, err := GetRepoInfo()
	if err != nil {
		t.<PERSON><PERSON><PERSON>("GetCurrentBranch() error = %v", err)
	}
	if branch == "" {
		t.<PERSON><PERSON>("GetCurrentBranch() returned empty branch name")
	}
	fmt.Printf("Branch: %s\nURL: %s\nName: %s\n", branch, url, name)
}
