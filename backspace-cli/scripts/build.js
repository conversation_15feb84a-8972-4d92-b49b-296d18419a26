const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Parse command line arguments
const args = process.argv.slice(2);
const platformIndex = args.findIndex(arg => arg === '--platform');
const targetPlatform = platformIndex !== -1 ? args[platformIndex + 1] : null;

// Ensure bin directory exists
const binDir = path.join(__dirname, '..', 'bin');
if (!fs.existsSync(binDir)) {
    fs.mkdirSync(binDir);
}

// Define all possible builds
const allBuilds = [
    { os: 'darwin', arch: 'amd64', output: 'backspace-darwin-x64' },
    { os: 'darwin', arch: 'arm64', output: 'backspace-darwin-arm64' },
    { os: 'linux', arch: 'amd64', output: 'backspace-linux-x64' },
    { os: 'linux', arch: 'arm64', output: 'backspace-linux-arm64' },
    { os: 'windows', arch: 'amd64', output: 'backspace-win32-x64.exe' },
];

// Filter builds based on platform argument
let builds = allBuilds;
if (targetPlatform) {
    builds = allBuilds.filter(build => build.os === targetPlatform);
    if (builds.length === 0) {
        console.error(`Invalid platform: ${targetPlatform}`);
        console.error('Valid platforms: darwin (macOS), linux, windows');
        process.exit(1);
    }
}

// Clean previous builds (only for the target platform if specified)
console.log('Cleaning previous builds...');
const files = fs.readdirSync(binDir).filter(f => {
    if (!f.startsWith('backspace-')) return false;
    if (!targetPlatform) return true;
    return builds.some(build => f === build.output);
});
files.forEach(f => fs.unlinkSync(path.join(binDir, f)));

builds.forEach(({ os, arch, output }) => {
    console.log(`Building for ${os}/${arch}...`);
    try {
        const goDir = path.join(__dirname, '..', 'backspace');
        const outFile = path.join(binDir, output);
        
        // Build from the module directory
        execSync(`go build -o ${outFile} .`, {
            stdio: 'inherit',
            cwd: goDir,
            env: { ...process.env, GOOS: os, GOARCH: arch }
        });
        console.log(`✓ Built ${output}`);
    } catch (error) {
        console.error(`✗ Failed to build ${output}:`, error.message);
    }
});

console.log('\nBuild complete!');