#!/usr/bin/env node

const { spawn } = require('child_process');
const path = require('path');
const os = require('os');

// Determine the binary name based on platform and architecture
function getBinaryName() {
  const platform = os.platform();
  const arch = os.arch();
  
  let binaryName = 'backspace-';
  
  if (platform === 'darwin') {
    binaryName += 'darwin-';
  } else if (platform === 'linux') {
    binaryName += 'linux-';
  } else if (platform === 'win32') {
    binaryName += 'win32-';
  } else {
    console.error(`Unsupported platform: ${platform}`);
    process.exit(1);
  }
  
  if (arch === 'x64') {
    binaryName += 'x64';
  } else if (arch === 'arm64') {
    binaryName += 'arm64';
  } else {
    console.error(`Unsupported architecture: ${arch}`);
    process.exit(1);
  }
  
  if (platform === 'win32') {
    binaryName += '.exe';
  }
  
  return binaryName;
}

const binaryPath = path.join(__dirname, getBinaryName());

// Get command line arguments (excluding node and script path)
const args = process.argv.slice(2);

// Spawn the binary
const proc = spawn(binaryPath, args, {
  stdio: 'inherit',
  shell: false
});

// Handle process exit
proc.on('exit', (code) => {
  process.exit(code);
});

// Handle errors
proc.on('error', (err) => {
  if (err.code === 'ENOENT') {
    console.error(`Error: Binary not found at ${binaryPath}`);
    console.error('Run "npm run build" to build the binaries first.');
  } else {
    console.error('Error spawning process:', err);
  }
  process.exit(1);
});