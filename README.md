<h1 align="center">Backspace-Turbo</h1>

<p align="center">
  <a href="#introduction"><strong>Introduction</strong></a> |   
  <a href="#features"><strong>Features</strong></a> |   
  <a href="#tech-stack"><strong>Tech Stack</strong></a> |   
  <a href="#getting-started"><strong>Getting started</strong></a> |   
  <a href="#contributing"><strong>Contributing</strong></a>
</p>
<p align="center">
  <a href="https://next-fast-turbo.mintlify.app/">
    <img src="https://img.shields.io/badge/Read%20the%20Documentation-8A2BE2" alt="Documentation" />
  </a>
</p>
<br/>

## Introduction

Backspace Turbo starts from an open-source project scaffold. It's designed to be easy to get up and running both locally and in production. It's a monorepo that includes a Next.js frontend, a FastAPI backend, and a fully built and annotated Mintlify documentation site. It's built with TypeScript, and includes ESLint and Prettier for code quality. It's also set up to use Vercel for deployments and Remote Caching.

View the live frontend at [next-fast-turbo-web.vercel.app](https://next-fast-turbo-web.vercel.app/) and the live backend at [next-fast-turbo-api.vercel.app](https://next-fast-turbo-api.vercel.app/).

## Features

- Pre-configured [FastAPI backend](https://next-fast-turbo.mintlify.app/documentation/configuration/fastapi)
- Pre-configured [Next.js frontend](https://next-fast-turbo.mintlify.app/documentation/configuration/nextjs)
- Pre-configured [Mintlify documentation site](https://next-fast-turbo.mintlify.app/documentation/configuration/docs)

## Tech Stack

- [Next.js](https://nextjs.org/) – Frontend Framework
- [Tailwind](https://tailwindcss.com/) – CSS Framework
- [ShadCN UI](https://ui.shadcn.com/) – UI Components
- [FastAPI](https://fastapi.tiangolo.com/) – Python Backend
- [Mintlify](https://mintlify.com/) – Documentation
- [Supabase](https://supabase.com/) – Database
- [Turborepo](https://turbo.build/repo) – Monorepo
- [Vercel](https://vercel.com/) – deployments

## Getting started

Next-Fast-Turbo is designed to be cloned and modified to each project. For more information on getting started, [view the documentation](https://next-fast-turbo.mintlify.app/documentation/introduction).

## Contributing

Contributions are welcome. Here's how you can contribute:

- [Open an issue](https://github.com/cording12/next-fast-turbo/issues) if you believe you've encountered a bug.
- Follow the [local development guide](https://next-fast-turbo.mintlify.app/documentation/local-development) to get your local dev environment set up.
- Make a [pull request](https://github.com/cording12/next-fast-turbo/pulls) to add new features/make quality-of-life improvements/fix bugs.

## License

Next-Fast-Turbo is open-source under the GNU General Public License Version 3 (GPLv3) or any later version.
