aiohttp==3.9.3; python_version >= "3.9" and python_version < "4.0"
aiosignal==1.3.1; python_version >= "3.9" and python_version < "4.0"
annotated-types==0.6.0; python_version >= "3.8"
anyio==4.3.0; python_version >= "3.9" and python_version < "4.0"
argcomplete==3.2.3; python_version >= "3.9" and python_version < "4.0"
async-timeout==4.0.3; python_version >= "3.9" and python_version < "3.11"
attrs==23.2.0; python_version >= "3.9" and python_version < "4.0"
certifi==2024.2.2; python_version >= "3.9" and python_version < "4.0"
charset-normalizer==3.3.2; python_version >= "3.9" and python_version < "4.0" and python_full_version >= "3.7.0"
click==8.1.7; python_version >= "3.8"
colorama==0.4.6; python_version >= "3.9" and python_full_version < "3.0.0" and platform_system == "Windows" and python_version < "4.0" or platform_system == "Windows" and python_version >= "3.9" and python_full_version >= "3.7.0" and python_version < "4.0"
commitizen==3.18.4; python_version >= "3.9" and python_version < "4.0"
decli==0.6.1; python_version >= "3.9" and python_version < "4.0"
deprecation==2.1.0; python_version >= "3.9" and python_version < "4.0"
dnspython==2.6.1; python_version >= "3.8"
email-validator==2.1.1; python_version >= "3.8"
exceptiongroup==1.2.0; python_version < "3.11" and python_version >= "3.8"
fastapi==0.109.2; python_version >= "3.8"
frozenlist==1.4.1; python_version >= "3.9" and python_version < "4.0"
gotrue==2.4.1; python_version >= "3.9" and python_version < "4.0"
h11==0.14.0; python_version >= "3.9" and python_version < "4.0"
httpcore==1.0.4; python_version >= "3.9" and python_version < "4.0"
httpx==0.25.2; python_version >= "3.9" and python_version < "4.0"
idna==3.6; python_version >= "3.9" and python_version < "4.0"
importlib-metadata==7.0.2; python_version >= "3.9" and python_version < "4.0"
jinja2==3.1.3; python_version >= "3.9" and python_version < "4.0"
markupsafe==2.1.5; python_version >= "3.9" and python_version < "4.0"
multidict==6.0.5; python_version >= "3.9" and python_version < "4.0"
packaging==24.0; python_version >= "3.9" and python_version < "4.0"
postgrest==0.16.1; python_version >= "3.9" and python_version < "4.0"
prompt-toolkit==3.0.36; python_version >= "3.9" and python_version < "4.0" and python_full_version >= "3.6.2"
pydantic-core==2.16.3; python_version >= "3.8"
pydantic-settings==2.2.1; python_version >= "3.8"
pydantic==2.6.4; python_version >= "3.9" and python_version < "4.0"
python-dateutil==2.9.0.post0; python_version >= "3.9" and python_full_version < "3.0.0" and python_version < "4.0" or python_version >= "3.9" and python_version < "4.0" and python_full_version >= "3.3.0"
python-dotenv==1.0.1; python_version >= "3.8"
pyyaml==6.0.1; python_version >= "3.9" and python_version < "4.0"
questionary==2.0.1; python_version >= "3.9" and python_version < "4.0"
realtime==1.0.2; python_version >= "3.9" and python_version < "4.0"
six==1.16.0; python_version >= "3.9" and python_full_version < "3.0.0" and python_version < "4.0" or python_version >= "3.9" and python_version < "4.0" and python_full_version >= "3.3.0"
sniffio==1.3.1; python_version >= "3.9" and python_version < "4.0"
starlette==0.36.3; python_version >= "3.8"
storage3==0.7.3; python_version >= "3.9" and python_version < "4.0"
strenum==0.4.15; python_version >= "3.9" and python_version < "4.0"
supabase-py-async==2.5.5; python_version >= "3.9" and python_version < "4.0"
supafunc==0.4.0; python_version >= "3.9" and python_version < "4.0"
termcolor==2.4.0; python_version >= "3.9" and python_version < "4.0"
tomlkit==0.12.4; python_version >= "3.9" and python_version < "4.0"
typing-extensions==4.10.0; python_version < "3.10" and python_version >= "3.9"
uvicorn==0.27.1; python_version >= "3.8"
wcwidth==0.2.13; python_version >= "3.9" and python_version < "4.0" and python_full_version >= "3.6.2"
websockets==11.0.3; python_version >= "3.9" and python_version < "4.0"
yarl==1.9.4; python_version >= "3.9" and python_version < "4.0"
zipp==3.18.1; python_version >= "3.9" and python_version < "4.0"
