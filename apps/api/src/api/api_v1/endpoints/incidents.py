from typing import Literal, Optional, Union

from fastapi import APIRouter, HTTPException

from src.api.deps import SessionDep
from src.crud import incident
from src.schemas import Incident, IncidentSearchResults

router = APIRouter()


@router.get("/get/", status_code=200, response_model=Incident)
async def get_incident(session: SessionDep, incident_id: str) -> Incident:
    """Returns an incident from an incident_id.

    **Returns:**
    - incident: incident object.
    """
    return await incident.get(session, id=incident_id)


@router.get("/get-all/", status_code=200, response_model=list[Incident])
async def get_all_incidents(session: SessionDep) -> list[Incident]:
    """Returns a list of all incidents.

    **Returns:**
    - list[incident]: List of all incidents.
    """
    return await incident.get_all(session)


@router.get("/search/", status_code=200, response_model=IncidentSearchResults)
async def search_incidents(
    session: SessionDep,
    search_on: Literal["id", "severity", "description"] = "description",
    keyword: Optional[Union[str, int]] = None,
    max_results: Optional[int] = 10,
) -> IncidentSearchResults:
    """
    Search for incidents based on a keyword and return the top `max_results` items.

    **Args:**
    - search_on (str, optional): The field to perform the search on. Defaults to "description".
    - keyword (str, optional): The keyword to search for. Defaults to None.
    - max_results (int, optional): The maximum number of search results to return. Defaults to 10.

    **Returns:**
    - IncidentSearchResults: Object containing a list of the top `max_results` items that match the keyword.
    """
    if not keyword:
        results = await incident.get_all(session)
        return IncidentSearchResults(results=results)

    results = await incident.search_all(
        session, field=search_on, search_value=keyword, max_results=max_results
    )

    if not results:
        raise HTTPException(
            status_code=404, detail="No incidents found matching the search criteria"
        )

    return IncidentSearchResults(results=results)
