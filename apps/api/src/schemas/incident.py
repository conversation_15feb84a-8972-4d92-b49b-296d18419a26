from typing import ClassVar, Sequence, Optional
from datetime import datetime

from pydantic import BaseModel


class Incident(BaseModel):
    id: int
    created_at: datetime
    severity: Optional[str] = None
    description: Optional[str] = None
    service: Optional[str] = None
    environment: Optional[str] = None
    table_name: ClassVar[str] = "Incident"


class IncidentCreate(BaseModel):
    severity: Optional[str] = None
    description: Optional[str] = None
    service: Optional[str] = None
    environment: Optional[str] = None


class IncidentUpdate(BaseModel):
    severity: Optional[str] = None
    description: Optional[str] = None
    service: Optional[str] = None
    environment: Optional[str] = None


class IncidentSearchResults(BaseModel):
    results: Sequence[Incident]
