from typing import Class<PERSON><PERSON>, Sequence, Optional
from datetime import datetime

from pydantic import BaseModel, EmailStr


class User(BaseModel):
    id: int
    created_at: datetime
    email: Optional[str] = None
    name: Optional[str] = None
    org_id: Optional[int] = None
    table_name: ClassVar[str] = "User"


class UserCreate(BaseModel):
    email: Optional[str] = None
    name: Optional[str] = None
    org_id: Optional[int] = None


class UserUpdate(BaseModel):
    email: Optional[str] = None
    name: Optional[str] = None
    org_id: Optional[int] = None


class ResponseMessage(BaseModel):
    message: str


class UserSearchResults(BaseModel):
    results: Sequence[User]
