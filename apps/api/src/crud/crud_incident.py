from typing import Optional

from fastapi import HTT<PERSON><PERSON>xception
from supabase_py_async import AsyncClient

from src.crud.base import CRUDBase
from src.schemas import Incident, IncidentCreate, IncidentUpdate


class CRUDIncident(CRUDBase[Incident, IncidentCreate, IncidentUpdate]):
    async def get(self, db: AsyncClient, *, id: str) -> Optional[Incident]:
        try:
            return await super().get(db, id=id)
        except Exception as e:
            raise HTTPException(
                status_code=404,
                detail=f"{e.code}: Incident not found. {e.details}",
            )

    async def get_all(self, db: AsyncClient) -> list[Incident]:
        try:
            return await super().get_all(db)
        except Exception as e:
            raise HTTPException(
                status_code=404,
                detail=f"An error occurred while fetching incidents. {e}",
            )

    async def search_all(
        self, db: AsyncClient, *, field: str, search_value: str, max_results: int
    ) -> list[Incident]:
        try:
            return await super().search_all(
                db, field=field, search_value=search_value, max_results=max_results
            )
        except Exception as e:
            raise HTTPException(
                status_code=404,
                detail=f"An error occurred while searching for incidents. {e}",
            )


incident = CRUDIncident(Incident)
