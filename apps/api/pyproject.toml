[project]
name = "api"
version = "0.1.0"
description = ""
authors = [
    {name = "cording12", email = "<EMAIL>"}
]
requires-python = ">=3.9"
dependencies = [
    "fastapi>=0.109.2",
    "uvicorn>=0.27.1",
    "email-validator>=2.1.0",
    "pydantic-settings>=2.2.1",
    "python-dotenv>=1.0.1",
    "supabase-py-async>=2.5.5",
]

[project.optional-dependencies]
dev = [
    "isort>=5.10.1",
    "black>=22.6.0",
    "pytest>=8.0.1",
    "pylint-pydantic>=0.3.2",
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"
