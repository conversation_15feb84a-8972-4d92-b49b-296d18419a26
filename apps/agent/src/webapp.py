import os
import json
import asyncio
import logging
from typing import Dict, Any, Optional, List
from contextlib import asynccontextmanager
from fastapi import FastAP<PERSON>, Request, HTTPException
from supabase._async.client import create_client as create_async_client, AsyncClient
from dotenv import load_dotenv

# Import database manager
from db import db_manager, get_db

# Import GitHub utilities
from utils.github_utils import (
    get_repository_details,
    build_top_repositories_images
)
from utils.docker_builder import cleanup_org_images


load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Manage application lifespan - startup and shutdown."""
    # Startup
    logger.info("🚀 Starting up application...")
    await db_manager.connect()
    
    # Optional: Run health check
    if await db_manager.health_check():
        logger.info("✅ Database health check passed")
    else:
        logger.warning("⚠️ Database health check failed")
    
    yield  # Application runs here
    
    # Shutdown
    logger.info("🛑 Shutting down application...")
    logger.info("✅ Application shutdown complete")

# Initialize FastAPI app with lifespan
app = FastAPI(
    title="Backspace GitHub Webhook Handler",
    lifespan=lifespan
)

@app.get("/")
async def read_root():
    """Health check endpoint."""
    db_healthy = await db_manager.health_check()
    return {
        "message": "Backspace GitHub Webhook Handler", 
        "status": "active",
        "database": "healthy" if db_healthy else "unhealthy"
    }

@app.post("/webhooks/github")
async def github_webhook_handler(request: Request):
    """Handle GitHub webhook events."""
    
    # Get headers and payload
    signature = request.headers.get("x-hub-signature-256")
    event_type = request.headers.get("x-github-event")
    delivery_id = request.headers.get("x-github-delivery")
    
    # Get raw payload
    payload_body = await request.body()
    
    # Verify signature (optional for testing, enable for production)
    # if not verify_github_signature(payload_body, signature):
    #     raise HTTPException(status_code=403, detail="Invalid signature")
    
    # Parse JSON payload
    try:
        payload = json.loads(payload_body)
    except json.JSONDecodeError:
        raise HTTPException(status_code=400, detail="Invalid JSON payload")
    
    logger.info(f"GitHub webhook received: {event_type} (delivery: {delivery_id})")
    
    # Route to appropriate handler
    if event_type == "installation":
        await handle_installation_event(payload)
    elif event_type == "installation_repositories":
        await handle_installation_repositories_event(payload)
    elif event_type == "push":
        await handle_push_event(payload)
    else:
        logger.warning(f"Unhandled event type: {event_type}")
    
    return {"status": "success", "event": event_type}

async def handle_installation_event(payload: Dict[str, Any]):
    """Handle GitHub app installation events."""
    action = payload.get("action")
    installation = payload.get("installation", {})
    account = installation.get("account", {})
    repositories = payload.get("repositories", [])
    
    account_login = account.get("login")
    account_type = account.get("type")
    installation_id = str(installation.get("id"))
    
    logger.info(f"Installation event: {action}")
    logger.info(f"Account: {account_login} ({account_type})")
    logger.info(f"Repositories: {len(repositories)}")
    
    if action == "created":
        try:
            db_client = get_db()
            # Query for integration with name 'github', installation_id, and github_org_name
            integration_result = await db_client.table('integrations') \
                .select('*') \
                .eq('name', 'github') \
                .eq('data->>installation_id', installation_id) \
                .eq('data->>github_org_name', account_login) \
                .execute()
            if integration_result.data:
                logger.info(f"Integration with name 'github', installation_id {installation_id}, and github_org_name {account_login} already exists.")
                return
            # If not found, create a new integration with github_org_name and github_org_type in data
            new_data = {
                "installation_id": installation_id,
                "github_org_name": account_login,
                "github_org_type": account_type
            }
            integration_payload = {
                "name": "github",
                "org_id": 1,  # Default org_id for now (TODO: change to installation.get("account", {}).get("id"))
                "data": new_data,
            }
            result = await db_client.table('integrations').insert(integration_payload).execute()
            logger.info(f"Created new github integration: {result.data}")
            # Optionally, you can start background processing here if needed
            if repositories:
                logger.info(f"Starting background processing for {len(repositories)} repositories...")
                # Fetch the integration_id for the new integration
                integration_id = result.data[0]['id']
                asyncio.create_task(
                    process_installation_repositories_background(
                        installation_id, repositories, integration_id, account_login
                    )
                )
                logger.info("✅ Repository processing started in background, webhook response will return immediately")
        except Exception as e:
            logger.error(f"Error handling installation event: {e}")
    elif action == "deleted":
        # App was uninstalled - remove integration, repositories, AND Docker snapshots
        try:
            db_client = get_db()
            # Find the integration by installation_id and github_org_name
            integration_result = await db_client.table('integrations').select('id, data').eq('name', 'github').eq('data->>installation_id', installation_id).eq('data->>github_org_name', account_login).execute()
            if integration_result.data:
                integration_id = integration_result.data[0]['id']
                integration_data = integration_result.data[0]['data']
                github_org_name = integration_data.get('github_org_name', account_login)
                
                logger.info(f"🗑️ Uninstalling GitHub App for organization: {github_org_name}")
                
                # Delete repositories with this integration_id
                await db_client.table('repositories').delete().eq('integration_id', integration_id).execute()
                logger.info(f"Deleted repositories for integration {integration_id}")
                
                # Delete the integration itself
                await db_client.table('integrations').delete().eq('id', integration_id).execute()
                logger.info(f"Deleted integration {integration_id}")
                
                # IMPORTANT: Clean up Daytona snapshots for this organization
                logger.info(f"🐳 Starting Daytona snapshot cleanup for organization: {github_org_name}")
                asyncio.create_task(
                    cleanup_org_images_with_logging(github_org_name)
                )
                logger.info(f"✅ Started Daytona cleanup task for organization: {github_org_name}")
                
            else:
                logger.warning(f"No integration found for installation {installation_id} and org {account_login}")
        except Exception as e:
            logger.error(f"Error removing integration, repositories, and Daytona snapshots: {e}")

async def process_installation_repositories_background(
    installation_id: str,
    repositories: List[Dict[str, Any]],
    integration_id: str,
    account_login: str
) -> None:
    """Process repositories and build Docker images in background."""
    try:
        logger.info(f"🔄 Background processing started for {len(repositories)} repositories")
        
        # Step 1: Add all repositories to database concurrently
        repo_tasks = [
            process_repository_async(installation_id, repo, integration_id, account_login)
            for repo in repositories
        ]
        
        logger.info("📝 Adding repositories to database...")
        await asyncio.gather(*repo_tasks, return_exceptions=True)
        logger.info("✅ All repositories added to database")
        
        # Step 2: Now build Docker images for top 10
        logger.info("🐳 Starting Docker image builds for top repositories...")
        db_client = get_db()
        await build_top_repositories_images(
            integration_id=integration_id,
            installation_id=int(installation_id),
            db_client=db_client,
            top_x=10,
            delay_between_starts=0.5
        )
        
        logger.info("🎉 Background processing completed successfully")
        
    except Exception as e:
        logger.error(f"❌ Background processing failed: {e}")

async def process_repository_async(
    installation_id: str, 
    repo: Dict[str, Any], 
    integration_id: str, 
    account_login: str
) -> str:
    """Process a single repository asynchronously."""
    try:
        db_client = get_db()
        
        # Get detailed repository information from GitHub API (async)
        repo_details = await get_repository_details(installation_id, repo['full_name'])
        
        repo_data = {
            'id': str(repo['id']),
            'url': f"https://github.com/{repo['full_name']}",
            'org_id': 1,  # Default org_id for now (TODO: update if needed)
            'name': repo['full_name'],
            'repo_created_at': repo_details.get('repo_created_at') if repo_details else None,
            'pushed_at': repo_details.get('pushed_at') if repo_details else None,
            'integration_id': integration_id
        }
        
        # Check if repository already exists (async)
        existing_repo = await db_client.table('repositories').select('*').eq('id', str(repo['id'])).execute()
        
        if not existing_repo.data:
            await db_client.table('repositories').insert(repo_data).execute()
            logger.info(f"Added repository: {repo['full_name']}")
            return f"Added repository: {repo['full_name']}"
        else:
            logger.info(f"Repository already exists: {repo['full_name']}")
            return f"Repository already exists: {repo['full_name']}"
            
    except Exception as e:
        logger.error(f"Failed to process repository {repo['full_name']}: {e}")
        raise Exception(f"Failed to process repository {repo['full_name']}: {e}")

async def handle_installation_repositories_event(payload: Dict[str, Any]):
    """Handle repository additions/removals for an installation."""
    action = payload.get("action")
    installation = payload.get("installation", {})
    repositories_added = payload.get("repositories_added", [])
    repositories_removed = payload.get("repositories_removed", [])
    
    installation_id = str(installation.get("id"))
    
    logger.info(f"Installation repositories event: {action}")
    logger.info(f"Installation ID: {installation_id}")
    logger.info(f"Repositories added: {len(repositories_added)}")
    logger.info(f"Repositories removed: {len(repositories_removed)}")
    
    try:
        db_client = get_db()
        # Find the integration by installation_id
        integration_result = await db_client.table('integrations').select('id').eq('name', 'github').eq('data->>installation_id', installation_id).execute()
        if not integration_result.data:
            logger.warning(f"No integration found for installation {installation_id}")
            return
        integration_id = integration_result.data[0]['id']
        # Handle added repositories
        if action == "added" and repositories_added:
            for repo in repositories_added:
                repo_data = {
                    'integration_id': integration_id,
                    'org_id': 1,  # Default org_id for now (TODO: update if needed)
                    'github_repo_id': str(repo['id']),  # Only if this column exists, else remove
                    'url': f"https://github.com/{repo['full_name']}",
                    'name': repo['full_name'],
                    'default_branch': repo.get('default_branch', 'main'),  # Only if this column exists, else remove
                    'repo_created_at': None,  # Can be set if available
                    'pushed_at': None  # Can be set if available
                }
                logger.info(f"Repository to add: {repo_data}")
                await db_client.table('repositories').insert(repo_data).execute()
        # Handle removed repositories
        if action == "removed" and repositories_removed:
            for repo in repositories_removed:
                logger.info(f"Repository to remove: {repo['full_name']} (ID: {repo['id']})")
                await db_client.table('repositories').delete().eq('github_repo_id', str(repo['id'])).execute()
    except Exception as e:
        logger.error(f"Error handling installation repositories event: {e}")

async def handle_push_event(payload: Dict[str, Any]):
    """Handle push events to repositories."""
    repository = payload.get("repository", {})
    installation = payload.get("installation", {})
    commits = payload.get("commits", [])
    ref = payload.get("ref", "")
    
    installation_id = str(installation.get("id"))
    repo_name = repository.get("full_name")
    repo_id = str(repository.get("id"))
    
    logger.info(f"Push event: {repo_name}")
    logger.info(f"Installation ID: {installation_id}")
    logger.info(f"Branch: {ref}")
    logger.info(f"Commits: {len(commits)}")
    
    # TODO: Process push events
    # - Trigger code analysis
    # - Create scan jobs
    # - Store commit information
    
    for commit in commits:
        logger.info(f"Commit: {commit.get('id')} by {commit.get('author', {}).get('name')}")

async def cleanup_org_images_with_logging(org_name: str) -> None:
    """Clean up Daytona snapshots for an organization with comprehensive logging."""
    try:
        logger.info(f"🧹 Starting Daytona snapshot cleanup for organization: {org_name}")
        await cleanup_org_images(org_name)
        logger.info(f"🎉 Daytona snapshot cleanup completed for organization: {org_name}")
    except Exception as e:
        logger.error(f"❌ Daytona snapshot cleanup failed for organization {org_name}: {e}")