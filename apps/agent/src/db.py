"""Database connection and operations module."""

import os
from typing import Optional
from supabase._async.client import create_client as create_async_client, AsyncClient
from dotenv import load_dotenv
import logging

load_dotenv()

logger = logging.getLogger(__name__)

class DatabaseManager:
    """Manages Supabase async client connection."""
    
    def __init__(self):
        self._client: Optional[AsyncClient] = None
        self._url: str = os.environ.get("SUPABASE_URI")
        self._key: str = os.environ.get("SUPABASE_SERVICE_ROLE_KEY")
    
    async def connect(self) -> None:
        """Initialize the async Supabase client."""
        if self._client is None:
            print("🔌 Connecting to Supabase...")
            self._client = await create_async_client(self._url, self._key)
            print("✅ Supabase connection established")
    
    async def disconnect(self) -> None:
        """Disconnect from the database."""
        if self._client:
            logger.info("🔌 Supabase client cleanup (automatic)")
            # Supabase AsyncClient doesn't require manual cleanup
            self._client = None
            logger.info("✅ Database manager disconnected")
    
    @property
    def client(self) -> AsyncClient:
        """Get the Supabase async client."""
        if self._client is None:
            raise RuntimeError("Database client not initialized. Call connect() first.")
        return self._client
    
    async def health_check(self) -> bool:
        """Check if the database connection is healthy."""
        try:
            # Simple query to test connection
            await self._client.table('Organization').select('count').limit(1).execute()
            return True
        except Exception as e:
            print(f"Database health check failed: {e}")
            return False

# Global database manager instance
db_manager = DatabaseManager()

# Convenience function to get the client
def get_db() -> AsyncClient:
    """Get the Supabase async client."""
    return db_manager.client