"""Base agent class for all AI agents."""

from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, Literal
import logging
import uuid

from langchain_core.language_models.chat_models import BaseChatModel
from langchain_core.runnables import RunnableConfig
from .llm_config import get_llm
from utils.sandbox import create_sandbox, cleanup_sandbox #type: ignore

logger = logging.getLogger(__name__)

from langgraph.graph.state import CompiledGraph
from langchain_core.tools import BaseTool

from langgraph.pregel import RetryPolicy
import anthropic
import openai
import json



class BaseGraph(ABC):
    """Abstract base class for all graph implementations."""
    
    def __init__(self, llm: BaseChatModel, tools: list[BaseTool]):
        self.tools = tools
        self.llm = llm.bind_tools(tools)

    
    @abstractmethod
    def compile(self) -> CompiledGraph:
        """Compile the graph and return a CompiledGraph instance.
        
        Returns:
            CompiledGraph: The compiled graph ready for execution
        """
        pass

    @property
    def retry_policy(self) -> RetryPolicy:
        """Get the retry policy configuration.
        
        Returns:
            RetryPolicy: Configured retry policy for handling API errors
        """
        return RetryPolicy(
            retry_on=[
                anthropic.RateLimitError, 
                openai.RateLimitError, 
                anthropic.InternalServerError, 
                anthropic.BadRequestError, 
                anthropic.APIStatusError,  # Covers overloaded_error and other API status issues
                json.JSONDecodeError
            ],
            max_attempts=3,
            initial_interval=60.0,  
            backoff_factor=1.5, 
            max_interval=1000.0,
            jitter=True,
        )


class BaseAgent(ABC):
    """Abstract base class for all AI agents."""
    
    def __init__(
        self, 
        *,
        model_provider: Optional[Literal["anthropic", "openai"]] = "anthropic",
        model_name: Optional[str] = "claude-sonnet-4-20250514",
        use_sandbox: bool = True,
    ):
        """
        Initialize the base agent.
        
        Args:
            llm: Pre-configured language model instance (takes precedence)
            model_provider: Model provider ("anthropic" or "openai") 
            model_name: Specific model name
            use_sandbox: Whether to use a sandbox
            
        Note:
            Either provide a pre-configured llm OR provide both model_provider and model_name.
            model_provider and model_name must be provided together (both or neither).
        """
        self.llm = get_llm(model_provider=model_provider, model_name=model_name)
        self.use_sandbox = use_sandbox
        self.sandbox = None
    
    
    async def run(self, query: str, **kwargs) -> str:
        """
        Process a query with automatic sandbox lifecycle management.
        
        Args:
            query: The input query or task
            **kwargs: Additional parameters
            
        Returns:
            The response from the agent
        """
        if self.use_sandbox:
            run_id = str(uuid.uuid4())  # Generate string UUID
            # Create sandbox with session
            self.sandbox, actual_run_id = await create_sandbox(run_id)
            configurable = {"sandbox": self.sandbox, "run_id": actual_run_id}

            config = {
                "configurable": configurable,
                "recursion_limit": 200,
                "max_concurrency": 10
            }
        else:
            run_id = str(uuid.uuid4())  # Generate string UUID
            config = {
                "configurable": {"run_id": run_id},
                "recursion_limit": 200,
                "max_concurrency": 10
            }

        try:
            # Run the actual implementation
            result = await self._execute(query, config)
            return result
        finally:
            # Always cleanup sandbox (includes session cleanup)
            if self.use_sandbox and self.sandbox:
                await cleanup_sandbox(self.sandbox, actual_run_id if 'actual_run_id' in locals() else run_id)
                self.sandbox = None
    
    @abstractmethod
    async def _execute(self, query: str, config: Optional[RunnableConfig] = None) -> str:
        """
        Execute the agent logic. Override this method in subclasses.
        If use_sandbox=True, self.sandbox will be available.
        
        Args:
            query: The input query or task
            config: the runnable config
            
        Returns:
            The response from the agent
        """
        pass