"""LLM configuration utilities for selecting and instantiating language models."""

from typing import Literal
from dataclasses import dataclass
from langchain_anthropic import Chat<PERSON>nthropic
from langchain_openai import ChatOpenAI
from langchain_core.language_models.chat_models import BaseChatModel


def get_llm(model_provider: Literal["anthropic", "openai"], model_name: str) -> BaseChatModel:
    """
    Get a configured language model instance.
    
    Args:
        model_provider: Either "anthropic" or "openai"
        model_name: The specific model name (see supported models below)
        
    Returns:
        Configured ChatAnthropic or ChatOpenAI instance
        
    """
    if model_provider == "anthropic":
        return ChatAnthropic(
            model=model_name,
            max_tokens=8192,
            temperature=0,
            max_retries=10
        )
    elif model_provider == "openai":
        # o1 models don't support temperature parameter
        if model_name.startswith(("o1", "o3", "o4")):
            return ChatOpenAI(
                model=model_name,
                max_tokens=8192,
                max_retries=10
            )
        else:
            return ChatOpenAI(
                model=model_name,
                max_tokens=8192,
                temperature=0,
                max_retries=10
            )
