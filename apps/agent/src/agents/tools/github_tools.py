"""
GitHub-specific tools for the Deep Scan Agent that ensure markdown-friendly output.

These tools wrap GitHub CLI commands and format the output in a markdown-friendly way.
"""

import asyncio
from typing import Annotated
from langchain_core.tools import tool
from langchain_core.runnables.config import RunnableConfig
from langgraph.prebuilt import InjectedState
from daytona import SessionExecuteRequest
from utils.sandbox_manager import sandbox_manager
from db import get_db, db_manager

import json
import re



@tool
async def github_create_issue(
    title: str,
    body: str,
    sandbox_session_id: Annotated[str, InjectedState("sandbox_session_id")],
    config: RunnableConfig,
    labels: str = "",
    section_id: str = None,
    section_name: str = None
) -> str:
    """Create a GitHub issue with markdown-formatted output and save to database.
    
    Args:
        title: Issue title
        body: Issue body (markdown supported)
        labels: Comma-separated list of labels (optional)
        section_id: Optional section ID for categorization
        section_name: Optional section name for categorization
        
    Returns:
        Markdown-formatted confirmation with issue details
        
    Example:
        github_create_issue(
            title="Test Coverage: Missing tests in auth module",
            body="Current coverage is 45%. Critical gaps in auth.py:123-145",
            labels="test-coverage,medium-priority",
            section_id="testing",
            section_name="Test Coverage"
        )
    """
    try:
        # Get the persistent sandbox instance
        if not sandbox_session_id:
            return f"❌ **Error**: No sandbox session ID provided. The sandbox may not have been initialized."
        
        sandbox = sandbox_manager.get_sandbox(sandbox_session_id)
        if not sandbox:
            return f"❌ **Error**: No active sandbox found for session {sandbox_session_id}"
        
        # Ensure prompts are disabled for non-interactive use
        config_cmd = "gh config set prompt disabled"
        config_req = SessionExecuteRequest(command=config_cmd)
        await asyncio.to_thread(
            sandbox.process.execute_session_command, 
            sandbox_session_id, 
            config_req
        )
        
        # If labels are provided, check which ones exist and create missing ones
        if labels:
            # Get existing labels
            list_labels_cmd = "gh label list --json name -L 1000"
            list_req = SessionExecuteRequest(command=list_labels_cmd)
            labels_result = await asyncio.to_thread(
                sandbox.process.execute_session_command,
                sandbox_session_id,
                list_req
            )
            
            existing_labels = set()
            try:
                labels_json = json.loads(labels_result.output if hasattr(labels_result, 'output') else str(labels_result))
                existing_labels = {label['name'].lower() for label in labels_json}
            except:
                pass
            
            # Check each label and create if missing
            requested_labels = [label.strip() for label in labels.split(',')]
            for label in requested_labels:
                if label.lower() not in existing_labels:
                    # Create the missing label
                    create_label_cmd = f"gh label create '{label}' --description 'Auto-created by scan agent'"
                    create_req = SessionExecuteRequest(command=create_label_cmd)
                    await asyncio.to_thread(
                        sandbox.process.execute_session_command,
                        sandbox_session_id,
                        create_req
                    )
        
        # Escape single quotes in title and body for shell safety
        escaped_title = title.replace("'", "'\"'\"'")
        escaped_body = body.replace("'", "'\"'\"'")
        
        # Build the gh command
        cmd = f"gh issue create --title '{escaped_title}' --body '{escaped_body}'"
        if labels:
            cmd += f" --label '{labels}'"
        
        # Execute command
        req = SessionExecuteRequest(command=cmd)
        result = await asyncio.to_thread(
            sandbox.process.execute_session_command, 
            sandbox_session_id, 
            req
        )
        
        output = result.output if hasattr(result, 'output') else str(result)
        
        # Extract issue URL from output first (more reliable than issue number)
        url_match = re.search(r'https://github\.com/[\w-]+/[\w-]+/issues/(\d+)', output)
        if url_match:
            issue_url = url_match.group(0)
            issue_number = url_match.group(1)
        else:
            # Fallback: try to find issue number in format #123
            issue_match = re.search(r'#(\d+)', output)
            issue_number = issue_match.group(1) if issue_match else None
            issue_url = None
        
        # Check for label warnings
        label_warnings = []
        if "could not add label:" in output:
            # Extract all label errors
            for line in output.split('\n'):
                if "could not add label:" in line:
                    label_match = re.search(r"'([^']+)' not found", line)
                    if label_match:
                        label_warnings.append(label_match.group(1))
        
        # Save to database
        try:
            # Only connect if not already connected
            try:
                _ = db_manager.client
            except Exception:
                await db_manager.connect()
            db_client = get_db()
            
            # Prepare the data for insertion
            issue_data = {
                "name": title,
                "description": body,
                "status": "queued",
                "pr_link": issue_url,
            }
            
            # Add optional fields if provided
            if section_id:
                issue_data["section_id"] = section_id
            if section_name:
                issue_data["section_name"] = section_name
            
            # Insert into the Issue table
            db_result = await db_client.table("Issue").insert(issue_data).execute()
            
            db_success = True
            db_id = db_result.data[0]["id"] if db_result.data else None
        except Exception as db_error:
            db_success = False
            db_id = None
            print(f"Database error: {str(db_error)}")
        
        # Format as markdown
        markdown_output = f"""✅ **GitHub Issue Created Successfully**

**Issue #**: {issue_number if issue_number else "Unknown"}
**Title**: {title}
**Labels**: {labels if labels else "None"}
{f"**Label Warnings**: ⚠️ The following labels don't exist: {', '.join(label_warnings)}" if label_warnings else ""}
**Status**: open
{f"**URL**: {issue_url}" if issue_url else ""}
{f"**Section**: {section_name} (ID: {section_id})" if section_id or section_name else ""}
{f"**Database ID**: {db_id}" if db_success and db_id else "**Database**: ⚠️ Failed to save to database"}

**Body**:
{body}
"""
        
        # Only include CLI output if there are errors or if issue creation failed
        if not issue_number or "error" in output.lower():
            markdown_output += f"""
**GitHub CLI Output**:
```
{output.strip()}
```"""
        
        return markdown_output
        
    except Exception as e:
        return f"""❌ **Failed to create GitHub issue**

**Error**: {str(e)}

**Attempted Command**:
```bash
gh issue create --title '{title}' --body '{body}' --label '{labels}'
```"""


@tool
async def github_list_issues(
    sandbox_session_id: Annotated[str, InjectedState("sandbox_session_id")],
    config: RunnableConfig,  # Required by langchain tool decorator
    state: str = "open",
    labels: str = "",
    limit: int = 10
) -> str:
    """List GitHub issues with markdown-formatted output.
    
    Args:
        state: Issue state - "open", "closed", or "all" (default: "open")
        labels: Comma-separated list of labels to filter by (optional)
        limit: Maximum number of issues to return (default: 10)
        
    Returns:
        Markdown-formatted table of issues
    """
    try:
        # Get the persistent sandbox instance
        if not sandbox_session_id:
            return f"❌ **Error**: No sandbox session ID provided. The sandbox may not have been initialized."
        
        sandbox = sandbox_manager.get_sandbox(sandbox_session_id)
        if not sandbox:
            return f"❌ **Error**: No active sandbox found for session {sandbox_session_id}"
        
        # Ensure prompts are disabled for non-interactive use
        config_cmd = "gh config set prompt disabled"
        config_req = SessionExecuteRequest(command=config_cmd)
        await asyncio.to_thread(
            sandbox.process.execute_session_command, 
            sandbox_session_id, 
            config_req
        )
        
        # Build the gh command
        cmd = f"gh issue list --state {state} --limit {limit} --json number,title,labels,state"
        if labels:
            cmd += f" --label '{labels}'"
        
        # Execute command
        req = SessionExecuteRequest(command=cmd)
        result = await asyncio.to_thread(
            sandbox.process.execute_session_command, 
            sandbox_session_id, 
            req
        )
        
        output = result.output if hasattr(result, 'output') else str(result)
        
        # Try to parse JSON output
        try:
            issues = json.loads(output)
            
            if not issues:
                return f"📋 **No {state} issues found**" + (f" with labels: {labels}" if labels else "")
            
            # Format as markdown table
            markdown_output = f"""📋 **GitHub Issues** ({state}{f', labels: {labels}' if labels else ''})

| # | Title | Labels | State |
|---|-------|--------|-------|"""
            
            for issue in issues[:limit]:
                labels_str = ", ".join([label["name"] for label in issue.get("labels", [])])
                markdown_output += f"\n| #{issue['number']} | {issue['title']} | {labels_str} | {issue['state']} |"
            
            return markdown_output
            
        except json.JSONDecodeError:
            # Fallback to raw output if JSON parsing fails
            return f"""📋 **GitHub Issues** ({state}{f', labels: {labels}' if labels else ''})

```
{output.strip()}
```"""
        
    except Exception as e:
        return f"""❌ **Failed to list GitHub issues**

**Error**: {str(e)}"""


@tool
async def github_view_issue(
    issue_number: int,
    sandbox_session_id: Annotated[str, InjectedState("sandbox_session_id")],
    config: RunnableConfig  # Required by langchain tool decorator
) -> str:
    """View a specific GitHub issue with markdown-formatted output.
    
    Args:
        issue_number: The issue number to view
        
    Returns:
        Markdown-formatted issue details
    """
    try:
        # Get the persistent sandbox instance
        if not sandbox_session_id:
            return f"❌ **Error**: No sandbox session ID provided. The sandbox may not have been initialized."
        
        sandbox = sandbox_manager.get_sandbox(sandbox_session_id)
        if not sandbox:
            return f"❌ **Error**: No active sandbox found for session {sandbox_session_id}"
        
        # Ensure prompts are disabled for non-interactive use
        config_cmd = "gh config set prompt disabled"
        config_req = SessionExecuteRequest(command=config_cmd)
        await asyncio.to_thread(
            sandbox.process.execute_session_command, 
            sandbox_session_id, 
            config_req
        )
        
        # Get issue details in JSON format
        cmd = f"gh issue view {issue_number} --json number,title,body,labels,state,author,createdAt,url"
        
        # Execute command
        req = SessionExecuteRequest(command=cmd)
        result = await asyncio.to_thread(
            sandbox.process.execute_session_command, 
            sandbox_session_id, 
            req
        )
        
        output = result.output if hasattr(result, 'output') else str(result)
        
        # Try to parse JSON output
        try:
            issue = json.loads(output)
            
            labels_str = ", ".join([label["name"] for label in issue.get("labels", [])])
            
            markdown_output = f"""📋 **Issue #{issue['number']}: {issue['title']}**

**State**: {issue['state']}
**Author**: {issue.get('author', {}).get('login', 'Unknown')}
**Created**: {issue.get('createdAt', 'Unknown')}
**Labels**: {labels_str if labels_str else 'None'}
**URL**: {issue.get('url', 'N/A')}

**Description**:
{issue.get('body', 'No description provided')}"""
            
            return markdown_output
            
        except json.JSONDecodeError:
            # Fallback to raw output if JSON parsing fails
            return f"""📋 **Issue #{issue_number}**

```
{output.strip()}
```"""
        
    except Exception as e:
        return f"""❌ **Failed to view GitHub issue #{issue_number}**

**Error**: {str(e)}"""


@tool
async def github_pr_create(
    title: str,
    body: str,
    sandbox_session_id: Annotated[str, InjectedState("sandbox_session_id")],
    db_issue_id: Annotated[str | None, InjectedState("db_issue_id")],
    config: RunnableConfig,  # Required by langchain tool decorator
    base: str = "main",
    draft: bool = False
) -> str:
    """Create a GitHub pull request with markdown-formatted output.
    
    Args:
        title: PR title
        body: PR body (markdown supported)
        base: Base branch (default: "main")
        draft: Create as draft PR (default: False)
        
    Returns:
        Markdown-formatted confirmation with PR details
    """
    try:
        # Get the persistent sandbox instance
        if not sandbox_session_id:
            return f"❌ **Error**: No sandbox session ID provided. The sandbox may not have been initialized."
        
        sandbox = sandbox_manager.get_sandbox(sandbox_session_id)
        if not sandbox:
            return f"❌ **Error**: Unable to get or recreate healthy sandbox for session {sandbox_session_id}"
        
        # Ensure prompts are disabled for non-interactive use
        config_cmd = "gh config set prompt disabled"
        config_req = SessionExecuteRequest(command=config_cmd)
        await asyncio.to_thread(
            sandbox.process.execute_session_command, 
            sandbox_session_id, 
            config_req
        )
        
        # Ensure current branch is pushed to avoid push prompts
        push_cmd = "git push -u origin HEAD"
        push_req = SessionExecuteRequest(command=push_cmd)
        await asyncio.to_thread(
            sandbox.process.execute_session_command, 
            sandbox_session_id, 
            push_req
        )
        
        # Escape single quotes in title and body for shell safety
        escaped_title = title.replace("'", "'\"'\"'")
        escaped_body = body.replace("'", "'\"'\"'")
        
        # Build the gh command with non-interactive settings
        cmd = f"gh pr create --title '{escaped_title}' --body '{escaped_body}' --base {base}"
        if draft:
            cmd += " --draft"
        
        # Execute command
        req = SessionExecuteRequest(command=cmd)
        result = await asyncio.to_thread(
            sandbox.process.execute_session_command, 
            sandbox_session_id, 
            req
        )
        
        output = result.output if hasattr(result, 'output') else str(result)
        
        # Extract PR number and URL from output
        pr_match = re.search(r'#(\d+)', output)
        pr_number = pr_match.group(1) if pr_match else "Unknown"
        
        # Extract PR URL from output (GitHub CLI typically outputs the PR URL)
        url_match = re.search(r'https://github\.com/[\w-]+/[\w-]+/pull/\d+', output)
        pr_url = url_match.group(0) if url_match else None
        
        # Update database issue if db_issue_id is provided
        db_update_status = None
        if db_issue_id:
            try:
                # Only connect if not already connected
                try:
                    _ = db_manager.client
                except Exception:
                    await db_manager.connect()
                db_client = get_db()
                
                # Fetch the existing issue first
                issue_result = await db_client.table("Issue").select("*").eq("id", db_issue_id).execute()
                
                if issue_result.data:
                    # Update the issue status to completed and set pr_link
                    update_data = {
                        "status": "completed"
                    }
                    if pr_url:
                        update_data["pr_link"] = pr_url
                    
                    update_result = await db_client.table("Issue").update(update_data).eq("id", db_issue_id).execute()
                    
                    if update_result.data:
                        db_update_status = f"✅ Updated issue {db_issue_id} status to 'completed'"
                        if pr_url:
                            db_update_status += f" and set PR link"
                    else:
                        db_update_status = f"⚠️ Failed to update issue {db_issue_id}"
                else:
                    db_update_status = f"⚠️ Issue {db_issue_id} not found in database"
                    
            except Exception as db_error:
                db_update_status = f"❌ Database update failed: {str(db_error)}"
        
        # Format as markdown
        # Don't wrap body in code block since it may already contain markdown
        markdown_output = f"""✅ **GitHub Pull Request Created Successfully**

**PR #**: {pr_number}
**Title**: {title}
**Base Branch**: {base}
**Draft**: {"Yes" if draft else "No"}
{f"**PR URL**: {pr_url}" if pr_url else ""}
{f"**Database Update**: {db_update_status}" if db_issue_id else ""}

**Body**:
{body}

**GitHub CLI Output**:
```
{output.strip()}
```"""
        
        return markdown_output
        
    except Exception as e:
        return f"""❌ **Failed to create GitHub pull request**

**Error**: {str(e)}"""


# Export the GitHub tools
github_tools = [
    github_create_issue,
    github_list_issues,
    github_view_issue,
    github_pr_create
]