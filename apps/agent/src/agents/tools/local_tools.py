import os
import re
import difflib
import subprocess
import asyncio
from langchain_core.tools import tool

from typing import Annotated

import re
import shlex


@tool
async def ripgrep_search(query: str) -> str:
    """Search through codebase files using ripgrep.
    
    Usage patterns:
    - Simple text search: 'function_name' or 'TODO'
    - Search with file type: 'import pandas --type py'
    - Regex search: 'def.*async --regex'
    - Case-sensitive: 'ClassName --case-sensitive'
    - Limit results: 'useState --max-count 5'
    
    Common flags:
    - --type [ext]: Search only in files with extension (e.g., py, js, ts)
    - --regex: Treat pattern as regex (default is literal string search)
    - --case-sensitive: Make search case-sensitive
    - --max-count [n]: Limit to n matches per file
    
    Note: Common directories like .venv, node_modules, etc. are automatically excluded.
    """
    try:
        # Base ripgrep command
        cmd = ["rg", "--line-number", "--no-heading", "--with-filename"]
        
        # Add exclusions for common directories
        exclusions = [
            ".venv", "venv", "env", "__pycache__", ".pytest_cache", ".mypy_cache",
            "node_modules", ".git", "dist", "build", ".next", "out",
            ".idea", ".vscode", ".cursor", "target", "vendor", 
            "coverage", ".coverage", ".tox", "*.egg-info", ".bundle", "bower_components"
        ]
        
        for exc in exclusions:
            cmd.extend(["--glob", f"!{exc}"])
        
        # Parse query for pattern and flags
        parts = query.split(' --')
        search_pattern = parts[0].strip()
        
        # Default to case-insensitive literal search
        use_regex = False
        case_sensitive = False
        
        # Process flags
        for i in range(1, len(parts)):
            flag_part = parts[i].strip()
            
            if flag_part == "regex":
                use_regex = True
            elif flag_part == "case-sensitive":
                case_sensitive = True
            elif ' ' in flag_part:
                flag_name, flag_value = flag_part.split(' ', 1)
                if flag_name == "type":
                    # Handle file type
                    cmd.extend(["--type", flag_value])
                elif flag_name == "max-count":
                    cmd.extend(["--max-count", flag_value])
                else:
                    cmd.extend([f"--{flag_name}", flag_value])
            else:
                cmd.append(f"--{flag_part}")
        
        # Add search mode flags
        if not use_regex:
            cmd.append("--fixed-strings")
        if not case_sensitive:
            cmd.append("--ignore-case")
        
        # Add the search pattern as a single argument
        cmd.append(search_pattern)
        
        # Add current directory as search path
        cmd.append(".")
        
        # Execute ripgrep locally
        def _run_ripgrep():
            try:
                result = subprocess.run(
                    cmd,
                    capture_output=True,
                    text=True,
                    encoding="utf-8",
                    timeout=30,
                    cwd=os.getcwd()
                )
                return result.stdout, result.stderr, result.returncode
            except subprocess.TimeoutExpired:
                return "", "Command timed out", 1
            except Exception as e:
                return "", str(e), 1
        
        stdout, stderr, returncode = await asyncio.to_thread(_run_ripgrep)
        
        # Check for errors
        if returncode > 0 and stderr:
            return f"Search error: {stderr}"
        
        if not stdout.strip():
            return f"No matches found for: {search_pattern}"
        
        # Parse and format results
        lines = stdout.strip().split('\n')
        formatted_results = []
        file_matches = {}
        
        for line in lines:
            # ripgrep format: file:line:content
            parts = line.split(':', 2)
            if len(parts) >= 3:
                filepath, line_num, content = parts
                
                # Group by file
                if filepath not in file_matches:
                    file_matches[filepath] = []
                
                file_matches[filepath].append({
                    'line_num': line_num,
                    'content': content.strip()
                })
        
        # Format grouped results
        for filepath, matches in file_matches.items():
            formatted_results.append(f"📁 {filepath}")
            for match in matches:
                formatted_results.append(f"  {match['line_num']:>4}: {match['content']}")
            formatted_results.append("")  # Empty line between files
        
        total_matches = sum(len(matches) for matches in file_matches.values())
        header = f"Found {total_matches} matches in {len(file_matches)} files:\n\n"
        
        return header + "\n".join(formatted_results).strip()
        
    except Exception as e:
        return f"Search failed: {str(e)}"
    

@tool
async def run_command(command: str) -> str:
    """Execute a bash command and return the output.
    
    Examples:
    - 'ls -la' (list files)
    - 'git status' (check git status)
    - 'python -m pytest tests/' (run tests)
    - 'find . -name "*.py" | head -10' (find Python files)
    - 'rm temp_file.txt' (delete file in current repo - allowed)
    - 'rm -rf temp_folder/' (delete folder in current repo - allowed)
    
    GitHub CLI (gh) Integration:
    - 'gh issue list' (list all issues)
    - 'gh issue create --title "Bug fix" --body "Description"' (create issue)
    - 'gh issue view 123' (view issue details)
    - 'gh issue edit 123 --title "New title"' (edit issue)
    - 'gh issue close 123' (close issue)
    - 'gh pr list' (list pull requests)
    - 'gh pr create --title "Feature" --body "Description"' (create PR)
    - 'gh pr view 456' (view PR details)
    - 'gh pr review 456 --approve' (approve PR)
    - 'gh pr merge 456' (merge PR)
    - 'gh pr close 456' (close PR)
    - 'gh repo view' (view repository information)
    - 'gh api repos/:owner/:repo/issues' (use GitHub API directly)
    
    Use this tool to run any bash command in the current working directory.
    Note: Commands that access outside the current repo are blocked for safety.
    """

    
    # Commands that are completely blocked (no scoping possible)
    COMPLETELY_BLOCKED_COMMANDS = {
        # System modification
        'sudo', 'su', 'passwd', 'useradd', 'userdel',
        # Network/security
        'wget', 'curl', 'ssh', 'scp', 'ftp', 'telnet', 'nc', 'netcat',
        # Process/system control
        'kill', 'killall', 'pkill', 'shutdown', 'reboot', 'halt', 'poweroff',
        # Disk operations
        'dd', 'mount', 'umount', 'fsck', 'format', 'fdisk', 'mkfs',
        # System info that could be sensitive
        'env', 'printenv', 'history',
    }
    
    # Commands that are allowed but only when scoped to current directory
    SCOPED_COMMANDS = {
        'rm', 'rmdir', 'unlink', 'truncate', 'chmod', 'chown', 
        '>', '>>', 'tee', 'mv', 'cp', 'tar', 'zip', 'unzip', 'gzip', 'gunzip'
    }
    
    # Patterns that are always dangerous regardless of scope
    ALWAYS_DANGEROUS_PATTERNS = [
        r'sudo\s+',              # any sudo command
        r'eval\s+',              # eval commands
        r'exec\s+',              # exec commands
        r'\|\s*sh\s*$',          # piping to shell
        r'\|\s*bash\s*$',        # piping to bash
        r'>\s*/dev/',            # redirecting to device files
        r'python.*os\.system',   # python os.system calls
        r'system\s*\(',          # system() calls
    ]
    
    def extract_paths_from_command(cmd: str) -> list[str]:
        """Extract file/directory paths from command."""
        try:
            # Parse command using shlex to handle quotes properly
            parts = shlex.split(cmd)
        except ValueError:
            # If shlex fails, fall back to simple split
            parts = cmd.split()
        
        paths = []
        skip_next = False
        
        for i, part in enumerate(parts):
            if skip_next:
                skip_next = False
                continue
                
            # Skip flags/options
            if part.startswith('-'):
                # Some flags take arguments, skip the next part too
                if part in ['-o', '-f', '-t', '-s', '--output', '--file']:
                    skip_next = True
                continue
            
            # Skip the command itself
            if i == 0:
                continue
                
            # This looks like a path if it doesn't start with - and isn't a number
            if not part.startswith('-') and not part.isdigit():
                paths.append(part)
        
        return paths
    
    def is_path_scoped_to_current_dir(path: str) -> bool:
        """Check if path is scoped to current directory and subdirectories."""
        import os.path
        
        # Block absolute paths
        if path.startswith('/'):
            return False
            
        # Block home directory shortcuts
        if path.startswith('~'):
            return False
            
        # Block parent directory navigation
        if '..' in path:
            return False
            
        # Block system/special directories even if relative
        dangerous_starts = [
            'usr/', 'etc/', 'bin/', 'sbin/', 'var/', 'tmp/', 'boot/', 
            'dev/', 'proc/', 'sys/', 'root/', 'home/', 'lib/', 'lib64/'
        ]
        
        for dangerous in dangerous_starts:
            if path.startswith(dangerous):
                return False
        
        # Allow relative paths that don't navigate up
        return True
    
    def is_command_safe(cmd: str) -> tuple[bool, str]:
        """Check if command is safe to execute."""
        cmd_lower = cmd.lower().strip()
        
        # Check against always dangerous patterns first
        for pattern in ALWAYS_DANGEROUS_PATTERNS:
            if re.search(pattern, cmd, re.IGNORECASE):
                return False, f"Blocked dangerous pattern: {pattern}"
        
        # Check against completely blocked commands
        for blocked_cmd in COMPLETELY_BLOCKED_COMMANDS:
            if cmd_lower.startswith(blocked_cmd.lower()) or f' {blocked_cmd.lower()}' in cmd_lower:
                return False, f"Blocked dangerous command: {blocked_cmd}"
        
        # Check scoped commands
        command_word = cmd_lower.split()[0] if cmd_lower.split() else ""
        
        # If it's a scoped command, check if all paths are within current directory
        if any(command_word.startswith(scoped.lower()) for scoped in SCOPED_COMMANDS):
            paths = extract_paths_from_command(cmd)
            
            for path in paths:
                if not is_path_scoped_to_current_dir(path):
                    return False, f"Path outside current directory not allowed: {path}"
        
        return True, ""
    
    # Safety check
    is_safe, reason = is_command_safe(command)
    if not is_safe:
        return f"🚫 Command blocked for safety: {reason}\nCommand: {command}"
    
    try:
        # Run the command in a thread to avoid blocking
        def _run_subprocess():
            return subprocess.run(
                command,
                shell=True,
                capture_output=True,
                text=True,
                encoding="utf-8",
                check=False,
                cwd=os.getcwd(),
                timeout=30
            )
        
        result = await asyncio.to_thread(_run_subprocess)
        
        # Combine stdout and stderr for complete output
        output = ""
        if result.stdout:
            output += f"STDOUT:\n{result.stdout}"
        if result.stderr:
            if output:
                output += "\n\n"
            output += f"STDERR:\n{result.stderr}"
        
        # Include return code if non-zero
        if result.returncode != 0:
            output += f"\n\nReturn code: {result.returncode}"
        
        return output if output.strip() else "Command executed successfully (no output)"
        
    except subprocess.TimeoutExpired:
        return "Command execution timed out (30 seconds limit)"
    except Exception as e:
        return f"Command execution failed: {str(e)}"
    

@tool
async def glob_files(pattern: str) -> str:
    """Find files based on pattern matching using glob.
    
    Examples:
    - '*.py' (all Python files in current directory)
    - '**/*.js' (all JavaScript files recursively)
    - 'src/**/test_*.py' (test files in src directory)
    - '*.{py,js,ts}' (multiple extensions)
    
    Use this tool to find files matching specific patterns.
    """
    import glob
    
    try:
        # Use glob to find matching files in a thread
        def _glob_search():
            matches = glob.glob(pattern, recursive=True)
            matches.sort()  # Sort for consistent output
            return matches
        
        matches = await asyncio.to_thread(_glob_search)
        
        if not matches:
            return f"No files found matching pattern: {pattern}"
        
        # Format results
        formatted_results = []
        for match in matches:
            formatted_results.append(f"📁 {match}")
        
        return f"Found {len(matches)} files matching '{pattern}':\n\n" + "\n".join(formatted_results)
        
    except Exception as e:
        return f"Glob search failed: {str(e)}"

@tool
async def list_directory(path: str = ".", show_hidden: bool = False, max_depth: int = 2) -> str:
    """List files and directories in the specified path with configurable depth.
    
    Examples:
    - list_directory() - list current directory up to depth 2
    - list_directory("src") - list src directory up to depth 2
    - list_directory(".", True, 3) - list with hidden files up to depth 3
    - list_directory("src", max_depth=1) - list only immediate contents
    
    Args:
        path: Directory path to list (default: current directory)
        show_hidden: Whether to show hidden files (default: False)
        max_depth: Maximum depth to traverse (default: 2, min: 1)
    """
    try:
        # Check path existence and type in a thread
        def _check_path():
            if not os.path.exists(path):
                return "not_exists"
            if not os.path.isdir(path):
                return "not_dir"
            return "valid"
        
        path_status = await asyncio.to_thread(_check_path)
        
        if path_status == "not_exists":
            return f"Path does not exist: {path}"
        if path_status == "not_dir":
            return f"Path is not a directory: {path}"
        
        # Ensure depth is at least 1
        max_depth = max(1, max_depth)
        
        def get_tree_structure(dir_path: str, prefix: str = "", current_depth: int = 1) -> list:
            """Recursively build directory tree structure."""
            items = []
            
            try:
                entries = sorted(os.listdir(dir_path))
                
                # Separate directories and files
                dirs = []
                files = []
                
                for entry in entries:
                    # Skip hidden files unless requested
                    if not show_hidden and entry.startswith('.'):
                        continue
                        
                    entry_path = os.path.join(dir_path, entry)
                    if os.path.isdir(entry_path):
                        dirs.append(entry)
                    else:
                        files.append((entry, entry_path))
                
                # Process directories first
                for i, dirname in enumerate(dirs):
                    dir_path_full = os.path.join(dir_path, dirname)
                    is_last_dir = (i == len(dirs) - 1) and len(files) == 0
                    
                    # Add directory with tree characters
                    if current_depth == 1:
                        items.append(f"📁 {dirname}/")
                    else:
                        items.append(f"{prefix}{'└── ' if is_last_dir else '├── '}📁 {dirname}/")
                    
                    # Recursively add subdirectories if within depth limit
                    if current_depth < max_depth:
                        sub_prefix = prefix + ("    " if is_last_dir else "│   ")
                        sub_items = get_tree_structure(dir_path_full, sub_prefix, current_depth + 1)
                        items.extend(sub_items)
                
                # Process files
                for i, (filename, filepath) in enumerate(files):
                    is_last = i == len(files) - 1
                    
                    # Get file size
                    try:
                        size = os.path.getsize(filepath)
                        if size < 1024:
                            size_str = f"{size}B"
                        elif size < 1024 * 1024:
                            size_str = f"{size/1024:.1f}KB"
                        else:
                            size_str = f"{size/(1024*1024):.1f}MB"
                        file_info = f"📄 {filename} ({size_str})"
                    except:
                        file_info = f"📄 {filename}"
                    
                    # Add file with tree characters
                    if current_depth == 1:
                        items.append(file_info)
                    else:
                        items.append(f"{prefix}{'└── ' if is_last else '├── '}{file_info}")
                        
            except PermissionError:
                items.append(f"{prefix}❌ Permission denied")
            except Exception as e:
                items.append(f"{prefix}❌ Error: {str(e)}")
            
            return items
        
        # Get the tree structure in a thread
        tree_items = await asyncio.to_thread(get_tree_structure, path)
        
        if not tree_items:
            return f"Directory is empty: {path}"
        
        return f"Contents of '{path}' (depth: {max_depth}):\n\n" + "\n".join(tree_items)
        
    except Exception as e:
        return f"Failed to list directory: {str(e)}"


@tool
async def read_file(filepath: str, start_line: int = None, end_line: int = None) -> str:
    """Read the contents of a file.
    
    Examples:
    - read_file("src/main.py") - read entire file
    - read_file("config.json", 1, 20) - read lines 1-20
    
    Args:
        filepath: Path to the file to read
        start_line: Starting line number (1-indexed, optional)
        end_line: Ending line number (1-indexed, optional)
    """
    try:
        # Check file existence and type in a thread
        def _check_file():
            if not os.path.exists(filepath):
                return "not_exists"
            if os.path.isdir(filepath):
                return "is_dir"
            return "valid"
        
        file_status = await asyncio.to_thread(_check_file)
        
        if file_status == "not_exists":
            return f"File does not exist: {filepath}"
        if file_status == "is_dir":
            return f"Path is a directory, not a file: {filepath}"
        
        # Read file in a thread
        def _read_file():
            with open(filepath, 'r', encoding='utf-8', errors='ignore') as f:
                if start_line is None and end_line is None:
                    content = f.read()
                    line_count = content.count('\n') + 1
                    return content, line_count, None, None
                else:
                    lines = f.readlines()
                    total_lines = len(lines)
                    
                    # Handle line range
                    start_idx = (start_line - 1) if start_line else 0
                    end_idx = end_line if end_line else total_lines
                    
                    # Validate range
                    start_idx = max(0, start_idx)
                    end_idx = min(total_lines, end_idx)
                    
                    if start_idx >= total_lines:
                        return None, None, start_line, total_lines
                    
                    selected_lines = lines[start_idx:end_idx]
                    content = ''.join(selected_lines)
                    
                    return content, total_lines, start_idx + 1, end_idx
        
        content, line_info, start_info, end_info = await asyncio.to_thread(_read_file)
        
        if content is None:
            return f"Start line {start_info} exceeds file length ({end_info} lines)"
        
        if start_info is None:  # Full file read
            return f"📄 {filepath} ({line_info} lines):\n\n{content}"
        else:  # Range read
            return f"📄 {filepath} (lines {start_info}-{end_info} of {line_info}):\n\n{content}"
        
    except Exception as e:
        return f"Failed to read file: {str(e)}"
    
@tool
async def edit_file(file_path: str, old_str: str, new_str: str) -> str:
    """Edit a file by replacing exact string matches.
    
    IMPORTANT: You must view/read the file first before attempting any edits!
    
    This tool performs precise string replacements in files. It finds an exact match
    of old_str and replaces it with new_str. The tool is designed for safe, accurate
    code modifications.
    
    Args:
        file_path: Absolute or relative path to the file to edit
        old_str: The EXACT string to find and replace. Must match uniquely.
        new_str: The replacement string. Must be different from old_str.
    
    Critical Requirements:
    1. EXACT MATCHING: old_str must match EXACTLY one section in the file,
       including all whitespace, indentation, and line breaks.
    
    2. UNIQUENESS: old_str must occur exactly ONCE in the file. If it appears
       multiple times, the edit will fail. Include enough context (3-5 lines)
       to ensure uniqueness.
    
    3. DIFFERENT STRINGS: old_str and new_str must be different. The tool will
       fail if they are identical.
    
    4. VIEW FIRST: Always use read_file() to view the current file contents
       before attempting edits to ensure accuracy.
    
    Examples:
        # Simple single-line edit
        edit_file("config.py", 
                  old_str='DEBUG = False', 
                  new_str='DEBUG = True')
        
        # Multi-line edit with context
        edit_file("main.py",
                  old_str='def calculate(x, y):\n    return x + y',
                  new_str='def calculate(x, y):\n    \"\"\"Add two numbers.\"\"\"\n    return x + y')
    
    Returns:
        Success: "✅ Successfully replaced in <file_path>" with diff showing changes
        Failure: "❌ Error: <detailed error message>"
    """
    try:
        # Validate inputs
        if not file_path:
            return "❌ Error: file_path is required"
        
        if not old_str:
            return "❌ Error: old_str cannot be empty"
        
        if new_str is None:
            return "❌ Error: new_str is required (use empty string for deletion)"
        
        if old_str == new_str:
            return "❌ Error: old_str and new_str must be different"
        
        # Check file and read content in a thread
        def _check_and_read_file():
            # Check if file exists
            if not os.path.exists(file_path):
                return "not_exists", None
            
            # Check if it's a directory
            if os.path.isdir(file_path):
                return "is_dir", None
            
            # Read the file
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                return "success", content
            except PermissionError:
                return "permission_denied", None
            except Exception as e:
                return "read_error", str(e)
        
        status, content = await asyncio.to_thread(_check_and_read_file)
        
        if status == "not_exists":
            return f"❌ Error: File does not exist: {file_path}"
        elif status == "is_dir":
            return f"❌ Error: Path is a directory, not a file: {file_path}"
        elif status == "permission_denied":
            return f"❌ Error: Permission denied reading file: {file_path}"
        elif status == "read_error":
            return f"❌ Error reading file: {content}"
        
        # Count occurrences
        occurrences = content.count(old_str)
        
        if occurrences == 0:
            error_msg = f"❌ Error: String not found in {file_path}\n"
            error_msg += f"Looking for: {repr(old_str)}"
            return error_msg
        
        if occurrences > 1:
            # Find all occurrence positions
            lines = content.split('\n')
            occurrence_lines = []
            
            # Find line numbers for each occurrence
            search_pos = 0
            while True:
                pos = content.find(old_str, search_pos)
                if pos == -1:
                    break
                
                # Calculate line number
                line_num = content[:pos].count('\n') + 1
                occurrence_lines.append(line_num)
                search_pos = pos + 1
            
            error_msg = f"❌ Error: String not unique - found {occurrences} occurrences in {file_path}\n"
            error_msg += f"Found at lines: {', '.join(map(str, occurrence_lines))}\n"
            error_msg += f"String: {repr(old_str)}\n"
            error_msg += "\nPlease make old_str more specific by including more context."
            
            return error_msg
        
        # Perform the replacement
        new_content = content.replace(old_str, new_str)
        
        # Generate diff for context
        old_lines = content.splitlines(keepends=True)
        new_lines = new_content.splitlines(keepends=True)
        
        diff = list(difflib.unified_diff(
            old_lines, 
            new_lines, 
            fromfile=f"a/{file_path}", 
            tofile=f"b/{file_path}",
            lineterm=""
        ))
        
        # Write the new content in a thread
        def _write_file():
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(new_content)
                return "success"
            except PermissionError:
                return "permission_denied"
            except Exception as e:
                return f"error:{str(e)}"
        
        write_status = await asyncio.to_thread(_write_file)
        
        if write_status == "permission_denied":
            return f"❌ Error: Permission denied writing to file: {file_path}"
        elif write_status.startswith("error:"):
            return f"❌ Error writing file: {write_status[6:]}"
        
        # Format the response with diff
        result = f"✅ Successfully replaced in {file_path}"
        if diff:
            diff_text = '\n'.join(diff)
            result += f"\n\n📝 **Diff:**\n```diff\n{diff_text}\n```"
        
        return result
        
    except Exception as e:
        return f"❌ Unexpected error in edit_file: {str(e)}"


@tool
async def write_file(file_path: str, content: str) -> str:
    """Write content to a file, creating it if it doesn't exist.
    
    This tool creates a new file or overwrites an existing file with the provided content.
    It will create any necessary parent directories automatically.
    
    Args:
        file_path: Path to the file to write (absolute or relative)
        content: The content to write to the file
    
    Important Notes:
    - This tool will OVERWRITE existing files without warning
    - Use edit_file for modifying existing files when you need to preserve content
    - Creates parent directories if they don't exist
    - Uses UTF-8 encoding for text files
    
    Examples:
        # Create a new Python file
        write_file("hello.py", 'print("Hello, World!")')
        
        # Create a file in a new directory
        write_file("src/utils/helper.py", "# Helper functions")
        
        # Create an empty file
        write_file("README.md", "")
    
    Returns:
        Success: "✅ Created file: <file_path>" or "✅ Overwrote file: <file_path>"
        Failure: "❌ Error: <detailed error message>"
    """
    try:
        # Validate inputs
        if not file_path:
            return "❌ Error: file_path is required"
        
        if content is None:
            return "❌ Error: content is required (use empty string for empty file)"
        
        # Check file status, create directories, and write file in a thread
        def _write_file_operation():
            # Check if path points to a directory
            if os.path.exists(file_path) and os.path.isdir(file_path):
                return "is_dir", None
            
            # Check if file already exists
            file_exists = os.path.exists(file_path)
            
            # Create parent directories if needed
            parent_dir = os.path.dirname(file_path)
            if parent_dir and not os.path.exists(parent_dir):
                try:
                    os.makedirs(parent_dir, exist_ok=True)
                except OSError as e:
                    return "mkdir_error", str(e)
            
            # Write the file
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                return "success", file_exists
            except OSError as e:
                return "write_error", str(e)
            except Exception as e:
                return "error", str(e)
        
        status, result = await asyncio.to_thread(_write_file_operation)
        
        if status == "is_dir":
            return f"❌ Error: Path is a directory, not a file: {file_path}"
        elif status == "mkdir_error":
            return f"❌ Error creating directories: {result}"
        elif status == "write_error":
            return f"❌ Error writing file: {result}"
        elif status == "error":
            return f"❌ Error: {result}"
        
        file_exists = result
        
        # Count lines for info
        line_count = content.count('\n') + 1 if content else 0
        
        # Return appropriate success message
        if file_exists:
            return f"✅ Overwrote file: {file_path} ({line_count} lines)"
        else:
            return f"✅ Created file: {file_path} ({line_count} lines)"
            
    except Exception as e:
        return f"❌ Unexpected error in write_file: {str(e)}"


local_tools = [ripgrep_search, run_command, glob_files, list_directory, read_file, edit_file, write_file]