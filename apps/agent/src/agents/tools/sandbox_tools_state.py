"""
Sandbox-enabled tools for secure code execution in Daytona containers.

These tools use bash commands via sandbox.process.exec() but maintain the same
nice formatting and safety features as the original tools.py.
"""

import asyncio
from typing import Annotated
from langchain_core.tools import tool
from langchain_core.runnables.config import RunnableConfig
from langgraph.prebuilt import InjectedState
from daytona import SessionExecuteRequest
from daytona import Sandbox
import difflib
from utils.sandbox_manager import sandbox_manager


@tool
async def sandbox_ripgrep_search(
    query: str, 
    sandbox_session_id: Annotated[str, InjectedState("sandbox_session_id")],
    config: RunnableConfig,
    directory: str = "."
) -> str:
    """Search through codebase files using ripgrep in sandbox.
    
    Usage patterns:
    - Simple text search: 'function_name' or 'TODO'
    - Search with file type: 'import pandas --type py'
    - Regex search: 'def.*async --regex'
    - Case-sensitive: 'ClassName --case-sensitive'
    - Limit results: 'useState --max-count 5'
    
    Common flags:
    - --type [ext]: Search only in files with extension (e.g., py, js, ts)
    - --regex: Treat pattern as regex (default is literal string search)
    - --case-sensitive: Make search case-sensitive
    - --max-count [n]: Limit to n matches per file
    
    Args:
        query: The search query with optional flags
        directory: The directory to search in (default: current directory)
    
    Note: Common directories like .venv, node_modules, etc. are automatically excluded.
    """
    try:
        # Get the persistent sandbox instance
        sandbox = sandbox_manager.get_sandbox(sandbox_session_id)
        if not sandbox:
            return f"❌ Error: No active sandbox found for session {sandbox_session_id}"
        
        # Base ripgrep command
        cmd = ["rg", "--line-number", "--no-heading", "--with-filename"]
        
        # Add exclusions for common directories
        exclusions = [
            ".venv", "venv", "env", "__pycache__", ".pytest_cache", ".mypy_cache",
            "node_modules", ".git", "dist", "build", ".next", "out",
            ".idea", ".vscode", ".cursor", "target", "vendor", 
            "coverage", ".coverage", ".tox", "*.egg-info", ".bundle", "bower_components"
        ]
        
        for exc in exclusions:
            cmd.extend(["--glob", f"!{exc}"])
        
        # Parse query for pattern and flags
        parts = query.split(' --')
        search_pattern = parts[0].strip()
        
        # Default to case-insensitive literal search
        use_regex = False
        case_sensitive = False
        
        # Process flags
        for i in range(1, len(parts)):
            flag_part = parts[i].strip()
            
            if flag_part == "regex":
                use_regex = True
            elif flag_part == "case-sensitive":
                case_sensitive = True
            elif ' ' in flag_part:
                flag_name, flag_value = flag_part.split(' ', 1)
                if flag_name == "type":
                    # Handle file type
                    cmd.extend(["--type", flag_value])
                elif flag_name == "max-count":
                    cmd.extend(["--max-count", flag_value])
                else:
                    cmd.extend([f"--{flag_name}", flag_value])
            else:
                cmd.append(f"--{flag_part}")
        
        # Add search mode flags
        if not use_regex:
            cmd.append("--fixed-strings")
        if not case_sensitive:
            cmd.append("--ignore-case")
        
        # Add the search pattern as a single argument
        cmd.append(search_pattern)
        
        # Add directory as search path
        cmd.append(directory)
        
        # Build command string for sandbox execution
        cmd_str = ' '.join(f"'{arg}'" if ' ' in arg else arg for arg in cmd)
        
        # Execute ripgrep in sandbox using SessionExecuteRequest
        try:
            req = SessionExecuteRequest(command=cmd_str)
            result = await asyncio.to_thread(
                sandbox.process.execute_session_command, 
                sandbox_session_id, 
                req
            )
            stdout = result.output if hasattr(result, 'output') else str(result)
            stderr = ""
            returncode = 0  # Assume success if we get a result
        except Exception as e:
            stdout = ""
            stderr = str(e)
            returncode = 1
        
        # Check for errors
        if returncode > 0 and stderr:
            return f"Search error: {stderr}"
        
        if not stdout.strip():
            return f"No matches found for: {search_pattern}"
        
        # Parse and format results
        lines = stdout.strip().split('\n')
        formatted_results = []
        file_matches = {}
        
        for line in lines:
            # ripgrep format: file:line:content
            parts = line.split(':', 2)
            if len(parts) >= 3:
                filepath, line_num, content = parts
                
                # Group by file
                if filepath not in file_matches:
                    file_matches[filepath] = []
                
                file_matches[filepath].append({
                    'line_num': line_num,
                    'content': content.strip()
                })
        
        # Format grouped results
        for filepath, matches in file_matches.items():
            formatted_results.append(f"📁 {filepath}")
            for match in matches:
                formatted_results.append(f"  {match['line_num']:>4}: {match['content']}")
            formatted_results.append("")  # Empty line between files
        
        total_matches = sum(len(matches) for matches in file_matches.values())
        header = f"Found {total_matches} matches in {len(file_matches)} files:\n\n"
        
        return header + "\n".join(formatted_results).strip()
        
    except Exception as e:
        return f"Search failed: {str(e)}"


@tool
async def sandbox_run_command(
    command: str, 
    sandbox_session_id: Annotated[str, InjectedState("sandbox_session_id")],
    config: RunnableConfig
) -> str:
    """Execute a bash command in sandbox and return the output.
    
    Examples:
    - 'ls -la' (list files)
    - 'git status' (check git status)
    - 'python -m pytest tests/' (run tests)
    - 'find . -name "*.py" | head -10' (find Python files)
    - 'rm temp_file.txt' (delete file in current repo - allowed)
    - 'rm -rf temp_folder/' (delete folder in current repo - allowed)
    
    GitHub CLI (gh) Integration:
    - 'gh issue list' (list all issues)
    - 'gh issue create --title "Bug fix" --body "Description"' (create issue)
    - 'gh issue view 123' (view issue details)
    - 'gh issue edit 123 --title "New title"' (edit issue)
    - 'gh issue close 123' (close issue)
    - 'gh pr list' (list pull requests)
    - 'gh pr create --title "Feature" --body "Description"' (create PR)
    - 'gh pr view 456' (view PR details)
    - 'gh pr review 456 --approve' (approve PR)
    - 'gh pr merge 456' (merge PR)
    - 'gh pr close 456' (close PR)
    - 'gh repo view' (view repository information)
    - 'gh api repos/:owner/:repo/issues' (use GitHub API directly)
    
    Use this tool to run any bash command in the sandbox.
    Note: Commands are executed in an isolated sandbox environment.
    """
    
    try:
        # Get the persistent sandbox instance
        sandbox = sandbox_manager.get_sandbox(sandbox_session_id)
        if not sandbox:
            return f"❌ Error: No active sandbox found for session {sandbox_session_id}"
        
        # Execute command in sandbox using SessionExecuteRequest
        req = SessionExecuteRequest(command=command)
        result = await asyncio.to_thread(
            sandbox.process.execute_session_command, 
            sandbox_session_id, 
            req
        )
        
        # Get output from result
        if hasattr(result, 'output'):
            output = result.output
        else:
            output = str(result)
        
        return output if output.strip() else "Command executed successfully (no output)"
        
    except Exception as e:
        return f"Command execution failed: {str(e)}"


@tool
async def sandbox_glob_files(
    pattern: str, 
    sandbox_session_id: Annotated[str, InjectedState("sandbox_session_id")],
    config: RunnableConfig
) -> str:
    """Find files based on pattern matching using glob in sandbox.
    
    Examples:
    - '*.py' (all Python files in current directory)
    - '**/*.js' (all JavaScript files recursively)
    - 'src/**/test_*.py' (test files in src directory)
    - '*.{py,js,ts}' (multiple extensions)
    
    Use this tool to find files matching specific patterns.
    """
    try:
        # Get the persistent sandbox instance
        sandbox = sandbox_manager.get_sandbox(sandbox_session_id)
        if not sandbox:
            return f"❌ Error: No active sandbox found for session {sandbox_session_id}"
        
        # Use find command as glob equivalent in sandbox
        if '**/' in pattern:
            # Recursive search
            find_pattern = pattern.replace('**/', '')
            cmd = f"find . -name '{find_pattern}' -type f"
        else:
            # Non-recursive search
            cmd = f"find . -maxdepth 1 -name '{pattern}' -type f"
        
        # Execute find command in sandbox using SessionExecuteRequest
        req = SessionExecuteRequest(command=cmd)
        result = await asyncio.to_thread(
            sandbox.process.execute_session_command, 
            sandbox_session_id, 
            req
        )
        output = result.output if hasattr(result, 'output') else str(result)
        
        if not output.strip():
            return f"No files found matching pattern: {pattern}"
        
        # Parse and format results
        matches = [line.strip() for line in output.strip().split('\n') if line.strip()]
        matches.sort()  # Sort for consistent output
        
        # Format results
        formatted_results = []
        for match in matches:
            formatted_results.append(f"📁 {match}")
        
        return f"Found {len(matches)} files matching '{pattern}':\n\n" + "\n".join(formatted_results)
        
    except Exception as e:
        return f"Glob search failed: {str(e)}"


@tool
async def sandbox_list_directory(
    path: str = ".", 
    show_hidden: bool = False, 
    max_depth: int = 2, 
    sandbox_session_id: Annotated[str, InjectedState("sandbox_session_id")] = None,
    config: RunnableConfig = None
) -> str:
    """List files and directories in the specified path with configurable depth in sandbox.
    
    Examples:
    - sandbox_list_directory() - list current directory up to depth 2
    - sandbox_list_directory("src") - list src directory up to depth 2
    - sandbox_list_directory(".", True, 3) - list with hidden files up to depth 3
    - sandbox_list_directory("src", max_depth=1) - list only immediate contents
    
    Args:
        path: Directory path to list (default: current directory)
        show_hidden: Whether to show hidden files (default: False)
        max_depth: Maximum depth to traverse (default: 2, min: 1)
    """
    try:
        # Get the persistent sandbox instance
        sandbox = sandbox_manager.get_sandbox(sandbox_session_id)
        if not sandbox:
            return f"❌ Error: No active sandbox found for session {sandbox_session_id}"
        
        # Check if path exists using ls command
        check_cmd = f"ls -ld '{path}'"
        try:
            req = SessionExecuteRequest(command=check_cmd)
            result = await asyncio.to_thread(
                sandbox.process.execute_session_command, 
                sandbox_session_id, 
                req
            )
            if not (result.output if hasattr(result, 'output') else str(result)).strip():
                return f"Path does not exist: {path}"
        except:
            return f"Path does not exist: {path}"
        
        # Ensure depth is at least 1
        max_depth = max(1, max_depth)
        
        # Build tree command
        tree_cmd = f"tree '{path}'"
        if max_depth > 0:
            tree_cmd += f" -L {max_depth}"
        if not show_hidden:
            tree_cmd += " -I '.*'"  # Ignore hidden files
        tree_cmd += " -F"  # Add file type indicators
        
        try:
            # Try tree command first
            req = SessionExecuteRequest(command=tree_cmd)
            result = await asyncio.to_thread(
                sandbox.process.execute_session_command, 
                sandbox_session_id, 
                req
            )
            output = result.output if hasattr(result, 'output') else str(result)
            if output and "command not found" not in output.lower():
                return f"Contents of '{path}' (depth: {max_depth}):\n\n{output}"
        except:
            pass
        
        # Fallback to find + ls for tree-like structure
        find_cmd = f"find '{path}' -maxdepth {max_depth}"
        if not show_hidden:
            find_cmd += " -not -path '*/.*'"
        find_cmd += " | sort"
        
        req = SessionExecuteRequest(command=find_cmd)
        result = await asyncio.to_thread(
            sandbox.process.execute_session_command, 
            sandbox_session_id, 
            req
        )
        output = result.output if hasattr(result, 'output') else str(result)
        
        if not output.strip():
            return f"Directory is empty: {path}"
        
        # Parse find output and create tree-like structure
        lines = [line.strip() for line in output.strip().split('\n') if line.strip()]
        formatted_results = []
        
        for line in lines:
            # Calculate depth relative to base path
            relative_path = line[len(path):].lstrip('/')
            if not relative_path:  # This is the base directory
                continue
            
            depth = relative_path.count('/')
            filename = relative_path.split('/')[-1]
            
            # Add indentation based on depth
            indent = "  " * depth
            
            # Check if it's a directory using ls
            is_dir_cmd = f"test -d '{line}' && echo 'DIR' || echo 'FILE'"
            try:
                req = SessionExecuteRequest(command=is_dir_cmd)
                dir_result = await asyncio.to_thread(
                    sandbox.process.execute_session_command, 
                    sandbox_session_id, 
                    req
                )
                is_dir = (dir_result.output if hasattr(dir_result, 'output') else str(dir_result)).strip() == 'DIR'
                icon = "📁" if is_dir else "📄"
                name = f"{filename}/" if is_dir else filename
            except:
                icon = "📄"
                name = filename
            
            formatted_results.append(f"{indent}{icon} {name}")
        
        return f"Contents of '{path}' (depth: {max_depth}):\n\n" + "\n".join(formatted_results)
        
    except Exception as e:
        return f"Failed to list directory: {str(e)}"


@tool
async def sandbox_read_file(
    filepath: str, 
    start_line: int = None, 
    end_line: int = None, 
    sandbox_session_id: Annotated[str, InjectedState("sandbox_session_id")] = None,
    config: RunnableConfig = None
) -> str:
    """Read the contents of a file in sandbox.
    
    Examples:
    - sandbox_read_file("src/main.py") - read entire file
    - sandbox_read_file("config.json", 1, 20) - read lines 1-20
    
    Args:
        filepath: Path to the file to read
        start_line: Starting line number (1-indexed, optional)
        end_line: Ending line number (1-indexed, optional)
    """
    try:
        # Get the persistent sandbox instance
        sandbox = sandbox_manager.get_sandbox(sandbox_session_id)
        if not sandbox:
            return f"❌ Error: No active sandbox found for session {sandbox_session_id}"
        
        # Check if file exists
        check_cmd = f"test -f '{filepath}' && echo 'EXISTS' || echo 'NOT_EXISTS'"
        req = SessionExecuteRequest(command=check_cmd)
        result = await asyncio.to_thread(
            sandbox.process.execute_session_command, 
            sandbox_session_id, 
            req
        )
        exists = (result.output if hasattr(result, 'output') else str(result)).strip() == 'EXISTS'
        
        if not exists:
            return f"File does not exist: {filepath}"
        
        # Check if it's a directory
        dir_check_cmd = f"test -d '{filepath}' && echo 'DIR' || echo 'FILE'"
        req = SessionExecuteRequest(command=dir_check_cmd)
        result = await asyncio.to_thread(
            sandbox.process.execute_session_command, 
            sandbox_session_id, 
            req
        )
        is_dir = (result.output if hasattr(result, 'output') else str(result)).strip() == 'DIR'
        
        if is_dir:
            return f"Path is a directory, not a file: {filepath}"
        
        if start_line is None and end_line is None:
            # Read entire file
            cat_cmd = f"cat '{filepath}'"
            req = SessionExecuteRequest(command=cat_cmd)
            result = await asyncio.to_thread(
                sandbox.process.execute_session_command, 
                sandbox_session_id, 
                req
            )
            content = result.output if hasattr(result, 'output') else str(result)
            
            # Count lines
            line_count = content.count('\n') + 1 if content else 0
            return f"📄 {filepath} ({line_count} lines):\n\n{content}"
        else:
            # Read specific line range
            if start_line and end_line:
                sed_cmd = f"sed -n '{start_line},{end_line}p' '{filepath}'"
            elif start_line:
                sed_cmd = f"sed -n '{start_line},$p' '{filepath}'"
            else:
                sed_cmd = f"sed -n '1,{end_line}p' '{filepath}'"
            
            req = SessionExecuteRequest(command=sed_cmd)
            result = await asyncio.to_thread(
                sandbox.process.execute_session_command, 
                sandbox_session_id, 
                req
            )
            content = result.output if hasattr(result, 'output') else str(result)
            
            # Get total line count
            wc_cmd = f"wc -l '{filepath}'"
            req = SessionExecuteRequest(command=wc_cmd)
            wc_result = await asyncio.to_thread(
                sandbox.process.execute_session_command, 
                sandbox_session_id, 
                req
            )
            total_lines = (wc_result.output if hasattr(wc_result, 'output') else str(wc_result)).strip().split()[0]
            
            start_display = start_line or 1
            end_display = end_line or total_lines
            
            return f"📄 {filepath} (lines {start_display}-{end_display} of {total_lines}):\n\n{content}"
        
    except Exception as e:
        return f"Failed to read file: {str(e)}"


@tool
async def sandbox_edit_file(
    file_path: str, 
    old_str: str, 
    new_str: str, 
    sandbox_session_id: Annotated[str, InjectedState("sandbox_session_id")],
    config: RunnableConfig
) -> str:
    """Edit a file by replacing exact string matches in sandbox.
    
    IMPORTANT: You must view/read the file first before attempting any edits!
    
    This tool performs precise string replacements in files. It finds an exact match
    of old_str and replaces it with new_str. The tool is designed for safe, accurate
    code modifications.
    
    Args:
        file_path: Path to the file to edit. Can be:
            - Relative path: "src/main.py" or "main.py"
            - Path with ./: "./src/main.py" (the ./ will be stripped)
            - DO NOT include /workspace/ prefix - it's added automatically
        old_str: The EXACT string to find and replace. Must match uniquely.
        new_str: The replacement string. Must be different from old_str.
    
    Critical Requirements:
    1. EXACT MATCHING: old_str must match EXACTLY one section in the file,
       including all whitespace, indentation, and line breaks.
    
    2. UNIQUENESS: old_str must occur exactly ONCE in the file. If it appears
       multiple times, the edit will fail. Include enough context (3-5 lines)
       to ensure uniqueness.
    
    3. DIFFERENT STRINGS: old_str and new_str must be different. The tool will
       fail if they are identical.
    
    4. VIEW FIRST: Always use sandbox_read_file() to view the current file contents
       before attempting edits to ensure accuracy.
    
    Examples:
        # Simple single-line edit
        sandbox_edit_file("config.py", 
                  old_str='DEBUG = False', 
                  new_str='DEBUG = True')
        
        # Multi-line edit with context
        sandbox_edit_file("main.py",
                  old_str='def calculate(x, y):\\n    return x + y',
                  new_str='def calculate(x, y):\\n    \\\"\\\"\\\"Add two numbers.\\\"\\\"\\\"\\n    return x + y')
    
    Returns:
        Success: "✅ Successfully replaced in <file_path>" with diff showing changes
        Failure: "❌ Error: <detailed error message>"
    """
    try:
        # Get the persistent sandbox instance
        sandbox = sandbox_manager.get_sandbox(sandbox_session_id)
        if not sandbox:
            return f"❌ Error: No active sandbox found for session {sandbox_session_id}"
        
        # Validate inputs
        if not file_path:
            return "❌ Error: file_path is required"
        
        if not old_str:
            return "❌ Error: old_str cannot be empty"
        
        if new_str is None:
            return "❌ Error: new_str is required (use empty string for deletion)"
        
        if old_str == new_str:
            return "❌ Error: old_str and new_str must be different"

        # Normalize file path - handle various input formats
        if file_path.startswith("./"):
            file_path = file_path[2:]  # Remove ./
        
        # Only prepend /workspace/ if not already present and not an absolute path
        if not file_path.startswith("/workspace/") and not file_path.startswith("/"):
            file_path = f"/workspace/{file_path}"
        elif file_path.startswith("/") and not file_path.startswith("/workspace/"):
            # If it's an absolute path not in workspace, prepend workspace
            file_path = f"/workspace{file_path}"
        
        # Read the file first to check existence and count occurrences
        try:
            content_bytes = await asyncio.to_thread(sandbox.fs.download_file, file_path)
            content = content_bytes.decode('utf-8')
        except Exception as e:
            return f"❌ Error: File does not exist or cannot be read: {file_path} - {str(e)}"
        
        # Count occurrences
        occurrences = content.count(old_str)
        
        if occurrences == 0:
            error_msg = f"❌ Error: String not found in {file_path}\n"
            error_msg += f"Looking for: {repr(old_str)}"
            return error_msg
        
        if occurrences > 1:
            # Find all occurrence positions
            occurrence_lines = []
            
            # Find line numbers for each occurrence
            search_pos = 0
            while True:
                pos = content.find(old_str, search_pos)
                if pos == -1:
                    break
                
                # Calculate line number
                line_num = content[:pos].count('\n') + 1
                occurrence_lines.append(line_num)
                search_pos = pos + 1
            
            error_msg = f"❌ Error: String not unique - found {occurrences} occurrences in {file_path}\n"
            error_msg += f"Found at lines: {', '.join(map(str, occurrence_lines))}\n"
            error_msg += f"String: {repr(old_str)}\n"
            error_msg += "\nPlease make old_str more specific by including more context."
            
            return error_msg
        
        # Use Daytona's replace_in_files API for the replacement
        try:
            results = await asyncio.to_thread(
                sandbox.fs.replace_in_files,
                files=[file_path],
                pattern=old_str,
                new_value=new_str
            )
            
            # Check if replacement was successful
            if not results or not results[0].success:
                error_msg = results[0].error if results and results[0].error else "Unknown error"
                return f"❌ Error replacing text: {error_msg}"
            
        except Exception as e:
            return f"❌ Error during replacement: {str(e)}"
        
        # Generate diff for context
        new_content = content.replace(old_str, new_str)
        old_lines = content.splitlines(keepends=True)
        new_lines = new_content.splitlines(keepends=True)
        
        diff = list(difflib.unified_diff(
            old_lines, 
            new_lines, 
            fromfile=f"a/{file_path}", 
            tofile=f"b/{file_path}",
            lineterm=""
        ))
        
        # Format the response with diff
        result = f"✅ Successfully replaced in {file_path}"
        if diff:
            diff_text = '\n'.join(diff)
            result += f"\n\n📝 **Diff:**\n```diff\n{diff_text}\n```"
        
        return result
        
    except Exception as e:
        return f"❌ Unexpected error in sandbox_edit_file: {str(e)}"


@tool
async def sandbox_write_file(
    file_path: str, 
    content: str, 
    sandbox_session_id: Annotated[str, InjectedState("sandbox_session_id")],
    config: RunnableConfig
) -> str:
    """Write content to a file in sandbox, creating it if it doesn't exist.
    
    This tool creates a new file or overwrites an existing file with the provided content.
    It will create any necessary parent directories automatically.
    
    Args:
        file_path: Path to the file to write. Can be:
            - Relative path: "src/main.py" or "main.py"
            - Path with ./: "./src/main.py" (the ./ will be stripped)
            - DO NOT include /workspace/ prefix - it's added automatically
        content: The content to write to the file
    
    Important Notes:
    - This tool will OVERWRITE existing files without warning
    - Use sandbox_edit_file for modifying existing files when you need to preserve content
    - Creates parent directories if they don't exist
    - Uses UTF-8 encoding for text files
    
    Examples:
        # Create a new Python file
        sandbox_write_file("hello.py", 'print("Hello, World!")')
        
        # Create a file in a new directory
        sandbox_write_file("src/utils/helper.py", "# Helper functions")
        
        # Create an empty file
        sandbox_write_file("README.md", "")
    
    Returns:
        Success: "✅ Created file: <file_path>" or "✅ Overwrote file: <file_path>"
        Failure: "❌ Error: <detailed error message>"
    """
    try:
        # Get the persistent sandbox instance
        sandbox = sandbox_manager.get_sandbox(sandbox_session_id)
        if not sandbox:
            return f"❌ Error: No active sandbox found for session {sandbox_session_id}"
        
        # Validate inputs
        if not file_path:
            return "❌ Error: file_path is required"
        
        if content is None:
            return "❌ Error: content is required (use empty string for empty file)"
        
        # Normalize file path - handle various input formats
        if file_path.startswith("./"):
            file_path = file_path[2:]  # Remove ./
        
        # Only prepend /workspace/ if not already present and not an absolute path
        if not file_path.startswith("/workspace/") and not file_path.startswith("/"):
            file_path = f"/workspace/{file_path}"
        elif file_path.startswith("/") and not file_path.startswith("/workspace/"):
            # If it's an absolute path not in workspace, prepend workspace
            file_path = f"/workspace{file_path}"

        # Check if file already exists using download to test
        file_exists = False
        try:
            await asyncio.to_thread(sandbox.fs.download_file, file_path)
            file_exists = True
        except Exception:
            file_exists = False
        
        # Create parent directories if needed
        parent_dir = '/'.join(file_path.split('/')[:-1])
        if parent_dir and parent_dir != '.' and parent_dir != '':
            try:
                await asyncio.to_thread(sandbox.fs.create_folder, parent_dir)
            except Exception:
                # Parent directory might already exist, that's fine
                pass
        
        # Write the file using Daytona filesystem API
        try:
            # Convert content to bytes for upload_file
            content_bytes = content.encode('utf-8')
            await asyncio.to_thread(sandbox.fs.upload_file, content_bytes, file_path)
        except Exception as e:
            return f"❌ Error writing file: {str(e)}"
        
        # Count lines for info
        line_count = content.count('\n') + 1 if content else 0
        
        # Return appropriate success message
        if file_exists:
            return f"✅ Overwrote file: {file_path} ({line_count} lines)"
        else:
            return f"✅ Created file: {file_path} ({line_count} lines)"
            
    except Exception as e:
        return f"❌ Unexpected error in sandbox_write_file: {str(e)}"


# Export tools list
read_tools = [
    # sandbox_ripgrep_search, 
    sandbox_run_command, 
    sandbox_glob_files, 
    sandbox_list_directory, 
    sandbox_read_file, 
]

sandbox_tools = [
    sandbox_edit_file, 
    sandbox_write_file
] + read_tools