"""
Analysis tools for comprehensive codebase security and quality assessment.

These tools provide specialized functionality for:
- Static analysis and security scanning
- Test execution and coverage analysis
- Dependency vulnerability scanning
- Code quality metrics and reporting
"""

import json
import subprocess
from typing import Dict, List, Any, Optional
from langchain_core.tools import tool
from langchain_core.runnables.config import RunnableConfig
from daytona import SessionExecuteRequest
import sys
import os
 

@tool
async def run_security_scan(scan_type: str = "bandit", target_path: str = ".", config: RunnableConfig = None) -> str:
    """Run security analysis tools on the codebase.
    
    Available scan types:
    - 'bandit': Python security scanner (finds common security issues)
    - 'safety': Python dependency vulnerability scanner
    - 'semgrep': Multi-language static analysis security scanner
    - 'npm-audit': Node.js dependency security audit
    - 'snyk': Multi-language dependency and code vulnerability scanner
    
    Args:
        scan_type: Type of security scan to run
        target_path: Path to scan (default: current directory)
        
    Returns:
        Security scan results with findings and recommendations
    """
    try:
        if config and "configurable" in config and "sandbox" in config["configurable"]:
            # Sandbox execution
            sandbox = config["configurable"]["sandbox"]
            run_id = config["configurable"]["run_id"]
            
            if scan_type == "bandit":
                cmd = f"bandit -r {target_path} -f json -o bandit_report.json; cat bandit_report.json || bandit -r {target_path}"
            elif scan_type == "safety":
                cmd = f"safety check --json || safety check"
            elif scan_type == "semgrep":
                cmd = f"semgrep --config=auto {target_path} --json -o semgrep_report.json; cat semgrep_report.json || semgrep --config=auto {target_path}"
            elif scan_type == "npm-audit":
                cmd = f"npm audit --json || npm audit"
            elif scan_type == "snyk":
                cmd = f"snyk test --json || snyk test"
            else:
                return f"❌ Unknown scan type: {scan_type}. Available types: bandit, safety, semgrep, npm-audit, snyk"
            
            
            try:
                req = SessionExecuteRequest(command=cmd)
                result = sandbox.process.execute_session_command(run_id, req)
                output = result.output if hasattr(result, 'output') else str(result)
            except Exception as e:
                return f"❌ Failed to run {scan_type}: {str(e)}"
        
        else:
            # Local execution
            if scan_type == "bandit":
                cmd = ["bandit", "-r", target_path, "-f", "json"]
            elif scan_type == "safety":
                cmd = ["safety", "check", "--json"]
            elif scan_type == "semgrep":
                cmd = ["semgrep", "--config=auto", target_path, "--json"]
            elif scan_type == "npm-audit":
                cmd = ["npm", "audit", "--json"]
            elif scan_type == "snyk":
                cmd = ["snyk", "test", "--json"]
            else:
                return f"❌ Unknown scan type: {scan_type}. Available types: bandit, safety, semgrep, npm-audit, snyk"
            
            try:
                result = subprocess.run(cmd, capture_output=True, text=True, cwd=target_path, timeout=300)
                output = result.stdout if result.stdout else result.stderr
            except subprocess.TimeoutExpired:
                return f"⏰ Security scan '{scan_type}' timed out after 5 minutes"
            except FileNotFoundError:
                return f"⚠️ Security scanner '{scan_type}' not found. Please install it first."
            except Exception as e:
                return f"❌ Error running {scan_type}: {str(e)}"

        # Parse and format results
        if not output.strip():
            return f"✅ {scan_type.upper()} scan completed - No issues found"
        
        # Try to parse JSON output for better formatting
        try:
            json_data = json.loads(output)
            return format_security_results(scan_type, json_data)
        except json.JSONDecodeError:
            # Return raw output if JSON parsing fails
            return f"🔍 {scan_type.upper()} Security Scan Results:\n\n{output}"
            
    except Exception as e:
        return f"❌ Security scan failed: {str(e)}"


@tool
async def run_code_quality_scan(scan_type: str = "flake8", target_path: str = ".", config: RunnableConfig = None) -> str:
    """Run code quality analysis tools on the codebase.
    
    Available scan types:
    - 'flake8': Python code style checker
    - 'pylint': Python code analysis for errors and quality
    - 'mypy': Python static type checker
    - 'black': Python code formatter (check only)
    - 'eslint': JavaScript/TypeScript linter
    - 'prettier': Code formatter for multiple languages (check only)
    - 'sonarqube': Comprehensive code quality scanner
    
    Args:
        scan_type: Type of quality scan to run
        target_path: Path to scan (default: current directory)
        
    Returns:
        Code quality analysis results with issues and suggestions
    """
    try:
        if config and "configurable" in config and "sandbox" in config["configurable"]:
            # Sandbox execution
            sandbox = config["configurable"]["sandbox"]
            run_id = config["configurable"]["run_id"]
            
            if scan_type == "flake8":
                cmd = f"flake8 {target_path} --statistics --count"
            elif scan_type == "pylint":
                cmd = f"pylint {target_path} --output-format=json || pylint {target_path}"
            elif scan_type == "mypy":
                cmd = f"mypy {target_path} --show-error-codes"
            elif scan_type == "black":
                cmd = f"black --check --diff {target_path}"
            elif scan_type == "eslint":
                cmd = f"eslint {target_path} --format json || eslint {target_path}"
            elif scan_type == "prettier":
                cmd = f"prettier --check {target_path}"
            elif scan_type == "sonarqube":
                cmd = f"sonar-scanner -Dsonar.projectKey=analysis -Dsonar.sources={target_path}"
            else:
                return f"❌ Unknown scan type: {scan_type}. Available types: flake8, pylint, mypy, black, eslint, prettier, sonarqube"
            
            
            try:
                req = SessionExecuteRequest(command=cmd)
                result = sandbox.process.execute_session_command(run_id, req)
                output = result.output if hasattr(result, 'output') else str(result)
            except Exception as e:
                return f"❌ Failed to run {scan_type}: {str(e)}"
        
        else:
            # Local execution
            if scan_type == "flake8":
                cmd = ["flake8", target_path, "--statistics", "--count"]
            elif scan_type == "pylint":
                cmd = ["pylint", target_path, "--output-format=json"]
            elif scan_type == "mypy":
                cmd = ["mypy", target_path, "--show-error-codes"]
            elif scan_type == "black":
                cmd = ["black", "--check", "--diff", target_path]
            elif scan_type == "eslint":
                cmd = ["eslint", target_path, "--format", "json"]
            elif scan_type == "prettier":
                cmd = ["prettier", "--check", target_path]
            elif scan_type == "sonarqube":
                cmd = ["sonar-scanner", f"-Dsonar.projectKey=analysis", f"-Dsonar.sources={target_path}"]
            else:
                return f"❌ Unknown scan type: {scan_type}. Available types: flake8, pylint, mypy, black, eslint, prettier, sonarqube"
            
            try:
                result = subprocess.run(cmd, capture_output=True, text=True, cwd=target_path, timeout=300)
                output = result.stdout if result.stdout else result.stderr
            except subprocess.TimeoutExpired:
                return f"⏰ Quality scan '{scan_type}' timed out after 5 minutes"
            except FileNotFoundError:
                return f"⚠️ Quality scanner '{scan_type}' not found. Please install it first."
            except Exception as e:
                return f"❌ Error running {scan_type}: {str(e)}"

        if not output.strip():
            return f"✅ {scan_type.upper()} scan completed - No issues found"
        
        return f"🔍 {scan_type.upper()} Code Quality Results:\n\n{output}"
            
    except Exception as e:
        return f"❌ Code quality scan failed: {str(e)}"


@tool
async def run_test_analysis(test_type: str = "pytest", target_path: str = ".", config: RunnableConfig = None) -> str:
    """Run test analysis and execution tools on the codebase.
    
    Available test types:
    - 'pytest': Python test runner with coverage
    - 'pytest-cov': Python test runner with detailed coverage report
    - 'unittest': Python built-in unittest runner
    - 'jest': JavaScript/Node.js test runner
    - 'mocha': JavaScript/Node.js test runner
    - 'coverage': Generate coverage reports for existing test results
    
    Args:
        test_type: Type of test analysis to run
        target_path: Path to test files (default: current directory)
        
    Returns:
        Test execution results, coverage metrics, and analysis
    """
    try:
        if config and "configurable" in config and "sandbox" in config["configurable"]:
            # Sandbox execution
            sandbox = config["configurable"]["sandbox"]
            run_id = config["configurable"]["run_id"]
            
            if test_type == "pytest":
                cmd = f"pytest {target_path} -v --tb=short"
            elif test_type == "pytest-cov":
                cmd = f"pytest {target_path} --cov=. --cov-report=term-missing --cov-report=html -v"
            elif test_type == "unittest":
                cmd = f"python -m unittest discover -s {target_path} -v"
            elif test_type == "jest":
                cmd = f"jest {target_path} --coverage --verbose"
            elif test_type == "mocha":
                cmd = f"mocha {target_path} --reporter spec"
            elif test_type == "coverage":
                cmd = f"coverage run -m pytest {target_path} && coverage report -m"
            else:
                return f"❌ Unknown test type: {test_type}. Available types: pytest, pytest-cov, unittest, jest, mocha, coverage"
            
            
            try:
                req = SessionExecuteRequest(command=cmd)
                result = sandbox.process.execute_session_command(run_id, req)
                output = result.output if hasattr(result, 'output') else str(result)
            except Exception as e:
                return f"❌ Failed to run {test_type}: {str(e)}"
        
        else:
            # Local execution
            if test_type == "pytest":
                cmd = ["pytest", target_path, "-v", "--tb=short"]
            elif test_type == "pytest-cov":
                cmd = ["pytest", target_path, "--cov=.", "--cov-report=term-missing", "--cov-report=html", "-v"]
            elif test_type == "unittest":
                cmd = ["python", "-m", "unittest", "discover", "-s", target_path, "-v"]
            elif test_type == "jest":
                cmd = ["jest", target_path, "--coverage", "--verbose"]
            elif test_type == "mocha":
                cmd = ["mocha", target_path, "--reporter", "spec"]
            elif test_type == "coverage":
                # Run coverage in two steps
                subprocess.run(["coverage", "run", "-m", "pytest", target_path], capture_output=True, text=True, cwd=target_path)
                cmd = ["coverage", "report", "-m"]
            else:
                return f"❌ Unknown test type: {test_type}. Available types: pytest, pytest-cov, unittest, jest, mocha, coverage"
            
            try:
                result = subprocess.run(cmd, capture_output=True, text=True, cwd=target_path, timeout=600)
                output = result.stdout if result.stdout else result.stderr
            except subprocess.TimeoutExpired:
                return f"⏰ Test analysis '{test_type}' timed out after 10 minutes"
            except FileNotFoundError:
                return f"⚠️ Test runner '{test_type}' not found. Please install it first."
            except Exception as e:
                return f"❌ Error running {test_type}: {str(e)}"

        if not output.strip():
            return f"⚠️ {test_type.upper()} test analysis completed - No output generated"
        
        return f"🧪 {test_type.upper()} Test Analysis Results:\n\n{output}"
            
    except Exception as e:
        return f"❌ Test analysis failed: {str(e)}"




def format_security_results(scan_type: str, data: Dict[str, Any]) -> str:
    """Format security scan results for better readability."""
    try:
        if scan_type == "bandit":
            if "results" in data:
                issues = data["results"]
                if not issues:
                    return "✅ BANDIT scan completed - No security issues found"
                
                formatted = f"🚨 BANDIT Security Scan Found {len(issues)} Issues:\n\n"
                for issue in issues:
                    severity = issue.get("issue_severity", "UNKNOWN").upper()
                    confidence = issue.get("issue_confidence", "UNKNOWN").upper()
                    filename = issue.get("filename", "Unknown file")
                    line = issue.get("line_number", "Unknown line")
                    test_id = issue.get("test_id", "")
                    text = issue.get("issue_text", "No description")
                    
                    formatted += f"📍 **{filename}:{line}** [{severity}/{confidence}]\n"
                    formatted += f"   Issue ID: {test_id}\n"
                    formatted += f"   Description: {text}\n\n"
                
                return formatted
                
        elif scan_type == "safety":
            if isinstance(data, list) and len(data) > 0:
                formatted = f"🚨 SAFETY Dependency Scan Found {len(data)} Vulnerabilities:\n\n"
                for vuln in data:
                    pkg = vuln.get("package", "Unknown package")
                    version = vuln.get("installed_version", "Unknown version")
                    vuln_id = vuln.get("vulnerability_id", "")
                    advisory = vuln.get("advisory", "No advisory")
                    
                    formatted += f"📦 **{pkg} v{version}**\n"
                    formatted += f"   Vulnerability ID: {vuln_id}\n"
                    formatted += f"   Advisory: {advisory}\n\n"
                
                return formatted
                
        elif scan_type == "semgrep":
            if "results" in data:
                issues = data["results"]
                if not issues:
                    return "✅ SEMGREP scan completed - No issues found"
                
                formatted = f"🔍 SEMGREP Static Analysis Found {len(issues)} Issues:\n\n"
                for issue in issues:
                    path = issue.get("path", "Unknown file")
                    start_line = issue.get("start", {}).get("line", "Unknown")
                    message = issue.get("extra", {}).get("message", "No description")
                    severity = issue.get("extra", {}).get("severity", "INFO").upper()
                    
                    formatted += f"📍 **{path}:{start_line}** [{severity}]\n"
                    formatted += f"   {message}\n\n"
                
                return formatted
    
    except Exception as e:
        return f"Error formatting results: {str(e)}\n\nRaw output:\n{json.dumps(data, indent=2)}"
    
    # Fallback to raw JSON if no specific formatter
    return f"Raw scan results:\n{json.dumps(data, indent=2)}"


# Export analysis tools
analysis_tools = [
    run_security_scan,
    run_code_quality_scan, 
    run_test_analysis
]