# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Overview

The `src/agents/` directory contains a sophisticated LangGraph-based agent system with three main agents:
- **Scanner Agent**: Comprehensive codebase analysis and security scanning
- **Coder Agent**: AI coding assistant for modifying and improving code
- **Branch Agent**: Orchestrates Scanner → Coder workflow for automated fixes

## Commands

### Using Agents (Recommended)

The best way to interact with agents is through their deployment modules:

```python
# Import agents from deployment.py
from src.agents.scanner.deployment import agent as scanner_agent
from src.agents.coder.deployment import agent as coder_agent
from src.agents.branch.deployment import agent as branch_agent

# Invoke agents with minimal parameters
# Scanner Agent
result = await scanner_agent.invoke({
    "query": "Analyze security vulnerabilities",
    "testing": True  # Optional: fast mode for development
})

# Coder Agent  
result = await coder_agent.invoke({
    "query": "Fix the authentication bug in auth.py",
    "testing": True  # Optional: fast mode
})

# Branch Agent (scan + fix workflow)
result = await branch_agent.invoke({
    "query": "Scan and fix security issues",
    "testing": True  # Optional: fast mode
})
```

Note: All other parameters (repo_id, sandbox_session_id, etc.) are handled by defaults in the deployment configuration.

### Testing

```bash
# Run all agent tests
uv run pytest tests/agents/

# Run specific agent tests
uv run pytest tests/agents/test_scanner.py
uv run pytest tests/agents/test_coder.py
uv run pytest tests/agents/test_branch.py

# Run with coverage
uv run pytest tests/agents/ --cov=src.agents --cov-report=term-missing
```

### Linting & Type Checking

```bash
# Lint agent code
ruff check src/agents/
flake8 src/agents/

# Type checking
mypy src/agents/

# Format code
black src/agents/
isort src/agents/
```

## Architecture

### Agent Hierarchy

```
BaseAgent (base.py)
├── ScannerAgent - Code analysis & vulnerability detection
├── CoderAgent - Code modification & improvement
└── BranchAgent - Orchestrates Scanner → Coder workflow
```

### Base Components

**BaseAgent** (`base.py`):
- Abstract base class for all agents
- Handles sandbox lifecycle management
- Provides `run()` method with automatic cleanup
- Configurable LLM support (Anthropic/OpenAI)

**BaseGraph** (`base.py`):
- Abstract base class for LangGraph implementations
- Provides retry policy for API resilience
- Requires `compile()` method implementation

**LLM Configuration** (`llm_config.py`):
- Centralized LLM instantiation
- Supports Anthropic (Claude) and OpenAI models
- Special handling for o1/o3/o4 models (no temperature)

### Agent Implementations

#### Scanner Agent (`scanner/`)

**Purpose**: Comprehensive codebase analysis with 7 key metrics
- Security vulnerabilities
- Test coverage
- Performance issues
- Code clarity
- Modularity
- Fault resilience
- Agent readiness

**Key Components**:
- `ScannerGraph`: Main workflow with sandbox lifecycle
- `ScanType` enum: Defines analysis metrics
- Metric-specific prompts via Jinja2 templates
- GitHub issue creation for findings

**Workflow**:
1. Start sandbox → Load codebase
2. Execute metric-specific analysis
3. Create GitHub issues for findings
4. Generate comprehensive report
5. Close sandbox

#### Coder Agent (`coder/`)

**Purpose**: AI-powered code modification and improvement

**Key Components**:
- `CoderGraph`: ReAct pattern with tool execution
- Supports both sandbox and local execution modes
- Integrated GitHub PR creation workflow

**Workflow**:
1. Start sandbox (optional)
2. Reasoner node processes query
3. Tool execution for code operations
4. Automatic PR creation after changes
5. Sandbox cleanup

#### Branch Agent (`branch/`)

**Purpose**: Automated scan → fix workflow

**Key Components**:
- `BranchGraph`: Orchestrates subgraphs
- Uses scanner output as coder input
- Jinja2 templates for query generation
- Testing mode for rapid development

**Workflow**:
1. Execute scanner subgraph
2. Pass findings to coder subgraph
3. Coder implements fixes
4. Create PR with changes

### Tools System

#### Tool Categories

1. **Local Tools** (`local_tools.py`):
   - `ripgrep_search`: Fast code search
   - `run_command`: Bash command execution
   - `glob_files`: File pattern matching
   - `list_directory`: Directory exploration
   - `read_file`: File reading
   - `edit_file`: Precise string replacement
   - `write_file`: File creation/overwrite

2. **Sandbox Tools** (`sandbox_tools_state.py`):
   - Sandbox-aware versions of local tools
   - Uses Daytona filesystem API
   - Session-based execution
   - Automatic `/workspace/` path handling

3. **GitHub Tools** (`github_tools.py`):
   - `github_create_issue`: Create formatted issues
   - `github_list_issues`: List with markdown table
   - `github_view_issue`: View issue details
   - `github_pr_create`: Create PRs with DB updates

4. **Analysis Tools** (`analysis_tools.py`):
   - `run_security_scan`: Security analysis (bandit, safety, etc.)
   - `run_code_quality_scan`: Quality checks (flake8, pylint, etc.)
   - `run_test_analysis`: Test execution and coverage

### State Management

Each agent uses TypedDict for state:
- `GraphState` (Coder): Basic state with messages, query, sandbox info
- `GraphState` (Scanner): Includes scan_type, metrics, results
- `BranchState`: Combines scanner results with coder inputs

### Important Patterns

1. **Sandbox Lifecycle**:
   ```python
   # Automatic in BaseAgent.run()
   sandbox, run_id = await create_sandbox(run_id)
   try:
       result = await self._execute(query, config)
   finally:
       await cleanup_sandbox(sandbox, run_id)
   ```

2. **Tool Injection**:
   ```python
   # Tools receive state via InjectedState
   sandbox_session_id: Annotated[str, InjectedState("sandbox_session_id")]
   ```

3. **Retry Policy**:
   ```python
   # Automatic retry for rate limits
   retry_policy = RetryPolicy(
       retry_on=[anthropic.RateLimitError, ...],
       max_attempts=3,
       initial_interval=60.0
   )
   ```

4. **PR Creation Workflow**:
   ```python
   # Always create PR after changes
   1. git checkout -b feature-branch
   2. Make changes and commit
   3. git push origin feature-branch
   4. gh pr create --title "..." --body "..."
   ```

## Development Guidelines

### Adding New Agents

1. Extend `BaseAgent` class
2. Implement `_execute()` method
3. Create corresponding `BaseGraph` subclass
4. Define state using TypedDict
5. Add tools to agent's toolset

### Adding New Tools

1. Use `@tool` decorator
2. Support both local and sandbox execution
3. Include comprehensive docstrings
4. Handle errors gracefully
5. Return markdown-formatted output

### Testing Strategy

- Unit tests for individual components
- Integration tests for agent workflows
- Mock sandbox/API calls for speed
- Test both success and failure paths
- Verify tool execution and state updates

### Best Practices

1. **Always use BaseAgent**: Ensures proper sandbox lifecycle
2. **Implement retry policies**: Handle API rate limits
3. **Use Jinja2 for prompts**: Maintainable prompt templates
4. **Create GitHub issues/PRs**: Track findings and fixes
5. **Type everything**: Use TypedDict for state, type hints everywhere
6. **Test thoroughly**: Both happy and error paths
7. **Document tools**: Clear docstrings with examples

## Configuration

### Environment Variables

```bash
# Required
ANTHROPIC_API_KEY=...
OPENAI_API_KEY=...
GITHUB_TOKEN=...

# Optional
DEFAULT_REPO_ID=990171565  # Default repository for scanning
```

### Model Selection

```python
# Default models
scanner = ScannerAgent(
    model_provider="openai",
    model_name="o4-mini-2025-04-16"
)

coder = CoderAgent(
    model_provider="anthropic",
    model_name="claude-sonnet-4-20250514"
)
```

## Common Workflows

### 1. Scan and Fix Security Issues

```python
branch_agent = BranchAgent()
await branch_agent.graph.astream({
    "scan_type": ScanType.SECURITY,
    "testing": False,
    "repo_id": "990171565"
})
```

### 2. Improve Test Coverage

```python
scanner = ScannerAgent()
# Scan for test gaps
await scanner.run("Analyze test coverage", scan_type=ScanType.TEST_COVERAGE)

# Then use coder to add tests
coder = CoderAgent()
await coder.run("Add unit tests for uncovered functions in auth.py")
```

### 3. Quick Testing Mode

```python
# Fast mode for development
await branch_agent.graph.astream({
    "scan_type": ScanType.SECURITY,
    "testing": True,  # Quick scan, minimal fixes
    "repo_id": "123456"
})
```

## Debugging

### Enable Logging

```python
import logging
logging.basicConfig(level=logging.INFO)
```

### Check Sandbox Status

```python
from utils.sandbox_manager import sandbox_manager
sandbox = sandbox_manager.get_sandbox(session_id)
```

### Inspect State

```python
# In graph nodes
print(f"Current state: {state}")
```

### Tool Debugging

```python
# Tools can be tested standalone
from agents.tools import ripgrep_search
result = await ripgrep_search.ainvoke({"query": "TODO"})
```

FOR TESTING ASYNC WE ALWAYS ADDD pytestmark = pytest.mark.anyio
