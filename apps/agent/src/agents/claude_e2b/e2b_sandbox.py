"""E2B Sandbox utilities for managing E2B sandboxes with Claude Code integration."""

import logging
import os
from typing import Optional, Any
from contextlib import asynccontextmanager
import asyncio
from dotenv import load_dotenv

from e2b_code_interpreter import AsyncSandbox
from utils.github_auth import create_installation_token
from db import get_db

# Configure logger with emoji support
logger = logging.getLogger(__name__)
load_dotenv()


async def get_installation_id_from_repo(repo_id: str) -> int:
    """Get GitHub installation ID from repository ID via database lookup.
    
    Args:
        repo_id: Repository ID to lookup
        
    Returns:
        GitHub installation ID
        
    Raises:
        ValueError: If repo_id not found or missing installation_id
    """
    try:
        db = get_db()
        
        # First get the repository and its integration_id
        repo_result = await db.table('repositories').select(
            'id, integration_id, url, name'
        ).eq('id', repo_id).execute()
        
        if not repo_result.data:
            raise ValueError(f"Repository with id {repo_id} not found")
        
        repo = repo_result.data[0]
        integration_id = repo.get('integration_id')
        
        if not integration_id:
            raise ValueError(f"No integration_id found for repository {repo_id}")
        
        # Now get the GitHub integration data from integrations table
        integration_result = await db.table('integrations').select(
            'data'
        ).eq('id', integration_id).execute()
        
        if not integration_result.data:
            raise ValueError(f"No integration found with id {integration_id}")
        
        integration_data = integration_result.data[0].get('data', {})
        installation_id = integration_data.get('installation_id')
        
        if not installation_id:
            raise ValueError(f"No installation_id found in integration data for integration {integration_id}")
        
        installation_id = int(installation_id)
        logger.debug(f"🔑 Found installation_id {installation_id} for repo {repo_id}")
        
        return installation_id
        
    except Exception as e:
        logger.error(f"Failed to get installation_id for repo {repo_id}: {e}")
        raise


async def create_sandbox(
    repo_id: Optional[str] = None,
    template_id: str = "vcomjjr43nxwhfxodbqm",
    envs: Optional[dict[str, str]] = None,
    timeout: int = 300
) -> AsyncSandbox:
    """Create and return an E2B sandbox with Claude Code and fresh GitHub credentials.
    
    Args:
        repo_id: Repository ID to get GitHub installation_id from database. If None, uses env GH_TOKEN.
        template_id: E2B template ID to use (defaults to our custom template)
        envs: Additional environment variables to set in the sandbox
        timeout: Sandbox creation timeout in seconds (default: 300s/5min)
    
    Returns:
        AsyncSandbox instance
        
    Raises:
        Exception: If sandbox creation or configuration fails
    """
    
    try:
        logger.info(f"🚀 Creating E2B sandbox with template: {template_id}")
        
        # Prepare environment variables
        env_vars = {
            "ANTHROPIC_API_KEY": os.getenv("ANTHROPIC_API_KEY"),
            "WORKSPACE_PATH": "/home/<USER>/workspace",
        }
        
        # Determine repo_id to use - provided parameter or DEFAULT_REPO_ID from environment
        effective_repo_id = repo_id or os.getenv("DEFAULT_REPO_ID")
        
        if not effective_repo_id:
            raise ValueError("No repo_id provided and DEFAULT_REPO_ID not set in environment")
        
        # Always generate fresh GitHub token from database
        try:
            logger.info(f"🔑 Generating fresh GitHub token for repo {effective_repo_id}")
            installation_id = await get_installation_id_from_repo(effective_repo_id)
            fresh_github_token = await create_installation_token(installation_id=installation_id)
            env_vars["GH_TOKEN"] = fresh_github_token
            logger.debug("✅ Generated fresh GitHub token from database")
        except Exception as e:
            logger.error(f"❌ Failed to generate GitHub token from repo {effective_repo_id}: {e}")
            raise ValueError(f"Cannot create sandbox without valid GitHub token. Failed to generate from repo {effective_repo_id}: {e}")
        
        # Merge with any additional envs provided
        if envs:
            env_vars.update(envs)
            logger.debug(f"📦 Added {len(envs)} additional environment variables")
        
        # Create the sandbox
        logger.info(f"📦 Initializing E2B sandbox (timeout: {timeout}s)...")
        sandbox = await AsyncSandbox.create(
            template=template_id,
            envs=env_vars,
            timeout=timeout
        )
        
        logger.info(f"✅ Successfully created E2B sandbox with ID: {sandbox.sandbox_id}")
        
        # Setup workspace and tools
        logger.info("🔧 Setting up workspace and tools...")
        
        setup_commands = [
            # Check current state
            ("pwd", "📍 Checking current directory"),
            ("whoami", "👤 Checking user"),
            ("echo $WORKSPACE_PATH", "📁 Checking workspace path"),
            
            # Create workspace directory
            ("mkdir -p $HOME/workspace", "📂 Creating workspace directory"),
            ("cd $HOME/workspace && pwd", "📍 Verifying workspace"),
            
            # Check if Claude Code is installed
            ("which claude || echo 'Claude not found'", "🔍 Checking Claude Code installation"),
            
            # Install Claude Code if needed (should be in the image already)
            ("npm list -g @anthropic-ai/claude-code || npm install -g @anthropic-ai/claude-code", "📦 Ensuring Claude Code is installed"),
            
            # Configure git if GitHub token is available
            ("git config --global user.email '<EMAIL>' && git config --global user.name 'Backspace Agent'", "🔧 Configuring git") if env_vars.get("GH_TOKEN") else None,
        ]
        
        for cmd_info in setup_commands:
            if cmd_info is None:
                continue
                
            cmd, description = cmd_info
            logger.info(f"{description}...")
            
            try:
                result = await sandbox.commands.run(cmd)
                if result.exit_code == 0:
                    logger.debug(f"   ✅ {result.stdout.strip() if result.stdout else 'Success'}")
                else:
                    logger.warning(f"   ⚠️ Exit code {result.exit_code}: {result.stderr.strip() if result.stderr else 'No error output'}")
            except Exception as e:
                logger.error(f"   ❌ Failed to run '{cmd}': {e}")
        
        # Verify Claude Code is accessible
        logger.info("🤖 Verifying Claude Code installation...")
        claude_check = await sandbox.commands.run("claude --version")
        if claude_check.exit_code == 0:
            logger.info(f"   ✅ Claude Code version: {claude_check.stdout.strip()}")
        else:
            logger.error(f"   ❌ Claude Code not found or not working properly")
            raise Exception("Claude Code CLI is not available in the sandbox")
        
        # Clone the repository into workspace
        logger.info("📂 Cloning repository into workspace...")
        try:
            # Get repository URL from database
            db = get_db()
            repo_result = await db.table('repositories').select('url').eq('id', effective_repo_id).execute()
            repo_url = repo_result.data[0]['url'] if repo_result.data else None
            
            if repo_url:
                logger.info(f"🔗 Repository URL: {repo_url}")
                repo_path = repo_url.replace('https://github.com/', '')
                
                # Use commands.run (has access to env vars) instead of run_code
                # Clone the repository using the fresh GitHub token
                clone_cmd = f"cd /home/<USER>/workspace && git clone https://x-access-token:$<EMAIL>/{repo_path} ."
                logger.info("🔄 Cloning repository...")
                clone_result = await sandbox.commands.run(clone_cmd)
                
                if clone_result.exit_code == 0:
                    logger.info("✅ Repository cloned successfully")
                else:
                    logger.error(f"❌ Failed to clone repository: {clone_result.stderr or clone_result.stdout}")
                
                # Configure git
                git_config_cmd = "cd /home/<USER>/workspace && git config user.email '<EMAIL>' && git config user.name 'Backspace Agent'"
                config_result = await sandbox.commands.run(git_config_cmd)
                if config_result.exit_code == 0:
                    logger.debug("✅ Git configured")
                else:
                    logger.warning(f"⚠️ Git config failed: {config_result.stderr}")
                
                # Verify the clone worked
                ls_result = await sandbox.commands.run("cd /home/<USER>/workspace && ls -la")
                if ls_result.exit_code == 0:
                    logger.info(f"📋 Workspace contents:\n{ls_result.stdout}")
                
                # Check for .git directory
                git_check = await sandbox.commands.run("cd /home/<USER>/workspace && test -d .git && echo 'Git repository found' || echo 'No git repository'")
                if git_check.exit_code == 0:
                    logger.info(f"🔍 Git check: {git_check.stdout.strip()}")
                
            else:
                logger.warning(f"⚠️ No repository URL found for repo_id {effective_repo_id}")
                
        except Exception as e:
            logger.error(f"⚠️ Failed to clone repository: {e}")
            # Don't fail sandbox creation if clone fails
        
        logger.info("🎉 Sandbox setup complete!")
        
        return sandbox
        
    except Exception as e:
        logger.error(f"💥 Failed to create E2B sandbox: {e}")
        raise


async def cleanup_sandbox(sandbox: Optional[AsyncSandbox]) -> None:
    """Clean up the E2B sandbox.
    
    Args:
        sandbox: The sandbox instance to clean up
        
    Raises:
        Exception: If cleanup fails
    """
    if sandbox:
        logger.info(f"🧹 Cleaning up E2B sandbox (ID: {sandbox.sandbox_id})...")
        
        try:
            await sandbox.kill()
            logger.info("✅ Successfully cleaned up E2B sandbox")
        except Exception as e:
            logger.error(f"⚠️ Error during sandbox cleanup: {e}")
            raise


@asynccontextmanager
async def sandbox_context(
    repo_id: Optional[str] = None,
    template_id: str = "vcomjjr43nxwhfxodbqm",
    envs: Optional[dict[str, str]] = None,
    timeout: int = 300
):
    """Context manager for E2B sandbox lifecycle management.
    
    Automatically generates a UUID for the session and handles cleanup.
    
    Args:
        repo_id: Repository ID to get GitHub installation_id from database. If None, uses env GH_TOKEN.
        template_id: E2B template ID to use
        envs: Additional environment variables to set
        timeout: Sandbox creation timeout in seconds (default: 300s/5min)
        
    Yields:
        sandbox: The sandbox instance with _session_id attribute
        
    Example:
        async with sandbox_context(timeout=1000) as sandbox:
            # Use sandbox here with 1000s timeout
            result = await sandbox.commands.run("ls")
        # Sandbox automatically cleaned up
    """
    sandbox = None
    
    try:
        logger.info("🌟 Entering sandbox context...")
        sandbox = await create_sandbox(repo_id=repo_id, template_id=template_id, envs=envs, timeout=timeout)
        yield sandbox
    except Exception as e:
        logger.error(f"💥 Error in sandbox context: {e}")
        raise
    finally:
        if sandbox:
            logger.info("🌙 Exiting sandbox context...")
            await cleanup_sandbox(sandbox)


async def run_command_in_sandbox(
    sandbox: AsyncSandbox,
    command: str,
    cwd: Optional[str] = None,
    timeout: int = 120
) -> dict[str, Any]:
    """Run a command in the sandbox with detailed logging.
    
    Args:
        sandbox: The E2B sandbox instance
        command: Command to run
        cwd: Working directory (optional)
        timeout: Command timeout in seconds
        
    Returns:
        Dict with exit_code, stdout, stderr, and duration
    """
    logger.info(f"⚡ Running command: {command[:100]}{'...' if len(command) > 100 else ''}")
    if cwd:
        logger.debug(f"   📁 Working directory: {cwd}")
    
    import time
    start_time = time.time()
    
    try:
        result = await sandbox.commands.run(
            command,
            cwd=cwd,
            timeout=timeout
        )
        
        duration = time.time() - start_time
        
        if result.exit_code == 0:
            logger.info(f"   ✅ Command succeeded in {duration:.2f}s")
        else:
            logger.warning(f"   ⚠️ Command failed with exit code {result.exit_code} in {duration:.2f}s")
        
        if result.stdout:
            logger.debug(f"   📤 STDOUT: {result.stdout[:200]}{'...' if len(result.stdout) > 200 else ''}")
        if result.stderr:
            logger.debug(f"   📥 STDERR: {result.stderr[:200]}{'...' if len(result.stderr) > 200 else ''}")
        
        return {
            "exit_code": result.exit_code,
            "stdout": result.stdout,
            "stderr": result.stderr,
            "duration": duration
        }
        
    except Exception as e:
        duration = time.time() - start_time
        logger.error(f"   💥 Command failed after {duration:.2f}s: {e}")
        raise