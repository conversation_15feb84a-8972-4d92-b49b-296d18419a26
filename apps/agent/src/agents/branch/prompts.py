from jinja2 import Template

# Base queries for each scan type
SCAN_TYPE_QUERIES = {
    "security": "Scan for security vulnerabilities and issues",
    "test_coverage": "Analyze test coverage and identify gaps", 
    "performance": "Analyze performance issues and bottlenecks",
    "clarity": "Evaluate code clarity and readability",
    "modularity": "Assess code modularity and architecture",
    "fault_resilience": "Check fault tolerance and error handling",
    "agent_readiness": "Evaluate AI agent compatibility",
    "comprehensive": "Perform comprehensive 7-metric analysis"
}

# Scanner query template with testing mode
SCANNER_QUERY_TEMPLATE = Template("""{{ base_query }}
{% if testing %}

⚡ TESTING MODE - URGENT INSTRUCTIONS ⚡
- SCAN ONLY ONE FILE (pick the most obvious/important one)
- DO EXACTLY ONE ACTION and return results IMMEDIATELY
- NO deep analysis - just quick findings
- SPEED IS CRITICAL - Be fast or face consequences!
- Maximum 30 seconds execution time
- Return basic findings NOW!

This is a SPEED TEST. Prioritize velocity over completeness.
{% endif %}""")

# Coder query template with testing mode
CODER_QUERY_TEMPLATE = Template("""{% if testing %}Based on scan results, make MINIMAL changes FAST:

{{ scan_results }}

⚡ TESTING MODE - ULTRA FAST ⚡
- Make ONE small fix only
- NO comprehensive changes
- NO extensive testing
- Create simple PR immediately
- Maximum 20 seconds execution
- SPEED OVER PERFECTION!

Do the absolute minimum and return NOW!{% else %}Based on the following code analysis results, please implement fixes and create a pull request:

{{ scan_results }}

Please:
1. Analyze the identified issues
2. Implement necessary code fixes
3. Create a pull request with the changes
4. Ensure all changes follow best practices{% endif %}""")

def get_scanner_query(scan_type: str, testing: bool = False) -> str:
    """
    Generate a scanner query using Jinja2 templating.
    
    Args:
        scan_type: The scan type (e.g., 'security', 'test_coverage')
        testing: Whether to enable aggressive testing mode
        
    Returns:
        The rendered scanner query string
    """
    base_query = SCAN_TYPE_QUERIES.get(scan_type, "Perform comprehensive code analysis")
    
    return SCANNER_QUERY_TEMPLATE.render(
        base_query=base_query,
        testing=testing
    )

def get_coder_query(scan_results: str, testing: bool = False) -> str:
    """
    Generate a coder query using Jinja2 templating.
    
    Args:
        scan_results: Results from the scanner analysis
        testing: Whether to enable aggressive testing mode
        
    Returns:
        The rendered coder query string
    """
    return CODER_QUERY_TEMPLATE.render(
        scan_results=scan_results,
        testing=testing
    )