"""State definitions for Claude-Coder graph."""

from typing import Optional, Dict, Any, Set, TYPE_CHECKING, Union
from typing_extensions import TypedDict
from enum import Enum

if TYPE_CHECKING:
    from e2b_code_interpreter import AsyncSandbox
    from daytona import Sandbox as DaytonaSandbox
    import modal
    from agents.claude_e2b.claude import ClaudeSession
else:
    # Fallback types for runtime
    AsyncSandbox = Any
    DaytonaSandbox = Any
    modal = Any
    ClaudeSession = Any


class SandboxType(Enum):
    """Enum for sandbox types."""
    E2B = "e2b"
    DAYTONA = "daytona"
    MODAL = "modal"


class SkipPhase(Enum):
    """Enum for phases that can be skipped during testing."""
    MODULARIZE = "modularize"
    BUILD = "build"
    TEST = "test"
    DOC = "doc"


class ClaudeCoderState(TypedDict):
    """State for the Claude-Coder graph execution."""
    
    # Sandbox management
    sandbox_type: SandboxType  # Type of sandbox to use
    sandbox: Optional[Union[AsyncSandbox, DaytonaSandbox, Any]]  # Sandbox instance (E2B, Daytona, or Modal)
    sandbox_run_id: Optional[str]  # Run ID for Daytona sandbox
    
    # Git configuration
    branch_name: str  # Current working branch
    base_branch: str  # Base branch for PR (default: "main")
    repo_path: str  # Repository root path
    repo_id: Optional[str]  # Repository ID (needed for Daytona)
    
    # Current execution phase
    current_phase: str  # modularize/build/test/doc
    
    # Results from each phase
    phase_results: Dict[str, ClaudeSession]  # Results from each Claude Code execution
    
    # PR management
    pr_url: Optional[str]  # PR URL once created/updated
    
    # Error tracking
    error: Optional[str]
    
    # Additional configuration
    claude_options: Dict[str, Any]  # Options to pass to Claude Code CLI
    
    # Skip configuration for testing
    skip_phases: Optional[Set[SkipPhase]]  # Phases to skip during execution