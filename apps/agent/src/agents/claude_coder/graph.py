"""LangGraph implementation for Claude-Coder agent."""

import logging
import os
import subprocess
from typing import Any, Dict, Optional

from agents.base import BaseGraph
from agents.claude_coder.states import ClaudeCoderState, SkipPhase, SandboxType
from agents.claude_coder.prompts import (
    get_modularize_prompts,
    get_build_prompts,
    get_test_prompts,
    get_doc_prompts
)
# Import all sandbox implementations
from agents.claude_e2b.e2b_sandbox import create_sandbox as create_e2b_sandbox, cleanup_sandbox as cleanup_e2b_sandbox
from agents.claude_e2b.claude import run_claude_in_sandbox as run_claude_in_e2b
from agents.claude_daytona.daytona_sandbox import create_dynamic_sandbox, cleanup_sandbox as cleanup_daytona_sandbox, run_command_in_sandbox
from daytona import SessionExecuteRequest
from agents.claude_modal.modal_sandbox import create_sandbox as create_modal_sandbox, cleanup_sandbox as cleanup_modal_sandbox
from agents.claude_modal.claude import run_claude_with_tracing as run_claude_in_modal, stream_claude_in_sandbox as stream_claude_in_modal
from langchain_core.language_models.chat_models import BaseChatModel
from langchain_core.tools import BaseTool
from langgraph.graph import END, START
from langgraph.graph.state import CompiledGraph, StateGraph
from langchain_core.runnables.config import RunnableConfig

logger = logging.getLogger(__name__)


class ClaudeCoderGraph(BaseGraph):
    """Graph for automated development pipeline using Claude Code."""
    
    def __init__(self, llm: BaseChatModel = None, tools: list[BaseTool] = None):
        """Initialize Claude-Coder graph.
        
        Note: llm and tools are not used in this graph as we use Claude Code directly.
        """
        # We don't use LLM/tools in this graph, but BaseGraph requires them
        # Use dummy values if not provided
        if llm is None:
            from agents.llm_config import get_llm
            llm = get_llm(model_provider="anthropic", model_name="claude-sonnet-4-20250514")
        
        super().__init__(llm=llm, tools=tools or [])
    
    # =================================== HELPER METHODS ====================================
    
    def _should_skip_phase(self, state: ClaudeCoderState, phase: SkipPhase) -> bool:
        """Check if a phase should be skipped based on skip_phases configuration."""
        skip_phases = state.get("skip_phases")
        if skip_phases is None:
            return False
        return phase in skip_phases
    
    async def _create_sandbox(self, sandbox_type: SandboxType, repo_id: Optional[str] = None, timeout: int = 600, claude_options: Optional[Dict[str, Any]] = None) -> tuple[Any, Optional[str]]:
        """Create a sandbox of the specified type.
        
        Args:
            sandbox_type: Type of sandbox to create (E2B, Daytona, or Modal)
            repo_id: Repository ID (required for Daytona and Modal)
            timeout: Timeout for sandbox creation
            
        Returns:
            Tuple of (sandbox, run_id) - run_id is only needed for Daytona
        """
        if sandbox_type == SandboxType.E2B:
            sandbox = await create_e2b_sandbox(
                repo_id=repo_id,
                timeout=timeout
            )
            return sandbox, None
        elif sandbox_type == SandboxType.DAYTONA:
            sandbox, run_id = await create_dynamic_sandbox(
                repo_id=repo_id,
                timeout=timeout,
                initial_path="apps/landing"  # Default to apps/landing for Daytona
            )
            return sandbox, run_id
        else:  # Modal
            # Check if snapshots should be disabled
            use_snapshot = True
            if claude_options:
                use_snapshot = claude_options.get("use_snapshot", "true").lower() != "false"
            
            sandbox = await create_modal_sandbox(
                repo_id=repo_id,
                initial_path="apps/landing",  # Default to apps/landing for Modal
                timeout=timeout,
                use_snapshot=use_snapshot  # Configurable snapshots
            )
            return sandbox, None
    
    async def _cleanup_sandbox(self, sandbox: Any, sandbox_type: SandboxType, run_id: Optional[str] = None) -> None:
        """Clean up a sandbox of the specified type.
        
        Args:
            sandbox: Sandbox instance
            sandbox_type: Type of sandbox
            run_id: Run ID (only needed for Daytona)
        """
        if sandbox_type == SandboxType.E2B:
            await cleanup_e2b_sandbox(sandbox)
        elif sandbox_type == SandboxType.DAYTONA:
            await cleanup_daytona_sandbox(sandbox, run_id)
        else:  # Modal
            await cleanup_modal_sandbox(sandbox)
    
    async def _run_claude_in_sandbox(
        self,
        sandbox: Any,
        prompt: str,
        sandbox_type: SandboxType,
        run_id: Optional[str] = None,
        claude_options: Optional[Dict[str, Any]] = None,
        timeout: int = 3600,
        config: Optional[RunnableConfig] = None
    ) -> Any:
        """Run Claude Code in the specified sandbox type.
        
        Args:
            sandbox: Sandbox instance
            prompt: Prompt for Claude
            sandbox_type: Type of sandbox
            run_id: Run ID (only needed for Daytona)
            claude_options: Options for Claude Code
            timeout: Timeout for execution
            
        Returns:
            ClaudeSession or equivalent result
        """
        if sandbox_type == SandboxType.E2B:
            return await run_claude_in_e2b(
                sandbox=sandbox,
                prompt=prompt,
                claude_options=claude_options,
                timeout=timeout
            )
        elif sandbox_type == SandboxType.DAYTONA:
            # For Daytona, we need to run Claude Code via command
            # Build Claude command
            cmd_parts = ["claude-code"]
            
            # Add any additional options
            if claude_options:
                max_turns = claude_options.get("max-turns", "10")
                cmd_parts.extend(["--max-turns", str(max_turns)])
            
            # Create the full command with the prompt
            escaped_prompt = prompt.replace("'", "'\"'\"'")
            full_command = f"echo '{escaped_prompt}' | {' '.join(cmd_parts)}"
            
            # Run in Daytona sandbox
            result = await run_command_in_sandbox(
                sandbox=sandbox,
                run_id=run_id,
                command=full_command,
                timeout=timeout
            )
            
            # Create a mock ClaudeSession-like object for compatibility
            from agents.claude_e2b.claude import ClaudeSession, ClaudeOutput
            session = ClaudeSession(
                session_id=f"daytona-{run_id}",
                prompt=prompt
            )
            
            # Add the output as a single output
            output = ClaudeOutput(
                timestamp=0,
                type="execution_result",
                content=result
            )
            session.add_output(output)
            session.finalize(success=result.get("exit_code", 1) == 0, error=result.get("output") if result.get("exit_code", 1) != 0 else None)
            
            return session
        else:  # Modal
            # Get parent run for tracing
            from langsmith.run_helpers import get_current_run_tree
            parent_run = None
            try:
                parent_run = get_current_run_tree()
                if parent_run:
                    logger.info(f"🔍 LangGraph parent run captured: {parent_run.id}")
            except Exception as e:
                logger.warning(f"⚠️ Could not get parent run for tracing: {e}")

            return await run_claude_in_modal(
                sandbox=sandbox,
                prompt=prompt,
                claude_options=claude_options,
                timeout=timeout,
                enable_tracing=True,
                parent_run=parent_run
            )
    
    async def _get_current_branch(self) -> str:
        """Get the current git branch name."""
        try:
            import asyncio
            proc = await asyncio.create_subprocess_exec(
                "git", "branch", "--show-current",
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            stdout, stderr = await proc.communicate()
            
            if proc.returncode == 0:
                return stdout.decode().strip()
            else:
                logger.error(f"Failed to get current git branch: {stderr.decode()}")
                return "main"
        except Exception as e:
            logger.error(f"Failed to get current git branch: {e}")
            return "main"
    
    async def _get_repo_path(self) -> str:
        """Get the repository root path."""
        try:
            import asyncio
            proc = await asyncio.create_subprocess_exec(
                "git", "rev-parse", "--show-toplevel",
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            stdout, stderr = await proc.communicate()
            
            if proc.returncode == 0:
                return stdout.decode().strip()
            else:
                logger.error(f"Failed to get repository root: {stderr.decode()}")
                return os.getcwd()
        except Exception as e:
            logger.error(f"Failed to get repository root: {e}")
            return os.getcwd()
    
    # =================================== NODES ====================================
    
    async def create_sandbox_node(self, state: ClaudeCoderState, config: RunnableConfig) -> Dict[str, Any]:
        """Create sandbox (E2B or Daytona) and initialize git state."""
        sandbox_type = state.get("sandbox_type", SandboxType.E2B)  # Default to E2B if not specified
        logger.info(f"🚀 Creating {sandbox_type.value.upper()} sandbox for Claude-Coder session...")
        
        try:
            # Auto-detect git information if not provided
            branch_name = state.get("branch_name") or await self._get_current_branch()
            base_branch = state.get("base_branch", "main")
            repo_path = state.get("repo_path") or await self._get_repo_path()
            repo_id = state.get("repo_id", os.getenv("DEFAULT_REPO_ID"))
            
            logger.info(f"📍 Working on branch: {branch_name}")
            logger.info(f"📍 Base branch: {base_branch}")
            logger.info(f"📍 Repository: {repo_path}")
            logger.info(f"📍 Sandbox type: {sandbox_type.value}")
            
            # Create sandbox based on type
            claude_options = state.get("claude_options", {})
            sandbox, run_id = await self._create_sandbox(
                sandbox_type=sandbox_type,
                repo_id=repo_id,
                timeout=3600,
                claude_options=claude_options
            )
            
            # For E2B, fetch latest changes and checkout to the specified branch
            if sandbox_type == SandboxType.E2B:
                checkout_cmd = f"cd /home/<USER>/workspace && git fetch origin && (git checkout -B {branch_name} origin/{branch_name} || git checkout -b {branch_name})"
                result = await sandbox.commands.run(checkout_cmd)
                
                if result.exit_code != 0:
                    logger.warning(f"Failed to checkout branch {branch_name}: {result.stderr}")
            # For Daytona, the repo is already cloned and we're in the right directory
            
            return {
                "sandbox": sandbox,
                "sandbox_type": sandbox_type,
                "sandbox_run_id": run_id,
                "branch_name": branch_name,
                "base_branch": base_branch,
                "repo_path": repo_path,
                "current_phase": "modularize",
                "phase_results": {},
                "skip_phases": state.get("skip_phases", set()),  # Initialize skip_phases if not provided
                "error": None
            }
            
        except Exception as e:
            logger.error(f"❌ Failed to create sandbox: {e}")
            return {
                "error": f"Failed to create sandbox: {str(e)}",
                "current_phase": "error"
            }
    
    async def modularize_node(self, state: ClaudeCoderState, config: RunnableConfig) -> Dict[str, Any]:
        """Run modularization phase with Claude Code."""
        logger.info("🔧 Starting MODULARIZE phase...")
        
        # Check if this phase should be skipped
        if self._should_skip_phase(state, SkipPhase.MODULARIZE):
            logger.info("⏭️ MODULARIZE phase skipped (skip_phases configuration)")
            phase_results = state["phase_results"].copy()
            phase_results["modularize"] = "SKIPPED"
            return {
                "phase_results": phase_results,
                "current_phase": "modularize_complete"
            }
        
        try:
            sandbox = state["sandbox"]
            sandbox_type = state.get("sandbox_type", SandboxType.E2B)
            sandbox_run_id = state.get("sandbox_run_id")
            branch_name = state["branch_name"]
            base_branch = state["base_branch"]
            repo_path = state["repo_path"]
            
            # Get prompts
            system_prompt, user_prompt = get_modularize_prompts(
                branch_name=branch_name,
                base_branch=base_branch,
                repo_path=repo_path
            )
            
            # Combine prompts for Claude Code
            full_prompt = f"{user_prompt}\n\nIMPORTANT: {system_prompt}"
            
            # Run Claude Code
            session = await self._run_claude_in_sandbox(
                sandbox=sandbox,
                prompt=full_prompt,
                sandbox_type=sandbox_type,
                run_id=sandbox_run_id,
                claude_options=state.get("claude_options", {"max-turns": "10"}),
                timeout=3600,  # 10 minutes
                config=config  # 🎯 Pass config for trace nesting
            )
            
            # Store results
            phase_results = state["phase_results"].copy()
            phase_results["modularize"] = session
            
            if not session.success:
                return {
                    "error": f"Modularize phase failed: {session.error}",
                    "phase_results": phase_results,
                    "current_phase": "error"
                }
            
            logger.info("✅ MODULARIZE phase completed successfully")
            return {
                "phase_results": phase_results,
                "current_phase": "modularize_complete"
            }
            
        except Exception as e:
            logger.error(f"❌ Modularize phase error: {e}")
            return {
                "error": f"Modularize phase error: {str(e)}",
                "current_phase": "error"
            }
    
    async def build_node(self, state: ClaudeCoderState, config: RunnableConfig) -> Dict[str, Any]:
        """Run build phase with Claude Code."""
        logger.info("🏗️ Starting BUILD phase...")
        
        # Check if this phase should be skipped
        if self._should_skip_phase(state, SkipPhase.BUILD):
            logger.info("⏭️ BUILD phase skipped (skip_phases configuration)")
            phase_results = state["phase_results"].copy()
            phase_results["build"] = "SKIPPED"
            return {
                "phase_results": phase_results,
                "current_phase": "build_complete"
            }
        
        try:
            sandbox = state["sandbox"]
            sandbox_type = state.get("sandbox_type", SandboxType.E2B)
            sandbox_run_id = state.get("sandbox_run_id")
            branch_name = state["branch_name"]
            repo_path = state["repo_path"]
            
            # Get prompts
            system_prompt, user_prompt = get_build_prompts(
                branch_name=branch_name,
                repo_path=repo_path
            )
            
            # Combine prompts
            full_prompt = f"{user_prompt}\n\nIMPORTANT: {system_prompt}"
            
            # Run Claude Code
            session = await self._run_claude_in_sandbox(
                sandbox=sandbox,
                prompt=full_prompt,
                sandbox_type=sandbox_type,
                run_id=sandbox_run_id,
                claude_options=state.get("claude_options", {"max-turns": "15"}),
                timeout=3600,
                config=config  # 🎯 Pass config for trace nesting
            )
            
            # Store results
            phase_results = state["phase_results"].copy()
            phase_results["build"] = session
            
            if not session.success:
                return {
                    "error": f"Build phase failed: {session.error}",
                    "phase_results": phase_results,
                    "current_phase": "error"
                }
            
            logger.info("✅ BUILD phase completed successfully")
            return {
                "phase_results": phase_results,
                "current_phase": "build_complete"
            }
            
        except Exception as e:
            logger.error(f"❌ Build phase error: {e}")
            return {
                "error": f"Build phase error: {str(e)}",
                "current_phase": "error"
            }
    
    async def test_node(self, state: ClaudeCoderState, config: RunnableConfig) -> Dict[str, Any]:
        """Run test phase with Claude Code."""
        logger.info("🧪 Starting TEST phase...")
        
        # Check if this phase should be skipped
        if self._should_skip_phase(state, SkipPhase.TEST):
            logger.info("⏭️ TEST phase skipped (skip_phases configuration)")
            phase_results = state["phase_results"].copy()
            phase_results["test"] = "SKIPPED"
            return {
                "phase_results": phase_results,
                "current_phase": "test_complete"
            }
        
        try:
            sandbox = state["sandbox"]
            sandbox_type = state.get("sandbox_type", SandboxType.E2B)
            sandbox_run_id = state.get("sandbox_run_id")
            branch_name = state["branch_name"]
            base_branch = state["base_branch"]
            
            # Get prompts
            system_prompt, user_prompt = get_test_prompts(
                branch_name=branch_name,
                base_branch=base_branch
            )
            
            # Combine prompts
            full_prompt = f"{user_prompt}\n\nIMPORTANT: {system_prompt}"
            
            # Run Claude Code
            session = await self._run_claude_in_sandbox(
                sandbox=sandbox,
                prompt=full_prompt,
                sandbox_type=sandbox_type,
                run_id=sandbox_run_id,
                claude_options=state.get("claude_options", {"max-turns": "20"}),
                timeout=3600,  # 20 minutes for testing
                config=config  # 🎯 Pass config for trace nesting
            )
            
            # Store results
            phase_results = state["phase_results"].copy()
            phase_results["test"] = session
            
            if not session.success:
                return {
                    "error": f"Test phase failed: {session.error}",
                    "phase_results": phase_results,
                    "current_phase": "error"
                }
            
            logger.info("✅ TEST phase completed successfully")
            return {
                "phase_results": phase_results,
                "current_phase": "test_complete"
            }
            
        except Exception as e:
            logger.error(f"❌ Test phase error: {e}")
            return {
                "error": f"Test phase error: {str(e)}",
                "current_phase": "error"
            }
    
    async def doc_node(self, state: ClaudeCoderState, config: RunnableConfig) -> Dict[str, Any]:
        """Run documentation phase with Claude Code and create PR."""
        logger.info("📝 Starting DOC phase...")
        
        # Check if this phase should be skipped
        if self._should_skip_phase(state, SkipPhase.DOC):
            logger.info("⏭️ DOC phase skipped (skip_phases configuration)")
            phase_results = state["phase_results"].copy()
            phase_results["doc"] = "SKIPPED"
            return {
                "phase_results": phase_results,
                "current_phase": "doc_complete"
            }
        
        try:
            sandbox = state["sandbox"]
            sandbox_type = state.get("sandbox_type", SandboxType.E2B)
            sandbox_run_id = state.get("sandbox_run_id")
            branch_name = state["branch_name"]
            base_branch = state["base_branch"]
            
            # Get prompts
            system_prompt, user_prompt = get_doc_prompts(
                branch_name=branch_name,
                base_branch=base_branch
            )
            
            # Combine prompts
            full_prompt = f"{user_prompt}\n\nIMPORTANT: {system_prompt}"
            
            # Run Claude Code
            session = await self._run_claude_in_sandbox(
                sandbox=sandbox,
                prompt=full_prompt,
                sandbox_type=sandbox_type,
                run_id=sandbox_run_id,
                claude_options=state.get("claude_options", {"max-turns": "15"}),
                timeout=3600,  # 15 minutes
                config=config  # 🎯 Pass config for trace nesting
            )
            
            # Store results
            phase_results = state["phase_results"].copy()
            phase_results["doc"] = session
            
            if not session.success:
                return {
                    "error": f"Doc phase failed: {session.error}",
                    "phase_results": phase_results,
                    "current_phase": "error"
                }
            
            # Extract PR URL if created
            pr_url = None
            for output in session.outputs:
                if output.type == "claude_message" and "github.com" in str(output.content) and "/pull/" in str(output.content):
                    # Simple extraction of PR URL
                    content = str(output.content)
                    start = content.find("https://github.com")
                    if start != -1:
                        end = content.find(" ", start)
                        if end == -1:
                            end = len(content)
                        pr_url = content[start:end].strip()
                        break
            
            logger.info("✅ DOC phase completed successfully")
            if pr_url:
                logger.info(f"📌 PR created/updated: {pr_url}")
            
            return {
                "phase_results": phase_results,
                "pr_url": pr_url,
                "current_phase": "doc_complete"
            }
            
        except Exception as e:
            logger.error(f"❌ Doc phase error: {e}")
            return {
                "error": f"Doc phase error: {str(e)}",
                "current_phase": "error"
            }
    
    async def cleanup_sandbox_node(self, state: ClaudeCoderState, config: RunnableConfig) -> Dict[str, Any]:
        """Clean up sandbox (E2B or Daytona)."""
        logger.info("🧹 Cleaning up sandbox...")
        
        sandbox = state.get("sandbox")
        sandbox_type = state.get("sandbox_type", SandboxType.E2B)
        sandbox_run_id = state.get("sandbox_run_id")
        
        if sandbox:
            try:
                await self._cleanup_sandbox(
                    sandbox=sandbox,
                    sandbox_type=sandbox_type,
                    run_id=sandbox_run_id
                )
                logger.info(f"✅ {sandbox_type.value.upper()} sandbox cleaned up successfully")
            except Exception as e:
                logger.warning(f"⚠️ Error during sandbox cleanup: {e}")
        
        return {
            "sandbox": None,
            "sandbox_run_id": None,
            "current_phase": "complete"
        }
    
    # =================================== EDGE CONDITIONS ====================================
    
    def should_continue(self, state: ClaudeCoderState) -> str:
        """Determine next step based on current phase."""
        current_phase = state.get("current_phase", "")
        error = state.get("error")
        
        logger.info(f"🔍 should_continue: current_phase='{current_phase}', error='{error}'")
        
        if current_phase == "error" or error:
            logger.info("➡️ Routing to cleanup due to error")
            return "cleanup"
        
        # Map completion states to next phases
        phase_transitions = {
            "modularize_complete": "build",
            "build_complete": "test", 
            "test_complete": "doc",
            "doc_complete": "cleanup",
            "cleanup": "cleanup"  # cleanup goes to END via direct edge
        }
        
        next_phase = phase_transitions.get(current_phase, "cleanup")
        logger.info(f"➡️ Routing from '{current_phase}' to '{next_phase}'")
        return next_phase
    
    # =================================== COMPILE ====================================
    
    def compile(self) -> CompiledGraph:
        """Compile the Claude-Coder graph."""
        builder = StateGraph(ClaudeCoderState)
        
        # Add all nodes
        builder.add_node("create_sandbox", self.create_sandbox_node)
        builder.add_node("modularize", self.modularize_node)
        builder.add_node("build", self.build_node)
        builder.add_node("test", self.test_node)
        builder.add_node("doc", self.doc_node)
        builder.add_node("cleanup", self.cleanup_sandbox_node)
        
        # Define the flow
        builder.add_edge(START, "create_sandbox")
        builder.add_edge("create_sandbox", "modularize")
        
        # Add conditional edges from each phase
        # Each node sets current_phase to the NEXT phase
        # should_continue() returns where to go based on that next phase
        builder.add_conditional_edges(
            "modularize",
            self.should_continue,
            {
                "build": "build",
                "cleanup": "cleanup"
            }
        )
        
        builder.add_conditional_edges(
            "build", 
            self.should_continue,
            {
                "test": "test",
                "cleanup": "cleanup"
            }
        )
        
        builder.add_conditional_edges(
            "test",
            self.should_continue,
            {
                "doc": "doc", 
                "cleanup": "cleanup"
            }
        )
        
        builder.add_conditional_edges(
            "doc",
            self.should_continue,
            {
                "cleanup": "cleanup"
            }
        )
        
        builder.add_edge("cleanup", END)
        
        return builder.compile()