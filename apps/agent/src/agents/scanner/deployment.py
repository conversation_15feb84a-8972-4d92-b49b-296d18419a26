from agents.scanner.agent import ScannerAgent

# Get the compiled graph for deployment
agent = ScannerAgent().graph  # returns CompiledGraph

# Example usage with scan types:
# 
# For targeted security scan:
# async for chunk in agent.astream({
#     "query": "Scan for security vulnerabilities",
#     "scan_type": ScanType.SECURITY
# }):
#     print(chunk)
#
# For comprehensive scan (all 7 metrics):
# async for chunk in agent.astream({
#     "query": "Perform comprehensive analysis",
#     "scan_type": ScanType.COMPREHENSIVE  # or None
# }):
#     print(chunk)
#
# For test coverage specific scan:
# async for chunk in agent.astream({
#     "query": "Analyze test coverage",
#     "scan_type": ScanType.TEST_COVERAGE
# }):
#     print(chunk)