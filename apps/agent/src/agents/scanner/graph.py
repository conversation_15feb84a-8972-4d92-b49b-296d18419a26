from agents.scanner.states import GraphState, ScanType
from langchain_core.messages import HumanMessage
from typing import Any
from langchain_core.language_models.chat_models import BaseChatModel
from langgraph.graph.state import CompiledGraph, StateGraph
from langgraph.prebuilt import ToolNode
from langgraph.graph import END, START
from langchain_core.runnables.config import RunnableConfig
from supabase import create_client, Client

from utils.sandbox_manager import sandbox_manager
from db import db_manager

import logging
import json
from datetime import datetime

logger = logging.getLogger(__name__)

from agents.scanner.prompts import (
    REASONER_SYSTEM_MESSAGE, 
    USER_PROMPT_TEMPLATE,
    get_metric_prompt,
    get_metric_user_prompt,
    METRIC_CONFIGS
)

from dotenv import load_dotenv
from langchain_core.tools import BaseTool

from agents.base import BaseGraph

load_dotenv()

class ScannerGraph(BaseGraph):
    def __init__(self, llm: BaseChatModel, tools: list[BaseTool]):
        super().__init__(llm=llm, tools=tools)

    # =================================== NODES ====================================

    async def start_sandbox(self, state: GraphState, config: RunnableConfig) -> dict[str, Any]:
        """Initialize and start a Daytona sandbox for the session.
        
        Args:
            state: Current graph state
            config: Runtime configuration for the graph
            
        Returns:
            Updated state with sandbox session_id
        """
        logger.info("🚀 Starting sandbox for deep scan agent session...")
        
        # Ensure database connection is established
        try:
            await db_manager.connect()
            logger.info("✅ Database connection established")
        except Exception as e:
            logger.error(f"❌ Failed to connect to database: {e}")
            # Continue anyway - some tools might still work without database
        
        try:
            # Get repo_id from state, with fallback to default
            repo_id = state.get("repo_id") or "990171565"  # default to backspace-monorepo
            logger.info(f"Using repo_id: {repo_id}")
            
            session_id, sandbox = await sandbox_manager.create_session(repo_id=repo_id)
            logger.info(f"✅ Sandbox started successfully with session {session_id}")
            
            return {
                "sandbox_session_id": session_id,
            }
        except Exception as e:
            logger.error(f"❌ Failed to start sandbox: {e}")
            return {
                "sandbox_session_id": None
            }


    async def close_sandbox(self, state: GraphState, config: RunnableConfig) -> dict[str, Any]:
        """Clean up and close the Daytona sandbox.
        
        Args:
            state: Current graph state containing sandbox session ID
            config: Runtime configuration for the graph
            
        Returns:
            Updated state with sandbox cleanup status
        """
        session_id = state.get("sandbox_session_id")
        
        try:
            if session_id:
                success = await sandbox_manager.cleanup_session(session_id)
                if success:
                    logger.info("✅ Sandbox cleaned up successfully")
                else:
                    logger.warning("⚠️ Sandbox cleanup had issues")
            
            return {
                "sandbox_session_id": None,
            }
        except Exception as e:
            logger.error(f"⚠️ Error during sandbox cleanup: {e}")
            return {
                "sandbox_session_id": None,
            }

    # Reasoner node
    async def reasoner(self, state: GraphState) -> dict[str, Any]:    
        messages = state["messages"]
        query = state["query"]
        scan_type = state.get("scan_type")
        is_first_message = not messages and query
        
        # Select the appropriate system and user prompts based on scan_type
        # Handle both string and enum inputs for comparison
        is_comprehensive = (
            scan_type == ScanType.COMPREHENSIVE or 
            scan_type == "comprehensive" or 
            scan_type is None
        )
        
        if scan_type and not is_comprehensive:
            # Use metric-specific prompts for targeted scan
            # Handle both string and enum inputs
            if isinstance(scan_type, str):
                metric_key = scan_type
            else:
                metric_key = scan_type.value
            
            if metric_key in METRIC_CONFIGS:
                system_message = get_metric_prompt(metric_key)
                logger.info(f"🎯 Using {metric_key} specific prompt for targeted scan")
                
                # Use metric-specific user prompt
                if is_first_message:
                    formatted_query = get_metric_user_prompt(metric_key, str(query))
                    human_message = HumanMessage(content=formatted_query)
                    messages = [human_message]
            else:
                # Fallback to comprehensive if invalid metric
                system_message = REASONER_SYSTEM_MESSAGE
                logger.warning(f"⚠️ Invalid scan_type {scan_type}, using comprehensive prompt")
                
                if is_first_message:
                    formatted_query = USER_PROMPT_TEMPLATE.replace("{query}", str(query))
                    human_message = HumanMessage(content=formatted_query)
                    messages = [human_message]
        else:
            # Use comprehensive prompt for full scan
            system_message = REASONER_SYSTEM_MESSAGE
            logger.info("📊 Using comprehensive prompt for full 7-metric scan")
            
            if is_first_message:
                formatted_query = USER_PROMPT_TEMPLATE.replace("{query}", str(query))
                human_message = HumanMessage(content=formatted_query)
                messages = [human_message]
        
        messages = [{"role": "system", "content": system_message}] + messages

        result = await self.llm.ainvoke(messages)
        
        if is_first_message:
            return {"messages": [human_message, result]}
        else:
            return {"messages": [result]}
        

    # =================================== EDGE CONDITIONS ====================================
    
    def should_continue(self, state):
        messages = state["messages"]
        last_message = messages[-1]
        # If there are no tool calls, then we close the sandbox
        if not last_message.tool_calls:
            return "close_sandbox"
        # Otherwise if there is, we continue with tools
        else:
            return "continue"

    def compile(self) -> CompiledGraph:
        builder = StateGraph(GraphState)

        # Add all nodes with sandbox lifecycle management
        builder.add_node("start_sandbox", self.start_sandbox, retry=self.retry_policy)
        builder.add_node("reasoner", self.reasoner, retry=self.retry_policy)
        builder.add_node("tools", ToolNode(self.tools), retry=self.retry_policy)
        builder.add_node("close_sandbox", self.close_sandbox, retry=self.retry_policy)

        # Define the flow: START -> start_sandbox -> reasoner -> tools/close_sandbox -> END
        builder.add_edge(START, "start_sandbox")
        builder.add_edge("start_sandbox", "reasoner")
        builder.add_edge("tools", "reasoner")
        builder.add_edge("close_sandbox", END)

        # Conditional edges from reasoner
        builder.add_conditional_edges(
            "reasoner",
            self.should_continue,
            {
                "continue": "tools",
                "close_sandbox": "close_sandbox",
            }
        )

        return builder.compile()
    
