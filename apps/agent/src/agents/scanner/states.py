from typing import Annotated, Optional
from typing_extensions import TypedDict
from langchain_core.messages import AnyMessage
from langgraph.graph.message import add_messages
from daytona import Sandbox
from enum import Enum


class ScanType(str, Enum):
    """Enum for scanner metric types"""
    TEST_COVERAGE = "test_coverage"
    CLARITY = "clarity"
    MODULARITY = "modularity"
    SECURITY = "security"
    PERFORMANCE = "performance"
    FAULT_RESILIENCE = "fault_resilience"
    AGENT_READINESS = "agent_readiness"
    COMPREHENSIVE = "comprehensive"  # For full 7-metric scan


class MetricScore(TypedDict):
    """Individual metric scoring with details"""
    score: float  # 0.0 to 100.0
    description: str
    findings: list[str]
    recommendations: list[str]

class ScanMetrics(TypedDict):
    """Complete metric scoring for all 7 key areas"""
    test_coverage: MetricScore
    clarity: MetricScore
    modularity: MetricScore
    security: MetricScore
    performance: MetricScore
    fault_resilience: MetricScore
    agent_readiness: MetricScore
    overall_score: float

class GraphState(TypedDict):
    messages: Annotated[list[AnyMessage], add_messages]
    query: str
    sandbox_session_id: int | None
    repo_id: str
    scan_results: str
    issues_created: int
    metrics: Optional[ScanMetrics]
    scan_type: Optional[ScanType]  # Specific metric to scan, defaults to COMPREHENSIVE

class ScanInput(TypedDict):
    query: str
    scan_type: Optional[ScanType]  # Specific metric to scan, defaults to COMPREHENSIVE

class ScanOutput(TypedDict):
    scan_results: str
    issues_created: int
    metrics: ScanMetrics
