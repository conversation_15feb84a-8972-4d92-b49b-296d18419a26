"""
Coder Agent - AI coding assistant for analyzing and modifying codebases.
"""

import asyncio
from typing import Optional, Literal
from langchain_core.messages import AIMessage, ToolMessage, HumanMessage
from agents.base import BaseAgent
from agents.coder.graph import CoderGraph
from agents.tools import local_tools, sandbox_tools, github_tools
from langchain_core.runnables import RunnableConfig


class CoderAgent(BaseAgent):
    """
    AI coding assistant that uses advanced tools to analyze and modify codebases.
    """
    
    def __init__(
        self, 
        *,
        model_provider: Optional[Literal["anthropic", "openai"]] = "anthropic",
        model_name: Optional[str] = "claude-sonnet-4-20250514",
        use_sandbox: bool = True,
    ):
        """
        Initialize CoderAgent with LLM configuration.
        
        Args:
            model_provider: Model provider ("anthropic" or "openai")
            model_name: Specific model name
            use_sandbox: Whether to use sandbox for code execution
        """
        super().__init__(
            model_provider=model_provider, 
            model_name=model_name, 
            use_sandbox=use_sandbox
        )

        self.tools = sandbox_tools + github_tools if use_sandbox else local_tools
        self.graph = CoderGraph(llm=self.llm, tools=self.tools, use_sandbox=use_sandbox).compile()

    
    async def _execute(self, query: str, config: Optional[RunnableConfig]) -> str:
        """
        Execute the CoderAgent logic using the coder graph.
        
        Args:
            query: The task or question to process
            **kwargs: Additional parameters
            
        Returns:
            The final response from the assistant
        """
        # Stream and pretty print
        last_chunk = None
        async for chunk in self.graph.astream({
            "query": query
        }, config=config):
            # Pretty print the messages as they come
            if "reasoner" in chunk:
                for msg in chunk["reasoner"].get("messages", []):
                    if isinstance(msg, (AIMessage, HumanMessage)) and msg.content:
                        print(msg.pretty_print())
                      
            elif "tools" in chunk:
                for msg in chunk["tools"].get("messages", []):
                    if isinstance(msg, ToolMessage):
                        print(msg.pretty_print())
            
            last_chunk = chunk
        
        if last_chunk is None:
            raise ValueError("No response was generated from the agent")
        
        # Extract the final AI message from the state
        messages = last_chunk["reasoner"].get("messages", [])
        return messages[-1].content




# Example usage
if __name__ == "__main__":
    async def main():
        # Example 1: Using model provider and name
        coder_agent = CoderAgent(
            model_provider="anthropic", 
            model_name="claude-sonnet-4-20250514"
        )
        
        # Example 2: Using OpenAI
        coder_agent_openai = CoderAgent(
            model_provider="openai",
            model_name="gpt-4o"
        )
        
        # Example 3: Search for patterns
        response = await coder_agent.run("Find all functions that handle file I/O")
        print(f"\nFinal Response: {response}")
        
        # Example 4: Analyze code structure
        response = await coder_agent.run("List all Python files and their main classes")
        print(f"\nFinal Response: {response}")
    
    # Run the async main function
    asyncio.run(main())