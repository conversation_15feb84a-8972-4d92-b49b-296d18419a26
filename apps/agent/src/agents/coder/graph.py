from agents.base import BaseGraph
from agents.coder.states import GraphState
from langchain_core.messages import HumanMessage
from typing import Any
from langchain_core.language_models.chat_models import BaseChatModel
from langgraph.graph.state import CompiledGraph, StateGraph
from langgraph.prebuilt import ToolNode
from langgraph.graph import END, START
from langchain_core.runnables.config import RunnableConfig

from utils.sandbox_manager import sandbox_manager
from db import db_manager

from agents.coder.prompts import REASONER_SYSTEM_MESSAGE, USER_PROMPT_TEMPLATE

from dotenv import load_dotenv
from langchain_core.tools import BaseTool

import os

import logging

logger = logging.getLogger(__name__)

load_dotenv()



class CoderGraph(BaseGraph):
    def __init__(self, llm: BaseChatModel, tools: list[BaseTool], use_sandbox: bool = True):
        super().__init__(llm=llm, tools=tools)
        self.use_sandbox = use_sandbox

    # =================================== NODES ====================================

    async def start_sandbox(self, state: GraphState, config: RunnableConfig) -> dict[str, Any]:
        """Initialize and start a Daytona sandbox for the session.
        
        Args:
            state: Current graph state
            config: Runtime configuration for the graph
            
        Returns:
            Updated state with sandbox session_id
        """
        logger.info("🚀 Starting sandbox for coder agent session...")
        
        # Ensure database connection is established
        try:
            await db_manager.connect()
            logger.info("✅ Database connection established")
        except Exception as e:
            logger.error(f"❌ Failed to connect to database: {e}")
            # Continue anyway - some tools might still work without database
        
        try:
            # Get repo_id from state, with fallback to environment variable
            repo_id = state.get("repo_id") or os.environ.get("DEFAULT_REPO_ID")
            logger.info(f"Using repo_id: {repo_id}")
            
            session_id, sandbox = await sandbox_manager.create_session(repo_id=repo_id)
            logger.info(f"✅ Sandbox started successfully with session {session_id}")
            
            return {
                "sandbox_session_id": session_id,
            }
        except Exception as e:
            logger.error(f"❌ Failed to start sandbox: {e}")
            return {
                "sandbox_session_id": None
            }

    async def close_sandbox(self, state: GraphState, config: RunnableConfig) -> dict[str, Any]:
        """Clean up and close the Daytona sandbox.
        
        Args:
            state: Current graph state containing sandbox session ID
            config: Runtime configuration for the graph
            
        Returns:
            Updated state with sandbox cleanup status
        """
        session_id = state.get("sandbox_session_id")
        
        try:
            if session_id:
                success = await sandbox_manager.cleanup_session(session_id)
                if success:
                    logger.info("✅ Sandbox cleaned up successfully")
                else:
                    logger.warning("⚠️ Sandbox cleanup had issues")
            
            return {
                "sandbox_session_id": None,
            }
        except Exception as e:
            logger.error(f"⚠️ Error during sandbox cleanup: {e}")
            return {
                "sandbox_session_id": None,
            }

    # Reasoner node
    async def reasoner(self, state: GraphState) -> dict[str, Any]:    
        messages = state["messages"]
        query = state["query"]
        is_first_message = not messages and query
        
        if is_first_message:
            formatted_query = USER_PROMPT_TEMPLATE.format(query=query)
            human_message = HumanMessage(content=formatted_query)
            messages = [human_message]
        
        messages = [{"role": "system", "content": REASONER_SYSTEM_MESSAGE}] + messages

        result = await self.llm.ainvoke(messages)
        
        if is_first_message:
            return {"messages": [human_message, result]}
        else:
            return {"messages": [result]}
        

    # =================================== EDGE CONDITIONS ====================================

    def should_continue(self, state):
        messages = state["messages"]
        last_message = messages[-1]
        # If there are no tool calls, then we finish
        if not last_message.tool_calls:
            if self.use_sandbox:
                return "close_sandbox"
            else:
                return "end"
        # Otherwise if there is, we continue with tools
        else:
            return "continue"

    def compile(self) -> CompiledGraph:
        builder = StateGraph(GraphState)

        if self.use_sandbox:
            # Add nodes with sandbox lifecycle management
            builder.add_node("start_sandbox", self.start_sandbox, retry=self.retry_policy)
            builder.add_node("reasoner", self.reasoner, retry=self.retry_policy)
            builder.add_node("tools", ToolNode(self.tools), retry=self.retry_policy)
            builder.add_node("close_sandbox", self.close_sandbox, retry=self.retry_policy)

            # Define the flow: START -> start_sandbox -> reasoner -> tools/close_sandbox -> END
            builder.add_edge(START, "start_sandbox")
            builder.add_edge("start_sandbox", "reasoner")
            builder.add_edge("tools", "reasoner")
            builder.add_edge("close_sandbox", END)

            # Conditional edges from reasoner
            builder.add_conditional_edges(
                "reasoner",
                self.should_continue,
                {
                    "continue": "tools",
                    "close_sandbox": "close_sandbox",
                }
            )
        else:
            # Local graph without sandbox nodes
            builder.add_node("reasoner", self.reasoner, retry=self.retry_policy)
            builder.add_node("tools", ToolNode(self.tools), retry=self.retry_policy)

            # Define the flow: START -> reasoner -> tools/END
            builder.add_edge(START, "reasoner")
            builder.add_edge("tools", "reasoner")

            # Conditional edges from reasoner
            builder.add_conditional_edges(
                "reasoner",
                self.should_continue,
                {
                    "continue": "tools",
                    "end": END,
                }
            )

        return builder.compile()

