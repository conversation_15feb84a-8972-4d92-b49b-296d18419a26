import os
import json
import hmac
import hashlib
import jwt
import time
import base64
import asyncio
import logging
import aiohttp
from typing import Dict, Any, Optional, List
from github import Auth, Github

from db import get_db

# Import Docker builder
from .docker_builder import build_and_push_repo_image

# Configure logging
logger = logging.getLogger(__name__)

# GitHub configuration
GITHUB_WEBHOOK_SECRET = os.environ.get("GITHUB_WEBHOOK_SECRET", "your_webhook_secret_here")
GITHUB_APP_ID = os.environ.get("GITHUB_APP_ID")

# Decode the base64 encoded private key
GITHUB_PRIVATE_KEY_B64 = os.environ.get("GITHUB_PRIVATE_KEY")
GITHUB_PRIVATE_KEY = base64.b64decode(GITHUB_PRIVATE_KEY_B64).decode('utf-8') if GITHUB_PRIVATE_KEY_B64 else None


def verify_github_signature(payload_body: bytes, signature_header: str) -> bool:
    """Verify that the payload was sent from GitHub by validating SHA256.
    
    Args:
        payload_body: Raw request body as bytes
        signature_header: GitHub signature header value
        
    Returns:
        bool: True if signature is valid, False otherwise
    """
    if not signature_header:
        return False
    
    hash_object = hmac.new(
        GITHUB_WEBHOOK_SECRET.encode('utf-8'), 
        msg=payload_body, 
        digestmod=hashlib.sha256
    )
    expected_signature = "sha256=" + hash_object.hexdigest()
    
    if not hmac.compare_digest(expected_signature, signature_header):
        return False
    
    return True


async def get_installation_access_token(installation_id: str) -> Optional[str]:
    """Get an installation access token for the GitHub App.
    
    Args:
        installation_id: GitHub App installation ID
        
    Returns:
        str: Installation access token if successful, None otherwise
    """
    try:
        # Create JWT for GitHub App authentication
        now = int(time.time())
        payload = {
            'iat': now - 60,  # issued at time, 60 seconds in the past to allow for clock drift
            'exp': now + (10 * 60),  # expiration time (10 minute maximum)
            'iss': GITHUB_APP_ID  # issuer
        }
        
        jwt_token = jwt.encode(payload, GITHUB_PRIVATE_KEY, algorithm='RS256')
        
        # Get installation access token using aiohttp
        headers = {
            'Authorization': f'Bearer {jwt_token}',
            'Accept': 'application/vnd.github.v3+json'
        }
        
        async with aiohttp.ClientSession() as session:
            async with session.post(
                f'https://api.github.com/app/installations/{installation_id}/access_tokens',
                headers=headers
            ) as response:
                if response.status == 201:
                    data = await response.json()
                    return data['token']
                else:
                    logger.error(f"Failed to get installation token: {response.status}")
                    return None
            
    except Exception as e:
        logger.error(f"Error getting installation access token: {e}")
        return None


async def get_repository_details(installation_id: str, repo_full_name: str) -> Dict[str, Any]:
    """Fetch repository timestamps using PyGithub.
    
    Args:
        installation_id: GitHub App installation ID
        repo_full_name: Repository full name (owner/repo)
        
    Returns:
        Dict containing repository timestamps (repo_created_at, pushed_at)
    """
    try:
        # Get installation access token
        token = await get_installation_access_token(installation_id)
        if not token:
            return {}
        
        def _get_repo_details():
            # Authenticate with GitHub
            auth = Auth.Token(token)
            g = Github(auth=auth)
            
            # Get repository
            repo = g.get_repo(repo_full_name)
            
            # Extract only the timestamps we need, matching the new schema
            details = {
                'repo_created_at': repo.created_at.isoformat() if repo.created_at else None,
                'pushed_at': repo.pushed_at.isoformat() if repo.pushed_at else None,
            }
            
            return details
        
        # Run the blocking GitHub API call in a thread
        return await asyncio.to_thread(_get_repo_details)
        
    except Exception as e:
        logger.error(f"Error fetching repository details for {repo_full_name}: {e}")
        return {}


async def build_top_repositories_images(
    integration_id: str, 
    installation_id: int, 
    db_client: Any,
    top_x: int = 10,
    delay_between_starts: float = 0.0
) -> None:
    """Build Docker images for the top X most recently pushed repositories.
    
    Args:
        integration_id: Integration ID in database
        installation_id: GitHub App installation ID
        db_client: Database client instance
        top_x: Number of top repositories to build (default: 10)
        delay_between_starts: Delay in seconds between starting each build (default: 0.0)
    """
    try:
        # First try to get top X repositories by most recent push, ordered by pushed_at DESC
        top_repos_result = await db_client.table('repositories')\
            .select('*')\
            .eq('integration_id', integration_id)\
            .not_.is_('pushed_at', 'null')\
            .order('pushed_at', desc=True)\
            .limit(top_x)\
            .execute()
        
        # If no repositories with pushed_at dates found, fallback to all repositories for this integration
        if not top_repos_result.data:
            logger.info(f"No repositories with push dates found for integration {integration_id}, falling back to all repositories")
            top_repos_result = await db_client.table('repositories')\
                .select('*')\
                .eq('integration_id', integration_id)\
                .limit(top_x)\
                .execute()
        
        if not top_repos_result.data:
            logger.warning(f"No repositories found for integration {integration_id}")
            return
        
        top_repos = top_repos_result.data
        logger.info(f"Found {len(top_repos)} repositories to build Docker images for (top {top_x})")
        
        # Start building images
        for i, repo in enumerate(top_repos, 1):
            repo_url = repo['url']
            repo_name = repo['name']
            
            logger.info(f"🐳 [{i}/{len(top_repos)}] Starting Docker build for: {repo_name}")
            
            # Start the build process asynchronously (fire and forget style)
            asyncio.create_task(
                build_repository_image_with_logging(
                    repo_url=repo_url,
                    installation_id=installation_id,
                    repo_name=repo_name,
                    current=i,
                    total=len(top_repos)
                )
            )
            
            # Optional delay between starting each build
            if delay_between_starts > 0 and i < len(top_repos):
                await asyncio.sleep(delay_between_starts)
        
        logger.info(f"🚀 Started Docker builds for {len(top_repos)} repositories")
        
    except Exception as e:
        logger.error(f"Error building top repositories images: {e}")


async def build_repository_image_with_logging(
    repo_url: str, 
    installation_id: int, 
    repo_name: str, 
    current: int, 
    total: int
) -> None:
    """Build a single repository image with comprehensive logging.
    
    Args:
        repo_url: Repository URL to build
        installation_id: GitHub App installation ID
        repo_name: Repository name for logging
        current: Current repository number
        total: Total number of repositories being built
    """
    try:
        logger.info(f"🔨 [{current}/{total}] Building Docker image for {repo_name}...")
        
        # Call the docker builder function
        result = await build_and_push_repo_image(
            repo_url=repo_url,
            installation_id=installation_id,
            force_rebuild=False
        )
        
        # Update database with the built image name
        try:
            db = get_db()
            
            # Update the repository's daytona_image field
            await db.table('repositories').update({
                'daytona_image': result
            }).eq('name', repo_name).execute()
            
            logger.info(f"📝 [{current}/{total}] Updated database with image: {result} for {repo_name}")
            
        except Exception as db_error:
            logger.error(f"❌ [{current}/{total}] Failed to update database for {repo_name}: {db_error}")
        
        logger.info(f"✅ [{current}/{total}] Successfully built Docker image for {repo_name}")
        logger.debug(f"Build result for {repo_name}: {result}")
        
    except Exception as e:
        logger.error(f"❌ [{current}/{total}] Failed to build Docker image for {repo_name}: {e}") 