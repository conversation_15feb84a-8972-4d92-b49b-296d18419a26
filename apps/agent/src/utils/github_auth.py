"""GitHub App authentication and installation token management."""

import os
import subprocess
import json
import logging
import asyncio

logger = logging.getLogger(__name__)


async def create_installation_token(installation_id: int) -> str:
    """Generate installation access token using gh CLI.
    
    Args:
        installation_id: GitHub App installation ID
        
    Returns:
        Installation access token (valid for 1 hour)
        
    Raises:
        ValueError: If required environment variables are missing
        Exception: If token generation fails
    """
    # Get credentials from environment
    app_id = os.getenv("GITHUB_APP_ID")
    private_key = os.getenv("GITHUB_PRIVATE_KEY")
    
    if not app_id or not private_key:
        raise ValueError("GITHUB_APP_ID and GITHUB_PRIVATE_KEY environment variables are required")
    
    logger.info(f"Generating installation token for installation {installation_id}")
    
    try:
        # Generate token using gh CLI - wrap in thread to avoid blocking
        def _run_gh_command():
            return subprocess.run([
                "gh", "token", "generate",
                "--base64-key", private_key,
                "--app-id", str(app_id),
                "--installation-id", str(installation_id)
            ], capture_output=True, text=True, check=True)
        
        result = await asyncio.to_thread(_run_gh_command)
        
        # Parse JSON response
        token_data = json.loads(result.stdout)
        
        logger.info(f"✅ Generated token for installation {installation_id}")
        return token_data['token']
        
    except subprocess.CalledProcessError as e:
        logger.error(f"Failed to generate token: {e.stderr}")
        raise Exception(f"Failed to generate installation token: {e.stderr}")
    except json.JSONDecodeError as e:
        logger.error(f"Failed to parse token response: {e}")
        raise Exception(f"Invalid token response format: {e}")