"""Docker image builder for repository-specific sandboxes using Daytona declarative API."""

import asyncio
import logging
import os
from datetime import datetime
from typing import Tuple
from urllib.parse import urlparse

from daytona import Daytona, Image, CreateSnapshotParams, Resources
from dotenv import load_dotenv


from utils.github_auth import create_installation_token


logger = logging.getLogger(__name__)

load_dotenv()


def parse_github_url(repo_url: str) -> Tuple[str, str]:
    """Extract owner and repo name from GitHub URL.
    
    Args:
        repo_url: GitHub repository URL
        
    Returns:
        Tuple of (owner, repo_name)
        
    Examples:
        https://github.com/openai/whisper -> ("openai", "whisper")
        https://github.com/langchain-ai/langchain.git -> ("langchain-ai", "langchain")
    """
    # Parse URL
    parsed = urlparse(repo_url)
    path = parsed.path.strip('/')
    
    # Remove .git suffix if present
    if path.endswith('.git'):
        path = path[:-4]
    
    # Split owner and repo
    parts = path.split('/')
    if len(parts) >= 2:
        return parts[0], parts[1]
    else:
        raise ValueError(f"Invalid GitHub URL format: {repo_url}")


def generate_image_name(repo_url: str) -> str:
    """Generate Docker image name from repo URL with timestamp.
    
    Args:
        repo_url: GitHub repository URL
        
    Returns:
        Docker image name with timestamp (e.g., openai/whisper:v20250530-010045)
    """
    owner, repo = parse_github_url(repo_url)
    # Use owner/repo format directly
    image_name = f"{owner}/{repo}".lower()
    
    # Always use timestamp for versioning
    timestamp = datetime.now().strftime("v%Y%m%d-%H%M%S")
    return f"{image_name}:{timestamp}"




async def build_and_push_repo_image(
    repo_url: str,
    installation_id: int,
    force_rebuild: bool = False,
    enable_build_logs: bool = False
) -> str:
    """Build and register Docker image with pre-cloned repository using Daytona sync API."""
    base_image = os.getenv("BASE_IMAGE")
    github_token = await create_installation_token(installation_id=installation_id)
    logger.info(f"Generated fresh installation token for ID {installation_id}")
    image_tag = generate_image_name(repo_url)
    repo_url_clean = repo_url.replace('https://', '')
    base_name = image_tag.split(':')[0] + ':'

    # If force_rebuild is False, check for any existing snapshot with the same base name and reuse the first one found
    if not force_rebuild:
        try:
            logger.debug("Fetching existing snapshots...")
            
            def _get_snapshots():
                daytona = Daytona()
                return daytona.snapshot.list()
            
            existing_snapshots = await asyncio.to_thread(_get_snapshots)
            logger.debug(f"Found {len(existing_snapshots)} existing snapshots")
            
            for snap in existing_snapshots:
                if hasattr(snap, 'name') and snap.name and snap.name.startswith(base_name):
                    logger.info(f"Reusing existing snapshot: {snap.name}")
                    return snap.name
        except Exception as e:
            logger.warning(f"Could not check existing snapshots: {e}")
            logger.info("Proceeding with new snapshot creation...")

    # Define the dynamic image using the base_image from env or default  
    # Ensure the repo URL has .git suffix for cloning
    clone_url = repo_url_clean
    if not clone_url.endswith('.git'):
        clone_url += '.git'
    
    dynamic_image = (
        Image.base(base_image)
        .run_commands([
            # Clone the repository directly into /workspace (base image workdir)
            f"git clone https://x-access-token:{github_token}@{clone_url} /workspace",
            "cd /workspace",
            # Configure git user identity (static config)
            'git config --global user.email "<EMAIL>"',
            'git config --global user.name "Backspace Agent"'
        ])
        .workdir("/workspace")
        .env({
            "WORKSPACE_PATH": "/workspace",
            "GITHUB_TOKEN": github_token,
            "GH_TOKEN": github_token
        })
        .run_commands([
            # Test that gh CLI works with the token and verify clone
            "gh auth status",
            "pwd && ls -la"
        ])
    )

    logger.info(f"Building snapshot: {image_tag}")
    logger.info(f"Repository: {repo_url}")

    try:
        logger.info("Building snapshot using Daytona sync API...")
        
        # Define log handler for real-time feedback (optional)
        log_handler = None
        if enable_build_logs:
            def log_handler(log_line: str) -> None:
                if log_line.strip():
                    logger.info(f"BUILD LOG: {log_line.strip()}")
        
        def _create_snapshot():
            daytona = Daytona()
            create_params = CreateSnapshotParams(
                name=image_tag,
                image=dynamic_image,
                resources=Resources(cpu=2, memory=4, disk=5),  # 2 CPU, 4GB RAM, 5GB disk
            )
            
            if enable_build_logs and log_handler:
                return daytona.snapshot.create(create_params, on_logs=log_handler)
            else:
                return daytona.snapshot.create(create_params)
        
        await asyncio.to_thread(_create_snapshot)
        
        logger.info(f"✅ Successfully created snapshot: {image_tag}")
        return image_tag
    except Exception as e:
        logger.error(f"Failed to create snapshot: {e}")
        raise

async def cleanup_org_images(org_name: str) -> None:
    """Clean up all Daytona snapshots for an organization using sync Daytona API.
    
    Args:
        org_name: Organization name (e.g., "openai", "langchain-ai")
    """
    try:
        def _get_snapshots_and_cleanup():
            daytona = Daytona()
            
            # Get all snapshots
            response = daytona.snapshot.list()
            
            # Find all snapshots that start with the org name
            org_prefix = f"{org_name.lower()}/"
            org_snapshots = []
            
            for snap in response:
                if hasattr(snap, 'name') and snap.name and snap.name.lower().startswith(org_prefix):
                    org_snapshots.append(snap)
            
            if not org_snapshots:
                return 0, 0, "No snapshots found"
            
            # Delete all snapshots for this org
            deleted_count = 0
            failed_count = 0
            
            for snap in org_snapshots:
                try:
                    daytona.snapshot.delete(snap.name)
                    deleted_count += 1
                except Exception as e:
                    failed_count += 1
            
            return deleted_count, failed_count, f"Found {len(org_snapshots)} snapshots"
        
        deleted_count, failed_count, message = await asyncio.to_thread(_get_snapshots_and_cleanup)
        
        if deleted_count == 0 and failed_count == 0:
            logger.info(f"No snapshots found for organization: {org_name}")
        else:
            logger.info(f"✅ Cleanup complete for org '{org_name}': {deleted_count} deleted, {failed_count} failed")
                
    except Exception as e:
        logger.error(f"Failed to cleanup snapshots for org '{org_name}': {e}")

# Example usage
if __name__ == "__main__":
    async def main():
        # Set up logging
        logging.basicConfig(level=logging.INFO)
        
        # Example: Build image for a public repo
        image_tag = await build_and_push_repo_image(
            repo_url="https://github.com/openai/whisper",
            installation_id=12345678,  # Your GitHub App installation ID
            force_rebuild=False  # Set to True to force rebuild with new timestamp
        )
        print(f"Built and pushed image: {image_tag}")
    
    asyncio.run(main())
    
