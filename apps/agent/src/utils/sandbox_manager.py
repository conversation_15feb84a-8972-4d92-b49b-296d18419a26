"""
Sandbox manager for persistent sandbox sessions.
Maintains active sandboxes in memory while keeping state serializable.
"""

import asyncio
import logging
from typing import Dict, <PERSON><PERSON>, <PERSON><PERSON>
from daytona import Sandbox
from utils.sandbox import create_sandbox, cleanup_sandbox

logger = logging.getLogger(__name__)

class SandboxManager:
    """Manages persistent sandbox sessions."""
    
    def __init__(self):
        self._sandboxes: Dict[str, Sandbox] = {}
        self._lock = asyncio.Lock()
    
    async def create_session(self, repo_id: str, session_id: Optional[str] = None) -> Tuple[str, Sandbox]:
        """Create a new sandbox session.
        
        Args:
            repo_id: Repository ID to get organization installation_id from database
            session_id: Optional session ID. If None, one will be generated.
            
        Returns:
            Tuple of (session_id, sandbox_instance)
        """
        async with self._lock:
            sandbox, run_id = await create_sandbox(repo_id=repo_id, run_id=session_id)
            self._sandboxes[run_id] = sandbox
            logger.info(f"🚀 Created sandbox session: {run_id}")
            return run_id, sandbox
    
    def get_sandbox(self, session_id: str) -> Optional[Sandbox]:
        """Get sandbox by session ID.
        
        Args:
            session_id: The sandbox session ID
            
        Returns:
            Sandbox instance if found, None otherwise
        """
        return self._sandboxes.get(session_id)
    
    async def cleanup_session(self, session_id: str) -> bool:
        """Clean up a sandbox session.
        
        Args:
            session_id: The sandbox session ID to clean up
            
        Returns:
            True if cleanup successful, False otherwise
        """
        async with self._lock:
            sandbox = self._sandboxes.pop(session_id, None)
            if sandbox:
                try:
                    await cleanup_sandbox(sandbox, session_id)
                    logger.info(f"✅ Cleaned up sandbox session: {session_id}")
                    return True
                except Exception as e:
                    logger.error(f"❌ Failed to cleanup sandbox {session_id}: {e}")
                    return False
            else:
                logger.warning(f"⚠️ Sandbox session not found: {session_id}")
                return False
    
    async def cleanup_all(self):
        """Clean up all active sandbox sessions."""
        async with self._lock:
            for session_id, sandbox in list(self._sandboxes.items()):
                try:
                    await cleanup_sandbox(sandbox, session_id)
                    logger.info(f"✅ Cleaned up sandbox session: {session_id}")
                except Exception as e:
                    logger.error(f"❌ Failed to cleanup sandbox {session_id}: {e}")
            self._sandboxes.clear()
    
    def list_active_sessions(self) -> list[str]:
        """Get list of active session IDs."""
        return list(self._sandboxes.keys())

# Global sandbox manager instance
sandbox_manager = SandboxManager() 