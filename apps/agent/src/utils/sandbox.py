"""Sandbox utilities for managing Daytona sandboxes."""

import logging
import os
import uuid
from typing import Optional
from contextlib import asynccontextmanager
import asyncio

from daytona import Daytona, CreateSandboxFromImageParams, SessionExecuteRequest
from .github_auth import create_installation_token
from db import get_db

logger = logging.getLogger(__name__)
from dotenv import load_dotenv

load_dotenv()


async def get_installation_id_from_repo(repo_id: str) -> tuple[int, str]:
    """Get GitHub installation ID and image name from repository ID via database lookup.
    
    Args:
        repo_id: Repository ID to lookup
        
    Returns:
        Tuple of (GitHub installation ID, image name)
        
    Raises:
        ValueError: If repo_id not found or missing installation_id
    """
    try:
        db = get_db()
        
        # First get the repository and its integration_id
        repo_result = await db.table('repositories').select(
            'id, daytona_image, integration_id, url, name'
        ).eq('id', repo_id).execute()
        
        if not repo_result.data:
            raise ValueError(f"Repository with id {repo_id} not found")
        
        repo = repo_result.data[0]
        integration_id = repo.get('integration_id')
        
        if not integration_id:
            raise ValueError(f"No integration_id found for repository {repo_id}")
        
        # Now get the GitHub integration data from integrations table
        integration_result = await db.table('integrations').select(
            'data'
        ).eq('id', integration_id).execute()
        
        if not integration_result.data:
            raise ValueError(f"No integration found with id {integration_id}")
        
        integration_data = integration_result.data[0].get('data', {})
        installation_id = integration_data.get('installation_id')
        
        if not installation_id:
            raise ValueError(f"No installation_id found in integration data for integration {integration_id}")
        
        installation_id = int(installation_id)
        image_name = repo.get('daytona_image')
        
        # For testing mode: skip Docker build entirely
        logger.debug(f"🧪 TESTING MODE: Skipping Docker build for repo {repo_id}")
        logger.debug(f"Found installation_id {installation_id} for repo {repo_id}")
        
        return installation_id, None  # Return None for image_name in testing mode
        
    except Exception as e:
        logger.error(f"Failed to get installation_id and image_name for repo {repo_id}: {e}")
        raise


async def create_sandbox(repo_id: str, run_id: Optional[str] = None) -> tuple[object, str]:
    """Create and return a Daytona sandbox with fresh GitHub credentials.
    
    Args:
        repo_id: Repository ID to get organization installation_id and image_name from database
        run_id: Session ID for the sandbox. If None, generates a random UUID string.
    
    Returns:
        Tuple of (sandbox instance, run_id used)
        
    Raises:
        Exception: If sandbox creation or configuration fails
    """
    # Generate run_id if not provided
    if run_id is None:
        run_id = str(uuid.uuid4())
    
    
    try:
        # For testing: use base image and get installation_id only
        base_image = os.getenv("BASE_IMAGE", "backspaceinc/base-sandbox:latest")
        logger.info(f"🧪 TESTING MODE: Creating sandbox with base image: {base_image}")
        
        # Still need installation_id for GitHub token, but ignore the image_name
        installation_id, _ = await get_installation_id_from_repo(repo_id)
        logger.debug(f"Using GitHub installation ID: {installation_id} for repo: {repo_id}")
        
        # Wrap blocking token creation in asyncio.to_thread
        fresh_github_token = await create_installation_token(installation_id=installation_id)
        logger.debug("Generated fresh GitHub token")

        # Always use base image for testing
        sandbox_params = CreateSandboxFromImageParams(
            image=base_image,  
            env_vars={
                "GH_TOKEN": fresh_github_token,
                "WORKSPACE_PATH": "/home/<USER>/workspace",
                "ANTHROPIC_API_KEY": os.getenv("ANTHROPIC_API_KEY"),
            }
        )
        
        # Wrap blocking Daytona operations in asyncio.to_thread
        def _create_sandbox_sync():
            daytona = Daytona()
            return daytona.create(params=sandbox_params)
        
        sandbox = await asyncio.to_thread(_create_sandbox_sync)
        
        # Create session and verify repository setup
        try:
            # Wrap blocking session creation
            await asyncio.to_thread(sandbox.process.create_session, run_id)
            logger.info("✅ Session created successfully")
            
            # Check current state and setup workspace in home directory
            setup_commands = [
                "pwd",
                "whoami", 
                "which claude-code || echo 'claude-code not found in PATH'",
                "ls -la $HOME",
                "mkdir -p $HOME/workspace",
                "cd $HOME/workspace && pwd",
                "echo 'WORKSPACE_PATH=' $WORKSPACE_PATH"
            ]
            
            for cmd in setup_commands:
                setup_req = SessionExecuteRequest(command=cmd)
                setup_result = await asyncio.to_thread(
                    sandbox.process.execute_session_command,
                    run_id,
                    setup_req
                )
                logger.info(f"🔧 SETUP: {cmd} -> {setup_result}")
            
            # Optional: Update git remote with fresh token for future operations
            db = get_db()
            repo_result = await db.table('repositories').select('url').eq('id', repo_id).execute()
            repo_url = repo_result.data[0]['url'] if repo_result.data else None
            
            if repo_url:
                repo_path = repo_url.replace('https://github.com/', '')
                git_remote_url = f"https://x-access-token:$<EMAIL>/{repo_path}.git"
                
                # Clone the repository into ~/workspace  
                clone_commands = f'''cd $HOME/workspace && \
git clone {git_remote_url} . && \
git config user.email "<EMAIL>" && \
git config user.name "Backspace Agent" && \
pwd && ls -la'''
                
                req = SessionExecuteRequest(command=clone_commands)
                
                # Execute clone commands
                result = await asyncio.to_thread(
                    sandbox.process.execute_session_command, 
                    run_id, 
                    req
                )
                logger.info(f"📂 Clone result: {result}")
                
                # Verify the clone worked
                verify_req = SessionExecuteRequest(command="ls -la $HOME/workspace/")
                verify_result = await asyncio.to_thread(
                    sandbox.process.execute_session_command,
                    run_id,
                    verify_req
                )
                logger.info(f"✅ Repository verification: {verify_result}")
                
            else:
                logger.warning("⚠️ No repo_url found, skipping repository clone")
            
        except Exception as e:
            logger.warning(f"⚠️ Failed to clone repository: {e}")
        
        logger.info(f"✅ Successfully created Daytona sandbox with session {run_id}")
        return sandbox, run_id
        
    except Exception as e:
        logger.error(f"❌ Failed to create Daytona sandbox: {e}")
        raise


async def cleanup_sandbox(sandbox: Optional[object], run_id: Optional[str] = None) -> None:
    """Clean up the Daytona sandbox and its session.
    
    Args:
        sandbox: The sandbox instance to clean up
        run_id: Session ID to clean up. If None, skips session cleanup.
        
    Raises:
        Exception: If cleanup fails
    """
    if sandbox:
        logger.info(f"🧹 Cleaning up Daytona sandbox{f' and session {run_id}' if run_id else ''}...")
        try:
            # Clean up session first if provided
            if run_id:
                try:
                    # Wrap blocking session deletion
                    await asyncio.to_thread(sandbox.process.delete_session, run_id)
                    logger.debug(f"✅ Cleaned up session {run_id}")
                except Exception as e:
                    logger.warning(f"⚠️ Failed to clean up session {run_id}: {e}")
            
            # Remove the sandbox - wrap blocking operation
            def _remove_sandbox_sync():
                sandbox.delete()
            
            await asyncio.to_thread(_remove_sandbox_sync)
            logger.info("✅ Successfully cleaned up Daytona sandbox")
        except Exception as e:
            logger.error(f"⚠️ Error during sandbox cleanup: {e}")
            raise


@asynccontextmanager
async def sandbox_context(repo_id: str):
    """Context manager for sandbox lifecycle management.
    
    Automatically generates a UUID for the session and handles cleanup.
    
    Args:
        repo_id: Repository ID to get organization installation_id from database
        
    Yields:
        sandbox: The sandbox instance
        
    Example:
        async with sandbox_context(repo_id="12345") as sandbox:
            # Use sandbox here
            req = SessionExecuteRequest(command="ls")
            result = sandbox.process.execute_session_command(sandbox._run_id, req)
        # Sandbox automatically cleaned up
    """
    sandbox = None
    run_id = None
    try:
        sandbox, run_id = await create_sandbox(repo_id)  # Pass repo_id
        # Store run_id on sandbox for easy access
        sandbox._run_id = run_id
        yield sandbox
    finally:
        if sandbox:
            await cleanup_sandbox(sandbox, run_id)
