"""
Claude Code + E2B Streaming Example

This script demonstrates how to use the Claude Code + E2B integration
to run Claude Code in a sandboxed environment with real-time streaming.
"""

import asyncio
import json
from typing import Dict, Any
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

from agents.tools.claude_e2b_integration import claude_code_in_e2b, ClaudeE2BStreamer, StreamEvent
from claude_code_sdk import ClaudeCodeOptions


async def simple_streaming_example():
    """Simple example of streaming Claude Code execution through E2B."""
    print("🚀 Starting Claude Code + E2B Streaming Example")
    print("=" * 50)
    
    prompt = """
    I need you to analyze a dataset. Please:
    1. Create a simple Python script that generates some sample data
    2. Calculate basic statistics
    3. Create a simple visualization
    4. List the files in the current directory
    
    Make sure to use proper error handling.
    """
    
    try:
        async for event in claude_code_in_e2b(prompt):
            handle_stream_event(event)
            
    except Exception as e:
        print(f"❌ Error: {e}")


async def advanced_streaming_example():
    """Advanced example with custom options and detailed event handling."""
    print("\n🔧 Advanced Claude Code + E2B Streaming Example")
    print("=" * 50)
    
    # Custom options for Claude Code
    options = ClaudeCodeOptions(
        max_turns=5,
        system_prompt="You are a helpful data analyst. Always explain your code before running it."
    )
    
    prompt = """
    I want to build a simple web scraper that:
    1. Installs necessary dependencies (requests, beautifulsoup4)
    2. Scrapes a simple website (like httpbin.org/json)
    3. Processes the JSON data
    4. Saves results to a file
    
    Please implement this step by step with error handling.
    """
    
    async with ClaudeE2BStreamer() as streamer:
        try:
            async for event in streamer.stream_claude_code(prompt, options):
                handle_advanced_event(event)
                
        except Exception as e:
            print(f"❌ Error in advanced example: {e}")


def handle_stream_event(event: StreamEvent):
    """Handle streaming events from Claude Code + E2B integration."""
    timestamp = f"[{event.timestamp:.2f}]"
    source_emoji = "🤖" if event.source == "claude" else "📦"
    
    if event.type == "claude_message":
        print(f"{timestamp} {source_emoji} Claude: {event.content}")
    
    elif event.type == "tool_call":
        tool_info = event.content
        print(f"{timestamp} 🔧 Tool Call: {tool_info['name']}")
        if tool_info.get('arguments'):
            print(f"    Args: {tool_info['arguments']}")
    
    elif event.type == "execution_start":
        info = event.content
        print(f"{timestamp} ▶️  Executing {info['language']} code:")
        print(f"    {info['code'][:100]}{'...' if len(info['code']) > 100 else ''}")
    
    elif event.type == "stdout":
        print(f"{timestamp} 📤 Output: {event.content.strip()}")
    
    elif event.type == "stderr":
        print(f"{timestamp} ⚠️  Error: {event.content.strip()}")
    
    elif event.type == "result":
        print(f"{timestamp} 📊 Result: {event.content}")
    
    elif event.type == "execution_complete":
        info = event.content
        status = "✅ Success" if info['success'] else f"❌ Failed: {info['error']}"
        print(f"{timestamp} {status}")
    
    elif event.type == "error":
        print(f"{timestamp} 💥 Error: {event.content}")


def handle_advanced_event(event: StreamEvent):
    """Advanced event handler with detailed logging."""
    # Create a detailed event log
    event_data = {
        "timestamp": event.timestamp,
        "type": event.type,
        "source": event.source,
        "content_preview": str(event.content)[:200] if event.content else None
    }
    
    # Log to console with emojis
    handle_stream_event(event)
    
    # You could also:
    # - Save events to a database
    # - Send to a websocket for real-time UI updates
    # - Process results for further analysis
    # - Trigger other workflows based on events


async def interactive_streaming_example():
    """Interactive example that lets you send multiple prompts."""
    print("\n💬 Interactive Claude Code + E2B Session")
    print("=" * 50)
    print("Type 'quit' to exit, 'help' for commands")
    
    async with ClaudeE2BStreamer() as streamer:
        while True:
            try:
                user_input = input("\n🎯 Enter your prompt: ").strip()
                
                if user_input.lower() == 'quit':
                    break
                elif user_input.lower() == 'help':
                    print_help()
                    continue
                elif not user_input:
                    continue
                
                print(f"\n🚀 Processing: {user_input}")
                print("-" * 40)
                
                async for event in streamer.stream_claude_code(user_input):
                    handle_stream_event(event)
                
                print("-" * 40)
                print("✅ Completed")
                
            except KeyboardInterrupt:
                print("\n👋 Interrupted by user")
                break
            except Exception as e:
                print(f"❌ Error: {e}")


def print_help():
    """Print help information."""
    help_text = """
📚 Available Commands:
    
Example prompts you can try:
• "Create a Python script to calculate fibonacci numbers"
• "Install pandas and create a sample dataframe"
• "List all files in the current directory"
• "Create a simple HTTP server using Python"
• "Generate some random data and plot it"
• "Check system information"

Special commands:
• 'quit' - Exit the session
• 'help' - Show this help

The integration will:
✅ Send your prompt to Claude Code
✅ Execute any code Claude generates in the E2B sandbox
✅ Stream all outputs back to you in real-time
✅ Handle errors gracefully
    """
    print(help_text)


async def websocket_streaming_example():
    """Example of how you might integrate this with a websocket for real-time UI."""
    print("\n🌐 WebSocket-Style Streaming Example")
    print("=" * 50)
    
    # Simulate a websocket connection
    websocket_events = []
    
    def websocket_handler(event: StreamEvent):
        """Simulate sending event to websocket clients."""
        event_json = {
            "type": event.type,
            "content": event.content,
            "timestamp": event.timestamp,
            "source": event.source
        }
        websocket_events.append(event_json)
        print(f"📡 WebSocket Event: {event.type} from {event.source}")
    
    prompt = "Create a simple 'Hello World' script and run it"
    
    async for event in claude_code_in_e2b(prompt):
        websocket_handler(event)
        # In a real app, you'd send this to connected websocket clients
        # await websocket.send_json(event_json)
    
    print(f"\n📊 Total events captured: {len(websocket_events)}")
    print("Events could be sent to frontend for real-time updates!")


async def main():
    """Run all examples."""
    print("🎉 Claude Code + E2B Integration Examples")
    print("=" * 60)
    
    # Run examples
    await simple_streaming_example()
    await advanced_streaming_example()
    await websocket_streaming_example()
    
    # Uncomment for interactive mode
    # await interactive_streaming_example()
    
    print("\n🎯 All examples completed!")
    print("\nTo run interactive mode, uncomment the line in main() function")


if __name__ == "__main__":
    asyncio.run(main())