from agents.tools.claude_e2b_integration import claude_code_in_e2b, ClaudeE2BStreamer, StreamEvent
from claude_code_sdk import Claude<PERSON>odeOptions


def handle_stream_event(event: StreamEvent):
    """Handle streaming events from Claude Code + E2B integration."""
    timestamp = f"[{event.timestamp:.2f}]"
    source_emoji = "🤖" if event.source == "claude" else "📦"
    
    # Skip SystemMessage events as they're too verbose
    if (event.type == "claude_message" and 
        hasattr(event.content, 'subtype') and 
        event.content.subtype == 'init'):
        print(f"{timestamp} 🚀 Claude initialized (session: {event.content.data.get('session_id', 'unknown')[:8]}...)")
        return
    
    if event.type == "claude_message":
        # Pretty print different message types
        content = event.content
        if hasattr(content, 'content') and content.content:
            # Extract text from TextBlock objects
            text_parts = []
            for block in content.content:
                if hasattr(block, 'text'):
                    text_parts.append(block.text)
                elif hasattr(block, 'name'):  # <PERSON>l calls
                    text_parts.append(f"🔧 Calling {block.name}")
            
            if text_parts:
                text = " | ".join(text_parts)
                print(f"{timestamp} {source_emoji} Claude: {text[:200]}{'...' if len(text) > 200 else ''}")
        else:
            print(f"{timestamp} {source_emoji} Claude: {str(content)[:200]}{'...' if len(str(content)) > 200 else ''}")
    
    elif event.type == "tool_call":
        tool_info = event.content
        print(f"{timestamp} 🔧 Tool Call: {tool_info['name']}")
        if tool_info.get('arguments'):
            print(f"    Args: {tool_info['arguments']}")
    
    elif event.type == "execution_start":
        info = event.content
        print(f"{timestamp} ▶️  Executing {info['language']} code:")
        print(f"    {info['code'][:100]}{'...' if len(info['code']) > 100 else ''}")
    
    elif event.type == "stdout":
        print(f"{timestamp} 📤 Output: {event.content.strip()}")
    
    elif event.type == "stderr":
        print(f"{timestamp} ⚠️  Error: {event.content.strip()}")
    
    elif event.type == "result":
        print(f"{timestamp} 📊 Result: {event.content}")
    
    elif event.type == "execution_complete":
        info = event.content
        status = "✅ Success" if info['success'] else f"❌ Failed: {info['error']}"
        print(f"{timestamp} {status}")
    
    elif event.type == "error":
        print(f"{timestamp} 💥 Error: {event.content}")


async def simple_streaming_example():
    """Simple example of streaming Claude Code execution through E2B."""
    print("🚀 Starting Claude Code + E2B Streaming Example")
    print("=" * 50)
    
    prompt = """
    I need you to analyze a dataset. Please:
    1. Create a simple Python script that generates some sample data
    2. Calculate basic statistics
    3. Create a simple visualization
    4. List the files in the current directory
    
    Make sure to use proper error handling.
    """
    
    try:
        async for event in claude_code_in_e2b(prompt):
            handle_stream_event(event)
            
    except Exception as e:
        print(f"❌ Error: {e}")


async def main():
    """Run all examples."""
    print("🎉 Claude Code + E2B Integration Examples")
    print("=" * 60)
    
    # Run examples
    await simple_streaming_example()
    
    # Uncomment for interactive mode
    # await interactive_streaming_example()
    
    print("\n🎯 All examples completed!")
    print("\nTo run interactive mode, uncomment the line in main() function")


import asyncio

if __name__ == "__main__":
    asyncio.run(main())