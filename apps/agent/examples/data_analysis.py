#!/usr/bin/env python3
"""
Simple data analysis script with sample data generation, statistics, and visualization.
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from typing import Dict, Any
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def generate_sample_data(n_samples: int = 1000) -> pd.DataFrame:
    """Generate sample dataset with multiple features."""
    try:
        np.random.seed(42)
        
        data = {
            'age': np.random.normal(35, 12, n_samples).astype(int),
            'income': np.random.exponential(50000, n_samples),
            'education_years': np.random.normal(14, 3, n_samples),
            'satisfaction_score': np.random.uniform(1, 10, n_samples),
            'category': np.random.choice(['A', 'B', 'C'], n_samples, p=[0.3, 0.5, 0.2])
        }
        
        data['age'] = np.clip(data['age'], 18, 80)
        data['education_years'] = np.clip(data['education_years'], 8, 20)
        
        df = pd.DataFrame(data)
        logger.info(f"Generated sample data with {len(df)} rows and {len(df.columns)} columns")
        return df
        
    except Exception as e:
        logger.error(f"Error generating sample data: {e}")
        raise

def calculate_basic_statistics(df: pd.DataFrame) -> Dict[str, Any]:
    """Calculate basic statistics for the dataset."""
    try:
        stats = {}
        
        numeric_columns = df.select_dtypes(include=[np.number]).columns
        
        for col in numeric_columns:
            stats[col] = {
                'mean': df[col].mean(),
                'median': df[col].median(),
                'std': df[col].std(),
                'min': df[col].min(),
                'max': df[col].max(),
                'q25': df[col].quantile(0.25),
                'q75': df[col].quantile(0.75)
            }
        
        categorical_columns = df.select_dtypes(include=['object']).columns
        for col in categorical_columns:
            stats[col] = {
                'unique_values': df[col].nunique(),
                'mode': df[col].mode().iloc[0] if not df[col].mode().empty else None,
                'value_counts': df[col].value_counts().to_dict()
            }
        
        logger.info("Basic statistics calculated successfully")
        return stats
        
    except Exception as e:
        logger.error(f"Error calculating statistics: {e}")
        raise

def create_visualizations(df: pd.DataFrame) -> None:
    """Create simple visualizations of the data."""
    try:
        plt.style.use('seaborn-v0_8')
        fig, axes = plt.subplots(2, 2, figsize=(12, 10))
        fig.suptitle('Data Analysis Visualizations', fontsize=16)
        
        df['age'].hist(bins=30, ax=axes[0, 0], alpha=0.7, color='skyblue')
        axes[0, 0].set_title('Age Distribution')
        axes[0, 0].set_xlabel('Age')
        axes[0, 0].set_ylabel('Frequency')
        
        axes[0, 1].scatter(df['age'], df['income'], alpha=0.6, color='coral')
        axes[0, 1].set_title('Age vs Income')
        axes[0, 1].set_xlabel('Age')
        axes[0, 1].set_ylabel('Income')
        
        category_counts = df['category'].value_counts()
        axes[1, 0].pie(category_counts.values, labels=category_counts.index, autopct='%1.1f%%')
        axes[1, 0].set_title('Category Distribution')
        
        df.boxplot(column='satisfaction_score', by='category', ax=axes[1, 1])
        axes[1, 1].set_title('Satisfaction Score by Category')
        axes[1, 1].set_xlabel('Category')
        axes[1, 1].set_ylabel('Satisfaction Score')
        
        plt.tight_layout()
        plt.savefig('data_analysis_plots.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        logger.info("Visualizations created and saved as 'data_analysis_plots.png'")
        
    except Exception as e:
        logger.error(f"Error creating visualizations: {e}")
        raise

def print_statistics_summary(stats: Dict[str, Any]) -> None:
    """Print a formatted summary of statistics."""
    try:
        print("\n" + "="*60)
        print("DATASET STATISTICS SUMMARY")
        print("="*60)
        
        for column, column_stats in stats.items():
            print(f"\n{column.upper()}:")
            print("-" * 30)
            
            if isinstance(column_stats, dict) and 'mean' in column_stats:
                print(f"  Mean: {column_stats['mean']:.2f}")
                print(f"  Median: {column_stats['median']:.2f}")
                print(f"  Std Dev: {column_stats['std']:.2f}")
                print(f"  Range: {column_stats['min']:.2f} - {column_stats['max']:.2f}")
                print(f"  IQR: {column_stats['q25']:.2f} - {column_stats['q75']:.2f}")
            else:
                print(f"  Unique Values: {column_stats['unique_values']}")
                print(f"  Mode: {column_stats['mode']}")
                print("  Value Counts:")
                for value, count in column_stats['value_counts'].items():
                    print(f"    {value}: {count}")
        
    except Exception as e:
        logger.error(f"Error printing statistics summary: {e}")
        raise

def main():
    """Main function to run the data analysis."""
    try:
        print("Starting data analysis...")
        
        df = generate_sample_data(1000)
        print(f"\nDataset shape: {df.shape}")
        print(f"Dataset info:")
        print(df.info())
        print(f"\nFirst 5 rows:")
        print(df.head())
        
        stats = calculate_basic_statistics(df)
        print_statistics_summary(stats)
        
        create_visualizations(df)
        
        print(f"\nData analysis completed successfully!")
        print(f"Visualization saved as 'data_analysis_plots.png'")
        
    except Exception as e:
        logger.error(f"Error in main execution: {e}")
        print(f"An error occurred: {e}")

if __name__ == "__main__":
    main()