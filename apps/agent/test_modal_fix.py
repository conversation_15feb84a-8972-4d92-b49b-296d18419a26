#!/usr/bin/env python3
"""Quick test to verify Modal Claude tracing fix."""

import asyncio
import os
import sys
import logging
from dotenv import load_dotenv

# Load environment variables
load_dotenv('../../.env')

# Add src to path
sys.path.insert(0, 'src')

from agents.claude_modal.modal_sandbox import create_sandbox, cleanup_sandbox
from agents.claude_modal.claude import run_claude_with_tracing

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_modal_tracing_fix():
    """Test that Modal Claude tracing works with parent_run parameter."""
    
    # Check environment
    if not os.getenv("ANTHROPIC_API_KEY"):
        logger.error("❌ ANTHROPIC_API_KEY not set")
        return False
        
    if not os.getenv("LANGSMITH_API_KEY"):
        logger.error("❌ LANGSMITH_API_KEY not set")
        return False
    
    sandbox = None
    try:
        logger.info("🚀 Creating Modal sandbox...")
        sandbox = await create_sandbox(
            repo_id=None,  # Use default
            timeout=300,
            use_snapshot=True
        )
        
        logger.info("🤖 Running Claude with tracing...")
        session = await run_claude_with_tracing(
            sandbox=sandbox,
            prompt="List the files in the current directory",
            claude_options={"max-turns": "3"},
            timeout=120,
            enable_tracing=True,
            parent_run=None  # No parent for this test
        )
        
        logger.info(f"✅ Session completed: {session.session_id}")
        logger.info(f"   📊 Total outputs: {len(session.outputs)}")
        logger.info(f"   ⏱️ Duration: {session.elapsed_time:.2f}s")
        
        # Check if tracing was enabled
        if session.trace_enabled:
            logger.info("✅ Tracing was enabled")
        else:
            logger.warning("⚠️ Tracing was not enabled")
            
        return True
        
    except Exception as e:
        logger.error(f"❌ Test failed: {e}")
        return False
        
    finally:
        if sandbox:
            logger.info("🧹 Cleaning up sandbox...")
            await cleanup_sandbox(sandbox)

if __name__ == "__main__":
    success = asyncio.run(test_modal_tracing_fix())
    if success:
        print("✅ Modal tracing fix test PASSED")
    else:
        print("❌ Modal tracing fix test FAILED")
