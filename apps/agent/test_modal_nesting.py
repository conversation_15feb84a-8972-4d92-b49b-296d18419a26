#!/usr/bin/env python3
"""
Test Modal Claude tracing with proper parent run nesting.
This should show Claude Code Session as a child under the LangGraph execution.
"""

import asyncio
import logging
import os
from datetime import datetime

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_modal_nesting():
    """Test Modal Claude tracing with parent run context."""
    try:
        # Import required modules
        from agents.claude_modal.claude import run_claude_with_tracing
        from agents.claude_modal.modal_sandbox import create_sandbox, cleanup_sandbox
        from langsmith.run_helpers import get_current_run_tree
        from langsmith import traceable
        
        logger.info("✅ Successfully imported Modal Claude and LangSmith modules")
        
        @traceable(name="🧪 Test Modal Nesting")
        async def test_with_parent_trace():
            """This function will be the parent trace."""
            logger.info("🚀 Starting parent trace...")
            
            # Get current run tree (this will be the parent)
            parent_run = get_current_run_tree()
            logger.info(f"📋 Parent run ID: {parent_run.id if parent_run else 'None'}")
            
            # Create Modal sandbox
            logger.info("🌟 Creating Modal sandbox...")
            sandbox = await create_sandbox(
                repo_id="test-repo",
                timeout=300,
                use_snapshot=False
            )
            
            try:
                # Run Claude with parent run context
                logger.info("🤖 Running Claude with parent run context...")
                session = await run_claude_with_tracing(
                    sandbox=sandbox,
                    prompt="List the files in the current directory and show me what's here",
                    claude_options={"max-turns": "3"},
                    timeout=120,
                    enable_tracing=True,
                    parent_run=parent_run  # 🎯 This should nest the Claude session
                )
                
                logger.info(f"✅ Claude session completed: {session.session_id}")
                logger.info(f"📊 Total outputs: {len(session.outputs)}")
                
                if session.run_tree:
                    logger.info(f"🔗 Claude trace ID: {session.run_tree.id}")
                    logger.info(f"🔗 Parent trace ID: {session.run_tree.parent_run.id if session.run_tree.parent_run else 'None'}")
                
                return session
                
            finally:
                # Clean up sandbox
                logger.info("🧹 Cleaning up sandbox...")
                await cleanup_sandbox(sandbox)
        
        # Run the test
        result = await test_with_parent_trace()
        
        logger.info("🎉 Test completed successfully!")
        logger.info("🔍 Check LangSmith to see if Claude Code Session appears as child under 'Test Modal Nesting'")
        
        return result
        
    except Exception as e:
        logger.error(f"💥 Test failed: {e}")
        import traceback
        traceback.print_exc()
        raise

if __name__ == "__main__":
    asyncio.run(test_modal_nesting())
