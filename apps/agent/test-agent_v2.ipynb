import logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')

# Test the fix
import os
os.environ["LANGCHAIN_API_KEY"] = "***************************************************"
os.environ["LANGCHAIN_TRACING_V2"] = "true"
os.environ["LANGCHAIN_PROJECT"] = "backspace-testing"

# BYPASS THE PROBLEMATIC __init__.py
import sys
sys.path.insert(0, 'src/agents')
import claude_e2b.claude as claude_module

prompt = "do ls"

print("🔍 Testing FIXED child nodes...")
output_count = 0

async for output in claude_module.stream_claude_in_sandbox(
    sandbox,
    prompt,
    claude_options={"max-turns": "3"},
    timeout=60,
    enable_tracing=True
):
    output_count += 1
    print(f"📤 Output {output_count}: {output.type}")
    
print(f"✅ STREAM COMPLETE - {output_count} outputs")
print("🌳 NOW CHECK LANGSMITH - YOU SHOULD SEE CHILD NODES!")

from agents.claude_e2b import (
    create_sandbox,
    stream_claude_in_sandbox,

)

from db import db_manager

await db_manager.connect()

sandbox = await create_sandbox(timeout=1000)

# Stream Claude outputs in real-time
async for output in stream_claude_in_sandbox(
    sandbox,
    "hey please fix this https://github.com/backspace-org/backspace-mono/issues/88",
    claude_options={"max-turns": "100"},
    timeout=0
):
    print(f"[{output.timestamp:.2f}] {output.type}: {str(output.content)[:100]}")
    
    # Special handling for different output types
    if output.type == "claude_message":
        print(f"  🤖 Claude said: {output.content}")
    elif output.type == "tool_call":
        print(f"  🔧 Tool: {output.content.get('name')}")
        if output.content.get('input'):
            print(f"      Input: {output.content.get('input')}")
    elif output.type == "tool_result":
        is_error = output.content.get('is_error', False)
        result_content = output.content.get('content', '')
        print(f"  {'❌' if is_error else '✅'} Tool Result: {result_content[:200]}...")
    elif output.type == "result":
        print(f"  🏁 Final result - Success: {not output.content.get('is_error')}")

print("\n=== STREAMING COMPLETE ===")

from db import db_manager
from agents.claude_e2b import create_sandbox

await db_manager.connect()
sandbox = await create_sandbox(timeout=1000)

# BYPASS THE PROBLEMATIC __init__.py
import sys
import os
sys.path.insert(0, 'src/agents')

# Import directly from the claude module
import claude_e2b.claude as claude_module
stream_claude_in_sandbox = claude_module.stream_claude_in_sandbox
# Set up tracing
os.environ["LANGCHAIN_API_KEY"] = os.getenv("LANGSMITH_API_KEY", "")
os.environ["LANGCHAIN_TRACING_V2"] = os.getenv("LANGSMITH_TRACING", "true")
os.environ["LANGCHAIN_PROJECT"] = os.getenv("LANGSMITH_PROJECT", "backspace-testing")


prompt = "hey please fix this https://github.com/backspace-org/backspace-mono/issues/88"

async for out in stream_claude_in_sandbox(
        sandbox,
        prompt,
        claude_options={"max-turns": "100"},
        timeout=0,
        enable_tracing=True  
    print(f"[{out.timestamp:.2f}] {out.type}: {str(out.content)[:120]}")
    
print("\n=== STREAM COMPLETE – CHECK LANGSMITH FOR TREE STRUCTURE! ===")



import os
import sys
# Set up tracing
os.environ["LANGCHAIN_API_KEY"] = os.getenv("LANGSMITH_API_KEY", "")
os.environ["LANGCHAIN_TRACING_V2"] = os.getenv("LANGSMITH_TRACING", "true")
os.environ["LANGCHAIN_PROJECT"] = os.getenv("LANGSMITH_PROJECT", "backspace-testing")

# Test LangSmith connection first
from langsmith import Client
client = Client()
print(f"✅ LangSmith client connected to: {client.api_url}")
print(f"✅ Project: {os.environ['LANGCHAIN_PROJECT']}")

# Now run your Claude code
from agents.claude_e2b import stream_claude_in_sandbox

prompt = "hey please fix this https://github.com/backspace-org/backspace-mono/issues/88"

print("🚀 Starting Claude session with tracing...")
session_id = None

async for out in stream_claude_in_sandbox(
        sandbox,
        prompt,
        claude_options={"max-turns": "100"},
        timeout=0
):
    if not session_id and hasattr(out, 'raw_event'):
        # Try to extract session ID from the first event
        event = out.raw_event or {}
        session_id = event.get('session_id', 'unknown')
        print(f"📋 Session ID: {session_id}")
    
    print(f"[{out.timestamp:.2f}] {out.type}: {str(out.content)[:120]}")

print(f"\n=== STREAM COMPLETE ===")
print(f"🔍 Check LangSmith for session: {session_id}")
print("📍 URL: https://smith.langchain.com/")
print("📂 Project: backspace-testing")

# Test the fix
import os
os.environ["LANGCHAIN_API_KEY"] = "***************************************************"
os.environ["LANGCHAIN_TRACING_V2"] = "true"
os.environ["LANGCHAIN_PROJECT"] = "backspace-testing"

# BYPASS THE PROBLEMATIC __init__.py
import sys
sys.path.insert(0, 'src/agents')
import claude_e2b.claude as claude_module

prompt = "do ls"

print("🔍 Testing FIXED child nodes...")
output_count = 0

async for output in claude_module.stream_claude_in_sandbox(
    sandbox,
    prompt,
    claude_options={"max-turns": "3"},
    timeout=60,
    enable_tracing=True
):
    output_count += 1
    print(f"📤 Output {output_count}: {output.type}")
    
print(f"✅ STREAM COMPLETE - {output_count} outputs")
print("🌳 NOW CHECK LANGSMITH - YOU SHOULD SEE CHILD NODES!")