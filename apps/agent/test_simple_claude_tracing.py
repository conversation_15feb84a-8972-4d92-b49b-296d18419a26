#!/usr/bin/env python3
"""
Simple test to verify Claude E2B tracing is working with <PERSON><PERSON><PERSON>.
This directly tests the run_claude_in_sandbox function with tracing enabled.
"""

import asyncio
import logging
import os
import sys
from pathlib import Path

# Load environment variables from .env file
from dotenv import load_dotenv
load_dotenv(Path(__file__).parent.parent / ".env")

# Add the src directory to Python path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from agents.claude_e2b.claude import run_claude_in_sandbox
from agents.claude_e2b import create_sandbox, cleanup_sandbox

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(levelname)s: %(message)s'
)
logger = logging.getLogger(__name__)

async def test_claude_e2b_tracing():
    """Test Claude E2B with Lang<PERSON>mith tracing enabled."""
    
    # Check environment variables
    required_env_vars = [
        "LANGSMITH_API_KEY",
        "LANGSMITH_TRACING", 
        "LANGSMITH_PROJECT",
        "E2B_API_KEY",
        "ANTHROPIC_API_KEY"
    ]
    
    missing_vars = [var for var in required_env_vars if not os.getenv(var)]
    if missing_vars:
        logger.error(f"❌ Missing environment variables: {missing_vars}")
        return False
    
    logger.info("✅ All required environment variables found")
    logger.info(f"🔍 LangSmith Project: {os.getenv('LANGSMITH_PROJECT')}")
    
    sandbox = None
    try:
        # Create E2B sandbox using the proper function
        logger.info("🚀 Creating E2B sandbox...")
        sandbox = await create_sandbox(
            repo_id=None,  # Use GH_TOKEN from env instead of database
            template_id="vcomjjr43nxwhfxodbqm",  # Claude Code template
            timeout=300
        )
        logger.info(f"✅ Created sandbox: {sandbox.sandbox_id}")

        # Environment is already set up by create_sandbox function
        logger.info("✅ Sandbox environment configured automatically")
        
        # Simple test prompt
        test_prompt = """
        Please analyze this simple task:
        1. List the current directory contents
        2. Create a simple test file called 'hello.txt' with content 'Hello World'
        3. Read the file back to verify it was created
        
        Keep this simple and quick for testing purposes.
        """
        
        logger.info("🎯 Running Claude with tracing enabled...")
        logger.info("📊 This should create LangSmith traces showing:")
        logger.info("   - Top-level: Claude CLI execution")
        logger.info("   - Child traces: System Init, Claude Messages, Tool Calls, Tool Results")
        logger.info("   - Hierarchical structure with tool results nested under tool calls")
        
        # Run Claude with tracing enabled
        session = await run_claude_in_sandbox(
            sandbox=sandbox,
            prompt=test_prompt,
            claude_options={"max-turns": "3"},  # Limit for quick test
            enable_tracing=True,  # This should enable LangSmith tracing
            timeout=120
        )
        
        logger.info("✅ Claude execution completed!")
        logger.info(f"📊 Session ID: {session.session_id}")
        logger.info(f"📝 Total outputs: {len(session.outputs)}")
        logger.info(f"💰 Total cost: ${session.total_cost:.4f}")
        
        # Show some sample outputs
        if session.outputs:
            logger.info("📋 Sample outputs:")
            for i, output in enumerate(session.outputs[:3]):  # Show first 3
                logger.info(f"   {i+1}. {output.type}: {str(output.content)[:100]}...")
        
        # Show LangSmith information
        project_name = os.getenv('LANGSMITH_PROJECT')
        logger.info("🔗 Check your LangSmith traces at:")
        logger.info(f"   https://smith.langchain.com/projects/{project_name}")
        logger.info("🎯 Look for traces with:")
        logger.info("   - Top-level: Claude CLI execution")
        logger.info("   - Child traces: System Init, Claude Messages, Tool Calls, Tool Results")
        logger.info("   - Tool results nested under their corresponding tool calls")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Test failed with exception: {e}")
        import traceback
        logger.error(f"📋 Full traceback:\n{traceback.format_exc()}")
        return False
    
    finally:
        # Clean up sandbox
        if sandbox:
            try:
                logger.info("🧹 Cleaning up sandbox...")
                await cleanup_sandbox(sandbox)
                logger.info("✅ Sandbox cleaned up")
            except Exception as e:
                logger.warning(f"⚠️ Failed to clean up sandbox: {e}")

async def main():
    """Main test function."""
    logger.info("🧪 Testing Claude E2B LangSmith tracing...")
    logger.info("=" * 60)
    
    success = await test_claude_e2b_tracing()
    
    logger.info("=" * 60)
    if success:
        logger.info("✅ Test PASSED - Claude E2B tracing is working!")
        logger.info("🎉 Your LangSmith integration is functioning correctly")
        logger.info("💡 The same tracing logic is used by claude_scanner automatically")
    else:
        logger.info("❌ Test FAILED - check the errors above")
    
    return success

if __name__ == "__main__":
    # Run the test
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
