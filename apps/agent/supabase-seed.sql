-- Supabase seed script for Scan, Section, and Issue tables
-- Run this in Supabase SQL Editor or via CLI

-- Clear existing data (optional - be careful in production!)
-- DELETE FROM "Issue";
-- DELETE FROM "Section";
-- DELETE FROM "Scan";

-- Insert Scans
INSERT INTO "Scan" (id, created_at, trigger, status, description, issues_found, duration, token_cost, creator, repo_id, overview) VALUES
(1, '2025-01-06T10:30:00Z', 'manual', 'completed', 'Comprehensive code quality analysis analyzing 324 files with 45,678 lines of code. This automated assessment evaluates code quality, identifies improvement opportunities, and provides actionable recommendations for enhanced maintainability and performance.', 7, '00:02:45', '0.453', '<EMAIL>', 990861151, 'Overall health score: 72%. Key areas for improvement include test coverage (65%), security vulnerabilities (2 critical), and performance optimizations needed in database queries.'),
(2, '2025-01-06T08:15:00Z', 'pr', 'completed', 'Pull request validation scan analyzing 87 files with 12,340 lines of code. Automated code review focusing on security, performance, and maintainability standards before merge.', 5, '00:01:30', '0.234', '<EMAIL>', 990861151, 'PR health score: 81%. Minor issues detected in code clarity and modularity. No critical security issues found.');

-- Insert Sections
INSERT INTO "Section" (id, created_at, name, summary, scan_id, incident_id) VALUES
(1, '2025-01-06T10:30:00Z', 'Security', '2 security vulnerabilities detected requiring immediate attention', 1, NULL),
(2, '2025-01-06T10:30:00Z', 'Performance', 'Database query optimizations and bundle size improvements recommended', 1, NULL),
(3, '2025-01-06T10:30:00Z', 'Test Coverage', 'Critical business logic lacks proper test coverage', 1, NULL),
(4, '2025-01-06T10:30:00Z', 'Fault Resilience', 'Error handling improvements needed for production stability', 1, NULL),
(5, '2025-01-06T08:15:00Z', 'Clarity', 'Code complexity and documentation improvements recommended', 2, NULL),
(6, '2025-01-06T08:15:00Z', 'Modularity', 'Tight coupling detected between components', 2, NULL),
(7, '2025-01-06T08:15:00Z', 'Agent Readiness', 'Code documentation and type annotations needed for AI assistance', 2, NULL);

-- Insert Issues
INSERT INTO "Issue" (id, created_at, name, description, section_id, section_name, status, pr_link) VALUES
(1, '2025-01-06T10:30:00Z', 'Critical XSS vulnerability in comment sections', '**Issue:** Critical XSS vulnerability in comment sections allowing script injection and potential data theft.\n\n**Vulnerability Details:**\n- **CVE:** Pending assignment\n- **CVSS Score:** 8.2 (High)\n- **Vector:** Stored XSS in user comments and profile descriptions\n\n**Attack Scenario:**\n1. Attacker posts comment with malicious script\n2. Script executes when other users view the comment\n3. User data sent to attacker''s server\n\n**Affected Components:**\n- Comment system (/components/comments/CommentBox.tsx)\n- User profiles (/components/profile/ProfileEditor.tsx)\n\n**Security Impact:**\n- Potential exposure of user sessions\n- Risk of account takeover\n- Compliance violation (SOC 2, GDPR)', 1, 'Security', 'running', NULL),
(2, '2025-01-06T10:30:00Z', 'SQL injection vulnerability in search endpoint', '**Issue:** User input not properly sanitized in database queries\n\n**Technical Details:**\n- Endpoint: `/api/search`\n- Method: GET\n- Parameter: `query` (vulnerable)\n\n**Example:**\n```sql\nSELECT * FROM products WHERE name LIKE ''%${userInput}%''\n```\n\n**Impact:**\n- Database access compromise\n- Data exfiltration risk\n- Service disruption possibility\n\n**Fix Required:**\n- Use parameterized queries\n- Input validation\n- Escape special characters', 1, 'Security', 'completed', 'https://github.com/company/repo/pull/234'),
(3, '2025-01-06T10:30:00Z', 'N+1 query pattern in dashboard loading', '**Issue:** Dashboard loading time increased to 15-20 seconds during peak usage\n\n**Performance Metrics:**\n- Average query time: 8.5s (target: <2s)\n- Database CPU usage: 89% during peak hours\n\n**Problematic Query:**\n```javascript\nusers.forEach(async (user) => {\n  const profile = await getUserProfile(user.id);\n  const settings = await getUserSettings(user.id);\n  const projects = await getUserProjects(user.id);\n});\n```\n\n**Optimization Required:**\n- Implement query batching\n- Use JOIN operations\n- Add caching layer', 2, 'Performance', 'running', NULL),
(4, '2025-01-06T10:30:00Z', 'Missing unit tests for payment processing', '**Issue:** Critical business logic lacks proper test coverage\n\n**Uncovered Functions:**\n- `processPayment()` - 0% coverage\n- `validateTransaction()` - 0% coverage\n- `handleRefund()` - 0% coverage\n\n**Risk Assessment:**\n- High risk of regression bugs\n- Payment calculation errors\n- Compliance requirements not met\n\n**Required Tests:**\n- Happy path scenarios\n- Edge cases (zero amounts, currency conversion)\n- Error handling\n- Integration with payment gateway', 3, 'Test Coverage', 'completed', 'https://github.com/company/repo/pull/245'),
(5, '2025-01-06T10:30:00Z', 'Integration tests missing for API endpoints', '**Issue:** API endpoints lack integration test coverage\n\n**Affected Endpoints:**\n- POST `/api/users` - No tests\n- PUT `/api/users/:id` - No tests\n- DELETE `/api/users/:id` - No tests\n\n**Test Coverage Gap:**\n- Current: 23%\n- Target: 80%\n\n**Implementation Plan:**\n- Set up test database\n- Create test fixtures\n- Implement end-to-end tests\n- Add CI/CD integration', 3, 'Test Coverage', 'running', NULL),
(6, '2025-01-06T10:30:00Z', 'Missing error boundaries in React components', '**Issue:** Application crashes completely on component errors\n\n**Affected Areas:**\n- Dashboard components\n- Data visualization charts\n- Form components\n\n**Current Behavior:**\n- White screen of death on errors\n- No fallback UI\n- Lost user context\n\n**Solution Required:**\n- Implement React Error Boundaries\n- Add fallback UI components\n- Error logging to monitoring service\n- Graceful degradation strategy', 4, 'Fault Resilience', 'completed', 'https://github.com/company/repo/pull/256'),
(7, '2025-01-06T08:15:00Z', 'Complex function with high cognitive complexity', '**Issue:** Function has too many nested conditions\n\n**File:** `/src/utils/dataProcessor.ts`\n**Function:** `processUserData()`\n**Complexity Score:** 32 (threshold: 10)\n\n**Problems:**\n- 8 levels of nesting\n- 156 lines in single function\n- Multiple responsibilities\n\n**Refactoring Plan:**\n- Extract validation logic\n- Create separate processing functions\n- Implement strategy pattern\n- Add clear documentation', 5, 'Clarity', 'running', NULL),
(8, '2025-01-06T08:15:00Z', 'Circular dependency between auth and user modules', '**Issue:** Modules have circular import dependencies\n\n**Dependency Chain:**\n```\nauth.service.ts → user.service.ts → auth.guard.ts → auth.service.ts\n```\n\n**Impact:**\n- Build time increased\n- Testing complexity\n- Maintenance difficulty\n\n**Resolution:**\n- Extract shared interfaces\n- Implement dependency injection\n- Create auth-core module\n- Refactor service boundaries', 6, 'Modularity', 'completed', 'https://github.com/company/repo/pull/267'),
(9, '2025-01-06T08:15:00Z', 'Missing TypeScript types for API responses', '**Issue:** Functions lack proper type definitions\n\n**Affected Files:**\n- `/src/api/client.ts` - 15 untyped functions\n- `/src/utils/transform.ts` - 8 untyped functions\n- `/src/services/data.ts` - 12 untyped functions\n\n**Current State:**\n```typescript\nfunction fetchUserData(id) {\n  // No type annotations\n  return api.get(`/users/${id}`);\n}\n```\n\n**Required:**\n- Define response interfaces\n- Add function signatures\n- Type API client methods\n- Enable strict TypeScript mode', 7, 'Agent Readiness', 'running', NULL);

-- Reset sequences if using auto-increment (adjust based on your schema)
-- SELECT setval('"Scan_id_seq"', 2);
-- SELECT setval('"Section_id_seq"', 7);
-- SELECT setval('"Issue_id_seq"', 9);