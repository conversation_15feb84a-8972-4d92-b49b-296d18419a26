#!/usr/bin/env python3
"""Run Claude-Coder pipeline on a specific branch."""

import asyncio
import logging
from agents.claude_coder.deployment import graph
from agents.claude_coder.states import ClaudeCoderState

# Enable detailed logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

async def run_claude_coder(branch_name: str, base_branch: str = "main", repo_path: str = ""):
    """Run the Claude-Coder pipeline on a specific branch."""
    
    print(f"🚀 Starting Claude-Coder Pipeline")
    print(f"📍 Branch: {branch_name}")
    print(f"📍 Base: {base_branch}")
    print(f"📍 Repo: {repo_path or 'auto-detect'}")
    print("=" * 50)
    
    # Create initial state
    initial_state: ClaudeCoderState = {
        "sandbox": None,
        "branch_name": branch_name,
        "base_branch": base_branch,
        "repo_path": repo_path,
        "current_phase": "",
        "phase_results": {},
        "pr_url": None,
        "error": None,
        "claude_options": {
            "max-turns": "15",  # Allow more turns for complex tasks
        }
    }
    
    try:
        # Run the graph
        print("⚡ Executing pipeline...")
        result = await graph.ainvoke(initial_state)
        
        # Display results
        print("\n" + "=" * 50)
        print("📊 PIPELINE RESULTS")
        print("=" * 50)
        
        error = result.get("error")
        if error:
            print(f"❌ Pipeline failed: {error}")
            return False
        
        phase_results = result.get("phase_results", {})
        pr_url = result.get("pr_url")
        
        print("✅ Pipeline completed successfully!")
        print(f"\n📈 Phases executed: {len(phase_results)}")
        
        for phase, session in phase_results.items():
            if session:
                status = "✅ Success" if session.success else "❌ Failed"
                cost = f"${session.total_cost_usd:.4f}" if hasattr(session, 'total_cost_usd') and session.total_cost_usd else "N/A"
                duration = f"{session.duration_ms/1000:.1f}s" if hasattr(session, 'duration_ms') and session.duration_ms else "N/A"
                print(f"   🔹 {phase.upper()}: {status} (Cost: {cost}, Duration: {duration})")
        
        if pr_url:
            print(f"\n🔗 Pull Request: {pr_url}")
        
        total_cost = sum(
            session.total_cost_usd for session in phase_results.values() 
            if session and hasattr(session, 'total_cost_usd') and session.total_cost_usd
        )
        if total_cost > 0:
            print(f"💰 Total cost: ${total_cost:.4f}")
        
        return True
        
    except Exception as e:
        print(f"\n💥 Pipeline crashed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Main entry point."""
    # 🔧 CONFIGURE YOUR RUN HERE
    branch_name = "roland/hooks"  # Change this to your branch
    base_branch = "main"          # Base branch for comparison
    repo_path = ""                # Leave empty for auto-detect
    
    success = await run_claude_coder(
        branch_name=branch_name,
        base_branch=base_branch,
        repo_path=repo_path
    )
    
    if success:
        print("\n🎉 Claude-Coder pipeline completed successfully!")
    else:
        print("\n💔 Claude-Coder pipeline failed. Check logs above.")

if __name__ == "__main__":
    asyncio.run(main())