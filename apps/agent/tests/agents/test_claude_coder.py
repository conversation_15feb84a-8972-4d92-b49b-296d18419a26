"""Tests for Claude-Code<PERSON> agent."""

import pytest
from unittest.mock import As<PERSON><PERSON><PERSON>, <PERSON><PERSON>ock, patch

from agents.claude_coder import Claude<PERSON>oder<PERSON><PERSON>
from agents.claude_coder.prompts import (
    get_modularize_prompts,
    get_build_prompts,
    get_test_prompts,
    get_doc_prompts
)
from agents.claude_coder.states import Claude<PERSON>oderState
from agents.claude_e2b.claude import <PERSON><PERSON>ess<PERSON>, ClaudeOutput

pytestmark = pytest.mark.anyio


class TestClaudeCoderPrompts:
    """Test prompt generation functions."""
    
    def test_modularize_prompts(self):
        """Test modularize prompt generation."""
        system_prompt, user_prompt = get_modularize_prompts(
            branch_name="feature/new-auth",
            base_branch="main",
            repo_path="/home/<USER>/project"
        )
        
        # Check system prompt
        assert "expert software architect" in system_prompt
        assert "SOLID principles" in system_prompt
        assert "modularization" in system_prompt
        
        # Check user prompt
        assert "feature/new-auth" in user_prompt
        assert "main" in user_prompt
        assert "/home/<USER>/project" in user_prompt
        assert "git diff main...feature/new-auth" in user_prompt
    
    def test_build_prompts(self):
        """Test build prompt generation."""
        system_prompt, user_prompt = get_build_prompts(
            branch_name="feature/new-auth",
            repo_path="/home/<USER>/project"
        )
        
        # Check system prompt
        assert "build engineer" in system_prompt
        assert "dynamic programming approach" in system_prompt
        assert "build incrementally" in system_prompt
        
        # Check user prompt
        assert "feature/new-auth" in user_prompt
        assert "/home/<USER>/project" in user_prompt
        assert "npm run dev > /dev/null 2>&1 & echo $!" in user_prompt
        assert "kill <PID>" in user_prompt
    
    def test_test_prompts(self):
        """Test test prompt generation."""
        system_prompt, user_prompt = get_test_prompts(
            branch_name="feature/new-auth",
            base_branch="main"
        )
        
        # Check system prompt
        assert "QA engineer" in system_prompt
        assert "Jest" in system_prompt
        assert "AAA pattern" in system_prompt
        
        # Check user prompt
        assert "feature/new-auth" in user_prompt
        assert "main" in user_prompt
        assert "npm test" in user_prompt
        assert "test: add tests for feature/new-auth features" in user_prompt
    
    def test_doc_prompts(self):
        """Test doc prompt generation."""
        system_prompt, user_prompt = get_doc_prompts(
            branch_name="feature/new-auth",
            base_branch="main"
        )
        
        # Check system prompt
        assert "technical documentation expert" in system_prompt
        assert "JSDoc" in system_prompt
        assert "@param, @returns, @throws, @example" in system_prompt
        
        # Check user prompt
        assert "feature/new-auth" in user_prompt
        assert "main" in user_prompt
        assert "docs: add documentation for feature/new-auth" in user_prompt
        assert "Create or update PR" in user_prompt


class TestClaudeCoderAgent:
    """Test Claude-Coder agent functionality."""
    
    async def test_agent_initialization(self):
        """Test agent initialization."""
        agent = ClaudeCoderAgent()
        
        # Check that the agent was created successfully
        assert agent is not None
        assert agent.graph is not None
        assert agent.llm is not None
        assert agent.use_sandbox is False  # We manage E2B sandbox ourselves
    
    def test_get_initial_state(self):
        """Test initial state generation."""
        agent = ClaudeCoderAgent()
        
        # Test with defaults
        state = agent.get_initial_state()
        assert state["sandbox"] is None
        assert state["branch_name"] == ""
        assert state["base_branch"] == "main"
        assert state["repo_path"] == ""
        assert state["current_phase"] == ""
        assert state["phase_results"] == {}
        assert state["pr_url"] is None
        assert state["error"] is None
        assert state["claude_options"] == {"max-turns": "10"}
        
        # Test with custom values
        state = agent.get_initial_state(
            branch_name="feature/test",
            base_branch="develop",
            repo_path="/custom/path",
            claude_options={"max-turns": "20"}
        )
        assert state["branch_name"] == "feature/test"
        assert state["base_branch"] == "develop"
        assert state["repo_path"] == "/custom/path"
        assert state["claude_options"] == {"max-turns": "20"}
    
    @patch("agents.claude_coder.graph.create_sandbox")
    @patch("agents.claude_coder.graph.run_claude_in_sandbox")
    @patch("agents.claude_coder.graph.cleanup_sandbox")
    async def test_execute_success(self, mock_cleanup, mock_run_claude, mock_create_sandbox):
        """Test successful pipeline execution."""
        # Mock sandbox creation
        mock_sandbox = AsyncMock()
        mock_sandbox.commands.run = AsyncMock(return_value=MagicMock(exit_code=0))
        mock_create_sandbox.return_value = mock_sandbox
        
        # Mock Claude sessions for each phase
        def create_session(phase: str, success: bool = True):
            session = ClaudeSession(
                session_id=f"test-{phase}",
                prompt=f"Test {phase} prompt"
            )
            session.success = success
            session.total_cost_usd = 0.05
            return session
        
        # Mock different sessions for each phase
        mock_run_claude.side_effect = [
            create_session("modularize"),
            create_session("build"),
            create_session("test"),
            create_session("doc")
        ]
        
        # Create agent and execute
        agent = ClaudeCoderAgent()
        result = await agent.execute("Test task")
        
        # Verify calls
        assert mock_create_sandbox.called
        assert mock_run_claude.call_count == 4  # All 4 phases
        assert mock_cleanup.called
        
        # Check result
        assert "completed successfully" in result
    
    @patch("agents.claude_coder.graph.create_sandbox")
    @patch("agents.claude_coder.graph.run_claude_in_sandbox")
    @patch("agents.claude_coder.graph.cleanup_sandbox")
    async def test_execute_failure_in_build(self, mock_cleanup, mock_run_claude, mock_create_sandbox):
        """Test pipeline failure during build phase."""
        # Mock sandbox creation
        mock_sandbox = AsyncMock()
        mock_sandbox.commands.run = AsyncMock(return_value=MagicMock(exit_code=0))
        mock_create_sandbox.return_value = mock_sandbox
        
        # Mock Claude sessions - fail at build
        def create_session(phase: str, success: bool = True):
            session = ClaudeSession(
                session_id=f"test-{phase}",
                prompt=f"Test {phase} prompt"
            )
            session.success = success
            session.error = f"{phase} failed" if not success else None
            session.total_cost_usd = 0.05
            return session
        
        mock_run_claude.side_effect = [
            create_session("modularize", success=True),
            create_session("build", success=False)  # Fail here
        ]
        
        # Create agent and execute
        agent = ClaudeCoderAgent()
        result = await agent.execute("Test task")
        
        # Verify calls
        assert mock_create_sandbox.called
        assert mock_run_claude.call_count == 2  # Only modularize and build
        assert mock_cleanup.called  # Cleanup should still happen
        
        # Check result
        assert "failed" in result.lower()
        assert "build failed" in result


class TestClaudeCoderGraph:
    """Test the Claude-Coder graph directly."""
    
    @patch("agents.claude_coder.graph.subprocess.run")
    async def test_git_branch_detection(self, mock_subprocess):
        """Test git branch detection."""
        from agents.claude_coder.graph import ClaudeCoderGraph
        
        # Mock git branch command
        mock_subprocess.return_value = MagicMock(
            stdout="feature/test-branch\n",
            returncode=0
        )
        
        graph = ClaudeCoderGraph()
        branch = graph._get_current_branch()
        
        assert branch == "feature/test-branch"
        mock_subprocess.assert_called_with(
            ["git", "branch", "--show-current"],
            capture_output=True,
            text=True,
            check=True
        )
    
    @patch("agents.claude_coder.graph.create_sandbox")
    @patch("agents.claude_coder.graph.cleanup_sandbox")
    async def test_graph_streaming(self, mock_cleanup, mock_create_sandbox):
        """Test graph streaming capabilities."""
        from agents.claude_coder.deployment import graph
        
        # Mock sandbox
        mock_sandbox = AsyncMock()
        mock_sandbox.commands.run = AsyncMock(return_value=MagicMock(exit_code=0))
        mock_create_sandbox.return_value = mock_sandbox
        
        # Create initial state
        initial_state: ClaudeCoderState = {
            "sandbox": None,
            "branch_name": "test-branch",
            "base_branch": "main",
            "repo_path": "/test/repo",
            "current_phase": "",
            "phase_results": {},
            "pr_url": None,
            "error": None,
            "claude_options": {"max-turns": "5"}
        }
        
        # Test streaming
        events = []
        async for event in graph.astream(initial_state):
            events.append(event)
            # Stop after sandbox creation to avoid full pipeline
            if "create_sandbox" in event:
                break
        
        assert len(events) > 0
        assert mock_create_sandbox.called


class TestDeployment:
    """Test deployment configuration."""
    
    def test_deployment_exports(self):
        """Test that deployment.py exports the correct objects."""
        from agents.claude_coder.deployment import agent, graph
        
        assert agent is not None
        assert graph is not None
        assert agent.graph == graph
    
    async def test_direct_graph_invocation(self):
        """Test invoking the graph directly from deployment."""
        from agents.claude_coder.deployment import graph
        from agents.claude_coder.states import ClaudeCoderState
        
        # Create state
        state: ClaudeCoderState = {
            "sandbox": None,
            "branch_name": "test",
            "base_branch": "main",
            "repo_path": "/test",
            "current_phase": "",
            "phase_results": {},
            "pr_url": None,
            "error": None,
            "claude_options": {}
        }
        
        # Mock the graph execution to avoid actual API calls
        with patch.object(graph, 'ainvoke') as mock_ainvoke:
            mock_ainvoke.return_value = {
                **state,
                "current_phase": "complete",
                "phase_results": {
                    "modularize": MagicMock(success=True),
                    "build": MagicMock(success=True),
                    "test": MagicMock(success=True),
                    "doc": MagicMock(success=True)
                }
            }
            
            result = await graph.ainvoke(state)
            
            assert result["current_phase"] == "complete"
            assert len(result["phase_results"]) == 4
            mock_ainvoke.assert_called_once_with(state)