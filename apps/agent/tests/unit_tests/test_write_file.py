import pytest
import tempfile
import os
import sys
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from agents.tools.local_tools import write_file

# Mark all tests in this module as async using anyio
pytestmark = pytest.mark.anyio


class TestWriteFile:
    """Test suite for the write_file tool using TDD approach."""
    
    @pytest.fixture
    def temp_dir(self):
        """Create a temporary directory for testing."""
        with tempfile.TemporaryDirectory() as tmpdir:
            yield tmpdir
    
    async def test_write_new_file(self, temp_dir):
        """Test writing content to a new file."""
        file_path = os.path.join(temp_dir, "new_file.txt")
        content = "Hello, World!\nThis is a test file."
        
        result = await write_file.ainvoke({
            "file_path": file_path,
            "content": content
        })
        
        # Should succeed
        assert "✅" in result
        assert "created" in result.lower() or "written" in result.lower()
        
        # Verify the file was created with correct content
        assert os.path.exists(file_path)
        with open(file_path, 'r') as f:
            assert f.read() == content
    
    async def test_write_python_file(self, temp_dir):
        """Test writing a Python file with proper content."""
        file_path = os.path.join(temp_dir, "example.py")
        content = '''def hello_world():
    """Print a greeting message."""
    print("Hello, World!")
    return True

if __name__ == "__main__":
    hello_world()
'''
        
        result = await write_file.ainvoke({
            "file_path": file_path,
            "content": content
        })
        
        assert "✅" in result
        
        # Verify content
        with open(file_path, 'r') as f:
            assert f.read() == content
    
    async def test_overwrite_existing_file(self, temp_dir):
        """Test overwriting an existing file."""
        file_path = os.path.join(temp_dir, "existing.txt")
        
        # Create initial file
        with open(file_path, 'w') as f:
            f.write("Original content")
        
        new_content = "New content that replaces the old"
        
        result = await write_file.ainvoke({
            "file_path": file_path,
            "content": new_content
        })
        
        assert "✅" in result
        # Should indicate it's overwriting
        assert "overwrote" in result.lower() or "replaced" in result.lower() or "written" in result.lower()
        
        # Verify new content
        with open(file_path, 'r') as f:
            assert f.read() == new_content
    
    async def test_write_empty_file(self, temp_dir):
        """Test creating an empty file."""
        file_path = os.path.join(temp_dir, "empty.txt")
        
        result = await write_file.ainvoke({
            "file_path": file_path,
            "content": ""
        })
        
        assert "✅" in result
        assert os.path.exists(file_path)
        
        # Verify file is empty
        with open(file_path, 'r') as f:
            assert f.read() == ""
    
    async def test_write_with_subdirectories(self, temp_dir):
        """Test creating file in non-existent subdirectories."""
        file_path = os.path.join(temp_dir, "sub", "dir", "file.txt")
        content = "File in nested directory"
        
        result = await write_file.ainvoke({
            "file_path": file_path,
            "content": content
        })
        
        assert "✅" in result
        assert os.path.exists(file_path)
        
        with open(file_path, 'r') as f:
            assert f.read() == content
    
    async def test_write_to_directory_path(self, temp_dir):
        """Test error when file_path points to a directory."""
        # temp_dir is already a directory
        result = await write_file.ainvoke({
            "file_path": temp_dir,
            "content": "some content"
        })
        
        assert "❌" in result
        assert "directory" in result.lower()
    
    async def test_write_permission_error(self, temp_dir):
        """Test handling of permission errors."""
        file_path = os.path.join(temp_dir, "readonly.txt")
        
        # Create a file and make its directory read-only
        with open(file_path, 'w') as f:
            f.write("initial")
        
        # Make directory read-only
        os.chmod(temp_dir, 0o555)
        
        try:
            result = await write_file.ainvoke({
                "file_path": os.path.join(temp_dir, "new_file.txt"),
                "content": "This should fail"
            })
            
            assert "❌" in result
            assert "permission" in result.lower() or "error" in result.lower()
        finally:
            # Restore permissions for cleanup
            os.chmod(temp_dir, 0o755)
    
    async def test_write_special_characters(self, temp_dir):
        """Test writing content with special characters and encodings."""
        file_path = os.path.join(temp_dir, "unicode.txt")
        content = "Hello, 世界! 🌍\nSpecial chars: €£¥\n\tTabbed content"
        
        result = await write_file.ainvoke({
            "file_path": file_path,
            "content": content
        })
        
        assert "✅" in result
        
        with open(file_path, 'r', encoding='utf-8') as f:
            assert f.read() == content
    
    async def test_write_large_file(self, temp_dir):
        """Test writing a large file."""
        file_path = os.path.join(temp_dir, "large.txt")
        # Create 1MB of content
        content = "x" * (1024 * 1024)
        
        result = await write_file.ainvoke({
            "file_path": file_path,
            "content": content
        })
        
        assert "✅" in result
        assert os.path.getsize(file_path) == len(content)
    
    async def test_write_invalid_path(self):
        """Test error handling for invalid file paths."""
        # Try to write to a path with null bytes
        result = await write_file.ainvoke({
            "file_path": "/tmp/test\x00file.txt",
            "content": "test"
        })
        
        assert "❌" in result
        assert "error" in result.lower()
    
    async def test_write_preserves_line_endings(self, temp_dir):
        """Test that line endings are preserved correctly."""
        file_path = os.path.join(temp_dir, "lineendings.txt")
        
        # Content with explicit line endings
        content = "Line 1\nLine 2\nLine 3"
        
        result = await write_file.ainvoke({
            "file_path": file_path,
            "content": content
        })
        
        assert "✅" in result
        
        with open(file_path, 'rb') as f:
            raw_content = f.read()
            # Should preserve \n line endings
            assert b'\r\n' not in raw_content  # No Windows line endings
            assert raw_content.count(b'\n') == 2
    
    async def test_write_json_file(self, temp_dir):
        """Test writing a JSON file."""
        file_path = os.path.join(temp_dir, "config.json")
        content = '''{
    "name": "test",
    "version": "1.0.0",
    "settings": {
        "debug": true,
        "port": 8080
    }
}'''
        
        result = await write_file.ainvoke({
            "file_path": file_path,
            "content": content
        })
        
        assert "✅" in result
        
        # Verify it's valid JSON
        import json
        with open(file_path, 'r') as f:
            data = json.load(f)
            assert data["name"] == "test"
            assert data["settings"]["port"] == 8080