"""Basic unit tests for Claude E2B integration - starting from the ground up."""

import pytest
import json
from unittest.mock import Mock, AsyncMock, patch
from datetime import datetime

from agents.claude_e2b.claude import (
    <PERSON><PERSON>utput,
    ClaudeSession,
    handle_claude_stream
)


class TestClaudeOutput:
    """Test ClaudeOutput dataclass functionality."""
    
    def test_claude_output_creation(self):
        """Test basic ClaudeOutput creation."""
        timestamp = datetime.now().timestamp()
        output = ClaudeOutput(
            timestamp=timestamp,
            type="claude_message",
            content="Hello world"
        )
        
        assert output.timestamp == timestamp
        assert output.type == "claude_message"
        assert output.content == "Hello world"
        assert output.raw_event is None
    
    def test_claude_output_with_raw_event(self):
        """Test ClaudeOutput with raw event data."""
        raw_event = {"type": "assistant", "message": {"content": [{"type": "text", "text": "Hello"}]}}
        output = ClaudeOutput(
            timestamp=123.45,
            type="claude_message",
            content="Hello",
            raw_event=raw_event
        )
        
        assert output.raw_event == raw_event
    
    def test_claude_output_str_representation(self):
        """Test string representation of ClaudeOutput."""
        output = ClaudeOutput(
            timestamp=123.45,
            type="test_type",
            content="test content"
        )
        
        expected = "[123.45] test_type: test content"
        assert str(output) == expected


class TestClaudeSession:
    """Test ClaudeSession functionality."""
    
    def test_claude_session_creation(self):
        """Test basic ClaudeSession creation."""
        session = ClaudeSession(
            session_id="test-123",
            prompt="Test prompt"
        )
        
        assert session.session_id == "test-123"
        assert session.prompt == "Test prompt"
        assert session.outputs == []
        assert session.end_time is None
        assert session.success is False
        assert session.error is None
    
    def test_add_output_to_session(self):
        """Test adding outputs to a session."""
        session = ClaudeSession(session_id="test", prompt="test")
        output = ClaudeOutput(
            timestamp=123.45,
            type="test",
            content="test"
        )
        
        session.add_output(output)
        
        assert len(session.outputs) == 1
        assert session.outputs[0] == output
    
    def test_session_finalize_success(self):
        """Test finalizing a session with success."""
        session = ClaudeSession(session_id="test", prompt="test")
        
        session.finalize(success=True)
        
        assert session.success is True
        assert session.error is None
        assert session.end_time is not None
    
    def test_session_finalize_with_error(self):
        """Test finalizing a session with an error."""
        session = ClaudeSession(session_id="test", prompt="test")
        error_msg = "Something went wrong"
        
        session.finalize(success=False, error=error_msg)
        
        assert session.success is False
        assert session.error == error_msg
        assert session.end_time is not None
    
    def test_elapsed_time_calculation(self):
        """Test elapsed time calculation."""
        session = ClaudeSession(session_id="test", prompt="test")
        
        # Before finalization, should return current elapsed time
        elapsed_before = session.elapsed_time
        assert elapsed_before >= 0
        
        # After finalization, should return fixed elapsed time
        session.finalize(success=True)
        elapsed_after = session.elapsed_time
        assert elapsed_after >= elapsed_before


class TestHandleClaudeStream:
    """Test the handle_claude_stream function."""
    
    def test_handle_empty_line(self):
        """Test handling empty lines."""
        result = handle_claude_stream("")
        assert result is None
        
        result = handle_claude_stream("   ")
        assert result is None
    
    def test_handle_invalid_json(self):
        """Test handling invalid JSON."""
        result = handle_claude_stream("not json")
        assert result is None
        
        result = handle_claude_stream('{"incomplete": json')
        assert result is None
    
    def test_handle_system_event(self):
        """Test handling system initialization event."""
        system_event = {
            "type": "system",
            "cwd": "/home/<USER>",
            "model": "claude-3-5-sonnet",
            "permissionMode": "ask"
        }
        
        result = handle_claude_stream(json.dumps(system_event))
        
        assert result is not None
        assert result.type == "system"
        assert result.content["cwd"] == "/home/<USER>"
        assert result.content["model"] == "claude-3-5-sonnet"
        assert result.content["permissionMode"] == "ask"
        assert result.raw_event == system_event
    
    def test_handle_claude_message(self):
        """Test handling Claude text message."""
        assistant_event = {
            "type": "assistant",
            "message": {
                "content": [
                    {
                        "type": "text",
                        "text": "Hello, I can help you with that!"
                    }
                ]
            }
        }
        
        result = handle_claude_stream(json.dumps(assistant_event))
        
        assert result is not None
        assert result.type == "claude_message"
        assert result.content == "Hello, I can help you with that!"
        assert result.raw_event == assistant_event
    
    def test_handle_tool_call(self):
        """Test handling tool use event."""
        tool_event = {
            "type": "assistant",
            "message": {
                "content": [
                    {
                        "type": "tool_use",
                        "name": "Write",
                        "input": {
                            "file_path": "/tmp/test.py",
                            "content": "print('hello')"
                        }
                    }
                ]
            }
        }
        
        result = handle_claude_stream(json.dumps(tool_event))
        
        assert result is not None
        assert result.type == "tool_call"
        assert result.content["name"] == "Write"
        assert result.content["input"]["file_path"] == "/tmp/test.py"
        assert result.raw_event == tool_event
    
    def test_handle_tool_result_success(self):
        """Test handling successful tool result."""
        tool_result_event = {
            "type": "user",
            "message": {
                "content": [
                    {
                        "type": "tool_result",
                        "tool_use_id": "123",
                        "is_error": False,
                        "content": "File written successfully"
                    }
                ]
            }
        }
        
        result = handle_claude_stream(json.dumps(tool_result_event))
        
        assert result is not None
        assert result.type == "tool_result"
        assert result.content["tool_use_id"] == "123"
        assert result.content["is_error"] is False
        assert result.content["content"] == "File written successfully"
    
    def test_handle_tool_result_error(self):
        """Test handling error tool result."""
        tool_result_event = {
            "type": "user",
            "message": {
                "content": [
                    {
                        "type": "tool_result",
                        "tool_use_id": "123",
                        "is_error": True,
                        "content": "Permission denied"
                    }
                ]
            }
        }
        
        result = handle_claude_stream(json.dumps(tool_result_event))
        
        assert result is not None
        assert result.type == "tool_result"
        assert result.content["is_error"] is True
        assert result.content["content"] == "Permission denied"
    
    def test_handle_final_result_success(self):
        """Test handling successful final result."""
        result_event = {
            "type": "result",
            "is_error": False,
            "result": "Task completed successfully",
            "duration_ms": 5000,
            "total_cost_usd": 0.0042
        }
        
        result = handle_claude_stream(json.dumps(result_event))
        
        assert result is not None
        assert result.type == "result"
        assert result.content["is_error"] is False
        assert result.content["result"] == "Task completed successfully"
        assert result.content["duration_ms"] == 5000
        assert result.content["total_cost_usd"] == 0.0042
    
    def test_handle_final_result_error(self):
        """Test handling error final result."""
        result_event = {
            "type": "result",
            "is_error": True,
            "result": "Task failed with error",
            "duration_ms": 2500,
            "total_cost_usd": 0.0021
        }
        
        result = handle_claude_stream(json.dumps(result_event))
        
        assert result is not None
        assert result.type == "result"
        assert result.content["is_error"] is True
        assert result.content["result"] == "Task failed with error"
    
    def test_handle_error_event(self):
        """Test handling error event."""
        error_event = {
            "type": "error",
            "error": "Connection timeout"
        }
        
        result = handle_claude_stream(json.dumps(error_event))
        
        assert result is not None
        assert result.type == "error"
        assert result.content == "Connection timeout"
    
    def test_handle_with_session_tracking(self):
        """Test that outputs are properly added to session."""
        session = ClaudeSession(session_id="test", prompt="test")
        
        system_event = {"type": "system", "cwd": "/home", "model": "claude"}
        handle_claude_stream(json.dumps(system_event), session)
        
        assert len(session.outputs) == 1
        assert session.outputs[0].type == "system"
    
    def test_session_finalization_on_result(self):
        """Test that session is finalized on result event."""
        session = ClaudeSession(session_id="test", prompt="test")
        
        # Success result should finalize session as success
        success_result = {
            "type": "result",
            "is_error": False,
            "result": "Success",
            "duration_ms": 1000,
            "total_cost_usd": 0.001
        }
        
        handle_claude_stream(json.dumps(success_result), session)
        
        assert session.success is True
        assert session.error is None
        assert session.duration_ms == 1000
        assert session.total_cost_usd == 0.001
        assert session.end_time is not None
    
    def test_session_finalization_on_error(self):
        """Test that session is finalized on error event."""
        session = ClaudeSession(session_id="test", prompt="test")
        
        error_event = {
            "type": "error",
            "error": "Something went wrong"
        }
        
        handle_claude_stream(json.dumps(error_event), session)
        
        assert session.success is False
        assert session.error == "Something went wrong"
        assert session.end_time is not None