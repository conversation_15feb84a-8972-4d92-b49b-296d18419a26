import pytest
import tempfile
import os
import sys
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from agents.tools.local_tools import edit_file

# Mark all tests in this module as async using anyio
pytestmark = pytest.mark.anyio


class TestEditFile:
    """Test suite for the edit_file tool using TDD approach."""
    
    @pytest.fixture
    def temp_file(self):
        """Create a temporary file for testing."""
        with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.py') as f:
            test_content = '''def hello_world():
    print("Hello, World!")
    return "greeting"

def add_numbers(a, b):
    # Simple addition function
    result = a + b
    return result

class Calculator:
    def __init__(self):
        self.history = []
    
    def calculate(self, operation, a, b):
        if operation == "add":
            return a + b
        elif operation == "subtract":
            return a - b
        else:
            raise ValueError("Unknown operation")
'''
            f.write(test_content)
            temp_path = f.name
        
        yield temp_path
        
        # Cleanup
        if os.path.exists(temp_path):
            os.unlink(temp_path)
    
    @pytest.fixture
    def empty_file(self):
        """Create an empty temporary file for testing."""
        with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.txt') as f:
            temp_path = f.name
        
        yield temp_path
        
        # Cleanup
        if os.path.exists(temp_path):
            os.unlink(temp_path)

    async def test_edit_file_basic_string_replace(self, temp_file):
        """Test basic string replacement functionality."""
        old_str = 'print("Hello, World!")'
        new_str = 'print("Hello, Universe!")'
        
        result = await edit_file.ainvoke({
            "file_path": temp_file,
            "old_str": old_str,
            "new_str": new_str
        })
        
        # Should succeed
        assert "✅" in result
        assert "successfully replaced" in result.lower()
        
        # Verify the change was made
        with open(temp_file, 'r') as f:
            content = f.read()
            assert 'print("Hello, Universe!")' in content
            assert 'print("Hello, World!")' not in content

    async def test_edit_file_multiline_replace(self, temp_file):
        """Test replacing multiple lines of code."""
        old_str = '''def add_numbers(a, b):
    # Simple addition function
    result = a + b
    return result'''
        
        new_str = '''def add_numbers(a, b):
    """Add two numbers together."""
    if not isinstance(a, (int, float)) or not isinstance(b, (int, float)):
        raise TypeError("Arguments must be numbers")
    result = a + b
    return result'''
        
        result = await edit_file.ainvoke({
            "file_path": temp_file,
            "old_str": old_str,
            "new_str": new_str
        })
        
        assert "✅" in result
        
        # Verify the change
        with open(temp_file, 'r') as f:
            content = f.read()
            assert '"""Add two numbers together."""' in content
            assert "raise TypeError" in content

    async def test_edit_file_nonexistent_file(self):
        """Test error handling for non-existent file."""
        result = await edit_file.ainvoke({
            "file_path": "/nonexistent/path/file.py",
            "old_str": "some text",
            "new_str": "new text"
        })
        
        assert "❌" in result
        assert "does not exist" in result.lower()

    async def test_edit_file_string_not_found(self, temp_file):
        """Test error when old_str is not found in file."""
        result = await edit_file.ainvoke({
            "file_path": temp_file,
            "old_str": "this string does not exist in the file",
            "new_str": "replacement text"
        })
        
        assert "❌" in result
        assert "not found" in result.lower()

    async def test_edit_file_multiple_occurrences(self, temp_file):
        """Test error when old_str matches multiple locations."""
        # Add duplicate content to create multiple matches
        with open(temp_file, 'a') as f:
            f.write('\n\ndef hello_world():\n    print("Hello, World!")\n    return "greeting"')
        
        result = await edit_file.ainvoke({
            "file_path": temp_file,
            "old_str": 'print("Hello, World!")',
            "new_str": 'print("Hello, Universe!")'
        })
        
        assert "❌" in result
        assert "multiple occurrences" in result.lower() or "not unique" in result.lower()

    async def test_edit_file_same_old_and_new_str(self, temp_file):
        """Test error when old_str and new_str are identical."""
        text = 'print("Hello, World!")'
        
        result = await edit_file.ainvoke({
            "file_path": temp_file,
            "old_str": text,
            "new_str": text
        })
        
        assert "❌" in result
        assert "must be different" in result.lower() or "identical" in result.lower()

    async def test_edit_file_with_whitespace_preservation(self, temp_file):
        """Test that whitespace and indentation are preserved correctly."""
        old_str = '''    def calculate(self, operation, a, b):
        if operation == "add":
            return a + b
        elif operation == "subtract":
            return a - b
        else:
            raise ValueError("Unknown operation")'''
        
        new_str = '''    def calculate(self, operation, a, b):
        """Perform basic arithmetic operations."""
        if operation == "add":
            return a + b
        elif operation == "subtract":
            return a - b
        elif operation == "multiply":
            return a * b
        else:
            raise ValueError("Unknown operation")'''
        
        result = await edit_file.ainvoke({
            "file_path": temp_file,
            "old_str": old_str,
            "new_str": new_str
        })
        
        assert "✅" in result
        
        # Verify indentation is preserved
        with open(temp_file, 'r') as f:
            content = f.read()
            assert '"Perform basic arithmetic operations."' in content
            assert 'elif operation == "multiply":' in content

    async def test_edit_file_empty_file(self, empty_file):
        """Test editing an empty file."""
        result = await edit_file.ainvoke({
            "file_path": empty_file,
            "old_str": "nonexistent",
            "new_str": "something"
        })
        
        assert "❌" in result
        assert "not found" in result.lower()

    async def test_edit_file_with_special_characters(self, temp_file):
        """Test editing content with special characters and regex metacharacters."""
        # Add content with special characters
        with open(temp_file, 'w') as f:
            f.write('pattern = r"\\d+\\.\\d+"  # Regex pattern\nresult = re.search(pattern, text)')
        
        old_str = 'pattern = r"\\d+\\.\\d+"  # Regex pattern'
        new_str = 'pattern = r"\\d+\\.\\d{2}"  # Updated regex pattern'
        
        result = await edit_file.ainvoke({
            "file_path": temp_file,
            "old_str": old_str,
            "new_str": new_str
        })
        
        assert "✅" in result
        
        with open(temp_file, 'r') as f:
            content = f.read()
            assert 'r"\\d+\\.\\d{2}"' in content

    async def test_edit_file_preserve_line_endings(self, temp_file):
        """Test that different line endings are preserved."""
        # This test ensures the tool works with different line ending styles
        old_str = 'def hello_world():'
        new_str = 'def hello_universe():'
        
        result = await edit_file.ainvoke({
            "file_path": temp_file,
            "old_str": old_str,
            "new_str": new_str
        })
        
        assert "✅" in result
        
        with open(temp_file, 'r') as f:
            content = f.read()
            assert 'def hello_universe():' in content

    async def test_edit_file_large_replacement(self, temp_file):
        """Test replacing with significantly larger content."""
        old_str = 'return "greeting"'
        new_str = '''# This is a much longer replacement
        # with multiple lines and comments
        logging.info("Returning greeting message")
        return "greeting"  # Original return statement'''
        
        result = await edit_file.ainvoke({
            "file_path": temp_file,
            "old_str": old_str,
            "new_str": new_str
        })
        
        assert "✅" in result
        
        with open(temp_file, 'r') as f:
            content = f.read()
            assert 'logging.info("Returning greeting message")' in content

    async def test_edit_file_directory_path(self):
        """Test error when file_path points to a directory."""
        with tempfile.TemporaryDirectory() as temp_dir:
            result = await edit_file.ainvoke({
                "file_path": temp_dir,
                "old_str": "text",
                "new_str": "replacement"
            })
            
            assert "❌" in result
            assert "directory" in result.lower()

    async def test_edit_file_permission_error(self, temp_file):
        """Test handling of permission errors."""
        # Make file read-only
        os.chmod(temp_file, 0o444)
        
        try:
            result = await edit_file.ainvoke({
                "file_path": temp_file,
                "old_str": 'print("Hello, World!")',
                "new_str": 'print("Hello, Universe!")'
            })
            
            assert "❌" in result
            assert "permission" in result.lower() or "error" in result.lower()
        finally:
            # Restore permissions for cleanup
            os.chmod(temp_file, 0o644)


    async def test_edit_file_encoding_handling(self, temp_file):
        """Test handling of different file encodings."""
        # Write content with unicode characters
        with open(temp_file, 'w', encoding='utf-8') as f:
            f.write('message = "Hello, 世界!"  # Unicode content\nprint(message)')
        
        old_str = 'message = "Hello, 世界!"'
        new_str = 'message = "Hello, 宇宙!"'
        
        result = await edit_file.ainvoke({
            "file_path": temp_file,
            "old_str": old_str,
            "new_str": new_str
        })
        
        assert "✅" in result
        
        with open(temp_file, 'r', encoding='utf-8') as f:
            content = f.read()
            assert '"Hello, 宇宙!"' in content

    async def test_edit_file_includes_diff(self, temp_file):
        """Test that edit_file returns a diff showing the changes made."""
        old_str = 'print("Hello, World!")'
        new_str = 'print("Hello, Universe!")'
        
        result = await edit_file.ainvoke({
            "file_path": temp_file,
            "old_str": old_str,
            "new_str": new_str
        })
        
        # Should succeed
        assert "✅" in result
        assert "successfully replaced" in result.lower()
        
        # Should include diff
        assert "📝 **Diff:**" in result
        assert "```diff" in result
        assert "```" in result
        
        # Diff should show the change
        assert "-" in result  # removed line
        assert "+" in result  # added line
        assert "Hello, World!" in result
        assert "Hello, Universe!" in result
        
        # Verify the change was made
        with open(temp_file, 'r') as f:
            content = f.read()
            assert 'print("Hello, Universe!")' in content
            assert 'print("Hello, World!")' not in content

    async def test_edit_file_multiline_diff(self, temp_file):
        """Test that multiline edits produce proper diffs."""
        # Write multiline content to temp file
        multiline_content = '''def greet():
    print("Hello")
    print("World")
    return "done"
'''
        with open(temp_file, 'w') as f:
            f.write(multiline_content)
        
        old_str = '''def greet():
    print("Hello")
    print("World")'''
        new_str = '''def greet():
    """Greet the world."""
    print("Hello")
    print("Universe")'''
        
        result = await edit_file.ainvoke({
            "file_path": temp_file,
            "old_str": old_str,
            "new_str": new_str
        })
        
        # Should succeed
        assert "✅" in result
        assert "📝 **Diff:**" in result
        
        # Should show proper diff format
        assert "@@" in result  # diff hunk header
        assert "+    \"\"\"Greet the world.\"\"\"" in result
        assert "-    print(\"World\")" in result
        assert "+    print(\"Universe\")" in result

    async def test_edit_file_no_diff_on_error(self):
        """Test that no diff is included when edit fails."""
        result = await edit_file.ainvoke({
            "file_path": "nonexistent_file.py",
            "old_str": "old",
            "new_str": "new"
        })
        
        # Should fail
        assert "❌" in result
        assert "📝 **Diff:**" not in result
        assert "```diff" not in result