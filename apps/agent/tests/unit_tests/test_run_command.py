import pytest
import os
import sys
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from agents.tools.local_tools import run_command

# Mark all tests in this module as async using anyio
pytestmark = pytest.mark.anyio


class TestRunCommand:
    """Test suite for the run_command tool with safety features."""
    
    # Test allowed commands
    async def test_allowed_basic_commands(self):
        """Test basic allowed commands work correctly."""
        # Test ls command
        result = await run_command.ainvoke({"command": "ls -la"})
        assert "🚫" not in result
        assert "STDOUT" in result or "Command executed successfully" in result
        
        # Test pwd command
        result = await run_command.ainvoke({"command": "pwd"})
        assert "🚫" not in result
        assert "/" in result  # Should contain a path
        
        # Test echo command
        result = await run_command.ainvoke({"command": "echo 'Hello, World!'"})
        assert "🚫" not in result
        assert "Hello, World!" in result
    
    async def test_git_commands_allowed(self):
        """Test that git commands are allowed."""
        result = await run_command.ainvoke({"command": "git status"})
        assert "🚫" not in result
        # Either we get git output or an error about not being a git repo
        assert ("branch" in result.lower()) or ("not a git repository" in result.lower()) or ("fatal" in result.lower())
    
    async def test_python_commands_allowed(self):
        """Test that Python commands are allowed."""
        result = await run_command.ainvoke({"command": "python --version"})
        assert "🚫" not in result
        assert "Python" in result or "python" in result.lower()
    
    # Test completely blocked commands
    async def test_blocked_sudo_commands(self):
        """Test that sudo commands are blocked."""
        result = await run_command.ainvoke({"command": "sudo ls"})
        assert "🚫 Command blocked for safety" in result
        assert "sudo" in result.lower()
    
    async def test_blocked_system_commands(self):
        """Test that system modification commands are blocked."""
        blocked_commands = [
            "passwd",
            "useradd testuser",
            "userdel testuser",
            "shutdown now",
            "reboot",
            "halt",
            "poweroff"
        ]
        
        for cmd in blocked_commands:
            result = await run_command.ainvoke({"command": cmd})
            assert "🚫 Command blocked for safety" in result
            assert "Blocked dangerous command" in result
    
    async def test_blocked_network_commands(self):
        """Test that network commands are blocked."""
        blocked_commands = [
            "wget https://example.com",
            "curl https://example.com",
            "ssh user@host",
            "scp file.txt user@host:",
            "ftp example.com",
            "telnet localhost",
            "nc -l 8080",
            "netcat localhost 8080"
        ]
        
        for cmd in blocked_commands:
            result = await run_command.ainvoke({"command": cmd})
            assert "🚫 Command blocked for safety" in result
    
    async def test_package_managers_not_blocked_for_safety(self):
        """Test that package managers are not blocked by safety checks."""
        
        # Test that these commands pass safety validation (but don't actually run them)
        test_commands = [
            "pip --version",  # Safe version check commands that won't modify anything
            "npm --version",
            "brew --version"
        ]
        
        for cmd in test_commands:
            result = await run_command.ainvoke({"command": cmd})
            assert "🚫" not in result
            # These version commands should execute safely without modifying packages
    
    # Test scoped commands
    async def test_scoped_rm_allowed_in_current_dir(self):
        """Test that rm is allowed for files in current directory."""
        # Create a temporary file
        temp_file = "test_temp_file.txt"
        with open(temp_file, 'w') as f:
            f.write("test")
        
        try:
            # Should be allowed to remove file in current directory
            result = await run_command.ainvoke({"command": f"rm {temp_file}"})
            assert "🚫" not in result
            assert not os.path.exists(temp_file)  # File should be deleted
        finally:
            # Clean up if test failed
            if os.path.exists(temp_file):
                os.remove(temp_file)
    
    async def test_scoped_rm_blocked_outside_current_dir(self):
        """Test that rm is blocked for paths outside current directory."""
        dangerous_rm_commands = [
            "rm /etc/passwd",
            "rm ../important_file.txt",
            "rm ~/Documents/file.txt",
            "rm /tmp/file.txt",
            "rm /usr/bin/ls"
        ]
        
        for cmd in dangerous_rm_commands:
            result = await run_command.ainvoke({"command": cmd})
            assert "🚫 Command blocked for safety" in result
            assert "Path outside current directory not allowed" in result
    
    async def test_scoped_commands_with_subdirectories(self):
        """Test that scoped commands work with subdirectories."""
        # Create a temporary subdirectory and file
        os.makedirs("test_subdir", exist_ok=True)
        test_file = "test_subdir/test_file.txt"
        
        try:
            with open(test_file, 'w') as f:
                f.write("test")
            
            # Should be allowed to remove file in subdirectory
            result = await run_command.ainvoke({"command": f"rm {test_file}"})
            assert "🚫" not in result
            assert not os.path.exists(test_file)
        finally:
            # Clean up
            import shutil
            if os.path.exists("test_subdir"):
                shutil.rmtree("test_subdir")
    
    async def test_chmod_scoped_correctly(self):
        """Test that chmod is only allowed for current directory files."""
        # Test blocked chmod
        result = await run_command.ainvoke({"command": "chmod 755 /etc/passwd"})
        assert "🚫 Command blocked for safety" in result
        
        # Test allowed chmod (create temp file first)
        temp_file = "test_chmod_file.txt"
        try:
            with open(temp_file, 'w') as f:
                f.write("test")
            
            result = await run_command.ainvoke({"command": f"chmod 644 {temp_file}"})
            assert "🚫" not in result
        finally:
            if os.path.exists(temp_file):
                os.remove(temp_file)
    
    async def test_mv_cp_scoped_correctly(self):
        """Test that mv and cp are scoped correctly."""
        # Test blocked mv/cp
        result = await run_command.ainvoke({"command": "mv file.txt /tmp/"})
        assert "🚫 Command blocked for safety" in result
        
        result = await run_command.ainvoke({"command": "cp file.txt /etc/"})
        assert "🚫 Command blocked for safety" in result
        
        # Test allowed mv/cp within current directory
        temp_file = "test_mv_file.txt"
        try:
            with open(temp_file, 'w') as f:
                f.write("test")
            
            # Move within current directory should be allowed
            result = await run_command.ainvoke({"command": f"mv {temp_file} {temp_file}.bak"})
            assert "🚫" not in result
            
            # Clean up
            if os.path.exists(f"{temp_file}.bak"):
                os.remove(f"{temp_file}.bak")
        finally:
            if os.path.exists(temp_file):
                os.remove(temp_file)
    
    # Test dangerous patterns
    async def test_dangerous_patterns_blocked(self):
        """Test that dangerous patterns are blocked."""
        dangerous_commands = [
            "echo 'test' | sh",
            "echo 'test' | bash",
            "echo test > /dev/null",
            "python -c 'import os; os.system(\"ls\")'",
            "eval 'ls -la'",
            "exec ls"
        ]
        
        for cmd in dangerous_commands:
            result = await run_command.ainvoke({"command": cmd})
            assert "🚫 Command blocked for safety" in result
            assert "Blocked dangerous pattern" in result
    
    async def test_piping_allowed_to_safe_commands(self):
        """Test that piping to safe commands is allowed."""
        result = await run_command.ainvoke({"command": "ls | grep py"})
        assert "🚫" not in result
        
        result = await run_command.ainvoke({"command": "echo 'test' | cat"})
        assert "🚫" not in result
        assert "test" in result
    
    # Test edge cases
    async def test_empty_command(self):
        """Test handling of empty command."""
        result = await run_command.ainvoke({"command": ""})
        # Should execute but might return empty output
        assert "🚫" not in result
    
    async def test_command_with_quotes(self):
        """Test commands with various quote styles."""
        result = await run_command.ainvoke({"command": 'echo "Hello, World!"'})
        assert "🚫" not in result
        assert "Hello, World!" in result
        
        result = await run_command.ainvoke({"command": "echo 'Single quotes'"})
        assert "🚫" not in result
        assert "Single quotes" in result
    
    async def test_complex_safe_command(self):
        """Test complex but safe command."""
        result = await run_command.ainvoke({
            "command": "find . -name '*.py' -type f | head -5"
        })
        assert "🚫" not in result
        # Should list some Python files or be empty
    
    async def test_redirect_to_local_file_allowed(self):
        """Test that redirecting to local files is allowed."""
        temp_file = "test_redirect.txt"
        try:
            result = await run_command.ainvoke({
                "command": f"echo 'test content' > {temp_file}"
            })
            assert "🚫" not in result
            
            # Verify file was created
            assert os.path.exists(temp_file)
            with open(temp_file, 'r') as f:
                assert f.read().strip() == "test content"
        finally:
            if os.path.exists(temp_file):
                os.remove(temp_file)
    
    async def test_append_to_local_file_allowed(self):
        """Test that appending to local files is allowed."""
        temp_file = "test_append.txt"
        try:
            # Create initial file
            with open(temp_file, 'w') as f:
                f.write("line1\n")
            
            result = await run_command.ainvoke({
                "command": f"echo 'line2' >> {temp_file}"
            })
            assert "🚫" not in result
            
            # Verify append worked
            with open(temp_file, 'r') as f:
                content = f.read()
                assert "line1" in content
                assert "line2" in content
        finally:
            if os.path.exists(temp_file):
                os.remove(temp_file)
    
    async def test_tar_zip_scoped_correctly(self):
        """Test that tar/zip commands are scoped correctly."""
        # Create test files
        os.makedirs("test_archive_dir", exist_ok=True)
        with open("test_archive_dir/file.txt", 'w') as f:
            f.write("test")
        
        try:
            # Allowed: tar within current directory
            result = await run_command.ainvoke({
                "command": "tar -czf test_archive.tar.gz test_archive_dir"
            })
            assert "🚫" not in result
            
            # Blocked: tar to system directory
            result = await run_command.ainvoke({
                "command": "tar -czf /tmp/archive.tar.gz test_archive_dir"
            })
            assert "🚫 Command blocked for safety" in result
            
            # Clean up
            if os.path.exists("test_archive.tar.gz"):
                os.remove("test_archive.tar.gz")
        finally:
            import shutil
            if os.path.exists("test_archive_dir"):
                shutil.rmtree("test_archive_dir")
    
    async def test_command_timeout(self):
        """Test that commands timeout after 30 seconds."""
        # This command should timeout
        result = await run_command.ainvoke({"command": "sleep 35"})
        assert "timed out" in result.lower()
    
    async def test_special_characters_in_paths(self):
        """Test handling of special characters in paths."""
        # Create file with spaces
        temp_file = "test file with spaces.txt"
        try:
            with open(temp_file, 'w') as f:
                f.write("test")
            
            # Should handle spaces correctly
            result = await run_command.ainvoke({
                "command": f'rm "{temp_file}"'
            })
            assert "🚫" not in result
            assert not os.path.exists(temp_file)
        finally:
            if os.path.exists(temp_file):
                os.remove(temp_file)
    
    async def test_environment_variables_blocked(self):
        """Test that commands trying to read environment variables are blocked."""
        result = await run_command.ainvoke({"command": "env"})
        assert "🚫 Command blocked for safety" in result
        
        result = await run_command.ainvoke({"command": "printenv"})
        assert "🚫 Command blocked for safety" in result
        
        result = await run_command.ainvoke({"command": "history"})
        assert "🚫 Command blocked for safety" in result