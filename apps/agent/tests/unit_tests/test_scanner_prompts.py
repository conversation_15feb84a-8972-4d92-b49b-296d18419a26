import pytest
from agents.scanner.prompts import (
    BASE_SYSTEM_MESSAGE,
    METRIC_TEMPLATE,
    METRIC_CONFIGS,
    get_metric_prompt,
    METRIC_PROMPTS,
    TEST_COVERAGE_PROMPT,
    <PERSON><PERSON>RITY_PROMPT,
    <PERSON><PERSON><PERSON><PERSON><PERSON>TY_PROMPT,
    SECURITY_PROMPT,
    PER<PERSON><PERSON><PERSON>NCE_PROMPT,
    FAULT_RESILIENCE_PROMPT,
    AGENT_READINESS_PROMPT,
)


class TestScannerPrompts:
    """Test scanner prompts formatting and content."""

    def test_base_system_message_content(self):
        """Test that base system message contains required content."""
        assert "Backspace's Deep Scan Agent" in BASE_SYSTEM_MESSAGE
        assert "github_create_issue" in BASE_SYSTEM_MESSAGE
        assert "github_list_issues" in BASE_SYSTEM_MESSAGE
        assert "github_view_issue" in BASE_SYSTEM_MESSAGE
        assert "github_pr_create" in BASE_SYSTEM_MESSAGE

    def test_all_metrics_have_configs(self):
        """Test that all 7 metrics have configurations."""
        expected_metrics = [
            "test_coverage",
            "clarity",
            "modularity",
            "security",
            "performance",
            "fault_resilience",
            "agent_readiness"
        ]
        assert set(METRIC_CONFIGS.keys()) == set(expected_metrics)

    def test_metric_config_structure(self):
        """Test that each metric config has required fields."""
        required_fields = [
            "metric_name",
            "core_question",
            "analysis_focus",
            "workflow_steps",
            "scoring_guidelines"
        ]
        
        for metric_key, config in METRIC_CONFIGS.items():
            for field in required_fields:
                assert field in config, f"Missing {field} in {metric_key} config"
            
            # Check types
            assert isinstance(config["metric_name"], str)
            assert isinstance(config["core_question"], str)
            assert isinstance(config["analysis_focus"], list)
            assert isinstance(config["workflow_steps"], list)
            assert isinstance(config["scoring_guidelines"], dict)
            
            # Check list lengths
            assert len(config["analysis_focus"]) >= 3
            assert len(config["workflow_steps"]) >= 4
            assert len(config["scoring_guidelines"]) == 5

    def test_get_metric_prompt_renders_correctly(self):
        """Test that get_metric_prompt renders templates correctly."""
        # Test a specific metric
        prompt = get_metric_prompt("test_coverage")
        
        # Check base message is included
        assert "Backspace's Deep Scan Agent" in prompt
        
        # Check metric-specific content
        assert "TEST COVERAGE METRIC" in prompt
        assert "How protected is the codebase against regressions?" in prompt
        assert "Run test suites and analyze coverage reports" in prompt
        assert "pytest --cov" in prompt
        
        # Check formatting
        assert "## 🎯 TEST COVERAGE METRIC (0-100 score)" in prompt
        assert "**Core Question**:" in prompt
        assert "**Analysis Focus**:" in prompt
        assert "## 🔧 ANALYSIS WORKFLOW:" in prompt
        assert "## 🎯 SCORING GUIDELINES:" in prompt

    def test_get_metric_prompt_invalid_metric(self):
        """Test that get_metric_prompt raises error for invalid metric."""
        with pytest.raises(ValueError, match="Unknown metric: invalid_metric"):
            get_metric_prompt("invalid_metric")

    def test_all_metric_prompts_generated(self):
        """Test that METRIC_PROMPTS contains all metrics."""
        assert len(METRIC_PROMPTS) == 7
        assert all(key in METRIC_PROMPTS for key in METRIC_CONFIGS.keys())

    def test_backward_compatibility_variables(self):
        """Test that individual prompt variables exist for backward compatibility."""
        # Check that all individual variables exist
        assert TEST_COVERAGE_PROMPT is not None
        assert CLARITY_PROMPT is not None
        assert MODULARITY_PROMPT is not None
        assert SECURITY_PROMPT is not None
        assert PERFORMANCE_PROMPT is not None
        assert FAULT_RESILIENCE_PROMPT is not None
        assert AGENT_READINESS_PROMPT is not None
        
        # Check they match the dictionary versions
        assert TEST_COVERAGE_PROMPT == METRIC_PROMPTS["test_coverage"]
        assert CLARITY_PROMPT == METRIC_PROMPTS["clarity"]
        assert MODULARITY_PROMPT == METRIC_PROMPTS["modularity"]
        assert SECURITY_PROMPT == METRIC_PROMPTS["security"]
        assert PERFORMANCE_PROMPT == METRIC_PROMPTS["performance"]
        assert FAULT_RESILIENCE_PROMPT == METRIC_PROMPTS["fault_resilience"]
        assert AGENT_READINESS_PROMPT == METRIC_PROMPTS["agent_readiness"]

    @pytest.mark.parametrize("metric_key", list(METRIC_CONFIGS.keys()))
    def test_each_metric_prompt_formatting(self, metric_key):
        """Test formatting for each metric prompt."""
        prompt = get_metric_prompt(metric_key)
        config = METRIC_CONFIGS[metric_key]
        
        # Check base content
        assert "Backspace's Deep Scan Agent" in prompt
        
        # Check metric name formatting
        metric_name_upper = config["metric_name"].upper()
        assert f"## 🎯 {metric_name_upper} METRIC (0-100 score)" in prompt
        
        # Check core question
        assert config["core_question"] in prompt
        
        # Check all analysis focus items are included
        for focus_item in config["analysis_focus"]:
            assert focus_item in prompt
        
        # Check all workflow steps are included
        for i, step in enumerate(config["workflow_steps"], 1):
            assert f"{i}. {step}" in prompt
        
        # Check all scoring guidelines are included
        for score_range, description in config["scoring_guidelines"].items():
            assert f"**{score_range}**: {description}" in prompt
        
        # Check footer
        assert f"Focus exclusively on {config['metric_name'].lower()} analysis" in prompt

    def test_template_rendering_edge_cases(self):
        """Test template rendering with edge cases."""
        # Test metric with underscores in name
        prompt = get_metric_prompt("test_coverage")
        assert "TEST COVERAGE" in prompt  # Underscores should be replaced with spaces
        
        prompt = get_metric_prompt("fault_resilience")
        assert "FAULT RESILIENCE" in prompt

    def test_scoring_guidelines_order(self):
        """Test that scoring guidelines maintain consistent order."""
        expected_order = ["90-100", "80-89", "70-79", "60-69", "0-59"]
        
        for metric_key, config in METRIC_CONFIGS.items():
            actual_order = list(config["scoring_guidelines"].keys())
            assert actual_order == expected_order, f"Scoring order mismatch in {metric_key}"

    def test_workflow_steps_numbering(self):
        """Test that workflow steps are numbered correctly in output."""
        for metric_key in METRIC_CONFIGS.keys():
            prompt = get_metric_prompt(metric_key)
            config = METRIC_CONFIGS[metric_key]
            
            # Check that steps are numbered 1 through N
            for i in range(1, len(config["workflow_steps"]) + 1):
                assert f"{i}. " in prompt

    def test_github_issue_creation_mentioned(self):
        """Test that GitHub issue creation is mentioned in appropriate steps."""
        for metric_key in METRIC_CONFIGS.keys():
            config = METRIC_CONFIGS[metric_key]
            # Check that at least one workflow step mentions GitHub issues
            github_step_found = any(
                "GitHub issue" in step 
                for step in config["workflow_steps"]
            )
            assert github_step_found, f"No GitHub issue creation step in {metric_key}"