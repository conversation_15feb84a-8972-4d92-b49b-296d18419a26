"""Tests for ripgrep search functionality."""

import subprocess
import tempfile
import os
import pytest
from pathlib import Path


class TestRipgrepSearch:
    """Test suite for ripgrep search functionality."""

    @pytest.fixture
    def temp_project(self):
        """Create a temporary project structure for testing."""
        with tempfile.TemporaryDirectory() as tmpdir:
            # Create test files
            test_files = {
                "main.py": """
def main():
    print("Hello World")
    return ResearchState(status="ready")

class ResearchState:
    def __init__(self, status):
        self.status = status
""",
                "utils.py": """
import os
from typing import Optional

def search_files(pattern: str) -> Optional[str]:
    # This is a utility function
    return pattern.upper()

RESEARCH_STATE = "active"
""",
                "config.py": """
# Configuration file
DEBUG = True
RESEARCH_STATE = "initialized"

def configure_research():
    pass
""",
                "nested/deep.py": """
# Deeply nested file
from main import ResearchState

state = ResearchState("nested")
"""
            }
            
            # Write test files
            for file_path, content in test_files.items():
                full_path = Path(tmpdir) / file_path
                full_path.parent.mkdir(parents=True, exist_ok=True)
                full_path.write_text(content)
            
            yield tmpdir

    def test_basic_pattern_search(self, temp_project):
        """Test basic pattern search."""
        result = subprocess.run(
            ['rg', 'ResearchState', temp_project],
            capture_output=True,
            text=True
        )
        
        assert result.returncode == 0
        assert 'main.py' in result.stdout
        assert 'class ResearchState:' in result.stdout
        assert 'nested/deep.py' in result.stdout

    def test_search_with_file_type(self, temp_project):
        """Test searching specific file types."""
        result = subprocess.run(
            ['rg', 'import', '--type', 'py', temp_project],
            capture_output=True,
            text=True
        )
        
        assert result.returncode == 0
        assert 'utils.py' in result.stdout
        assert 'import os' in result.stdout
        assert 'from main import ResearchState' in result.stdout

    def test_case_insensitive_search(self, temp_project):
        """Test case-insensitive search."""
        result = subprocess.run(
            ['rg', '-i', 'researchstate', temp_project],
            capture_output=True,
            text=True
        )
        
        assert result.returncode == 0
        # Should find ResearchState in multiple files
        assert 'main.py' in result.stdout
        assert 'nested/deep.py' in result.stdout

    def test_search_with_context(self, temp_project):
        """Test search with context lines."""
        result = subprocess.run(
            ['rg', 'def main', '-A', '2', '-B', '1', temp_project],
            capture_output=True,
            text=True
        )
        
        assert result.returncode == 0
        assert 'def main():' in result.stdout
        assert 'print("Hello World")' in result.stdout  # After context
        assert 'return ResearchState' in result.stdout  # After context

    def test_count_matches(self, temp_project):
        """Test counting matches per file."""
        result = subprocess.run(
            ['rg', 'def', '-c', temp_project],
            capture_output=True,
            text=True
        )
        
        assert result.returncode == 0
        # main.py has 2 defs, utils.py has 1, config.py has 1
        assert 'main.py:2' in result.stdout
        assert 'utils.py:1' in result.stdout
        assert 'config.py:1' in result.stdout

    def test_search_specific_directory(self, temp_project):
        """Test searching in specific directory."""
        nested_dir = os.path.join(temp_project, 'nested')
        result = subprocess.run(
            ['rg', 'ResearchState', nested_dir],
            capture_output=True,
            text=True
        )
        
        assert result.returncode == 0
        assert 'deep.py' in result.stdout
        assert 'main.py' not in result.stdout  # Not in nested directory

    def test_regex_pattern(self, temp_project):
        """Test regex pattern search."""
        result = subprocess.run(
            ['rg', r'def \w+\(', temp_project],
            capture_output=True,
            text=True
        )
        
        assert result.returncode == 0
        assert 'def main(' in result.stdout
        assert 'def search_files(' in result.stdout
        assert 'def configure_research(' in result.stdout

    def test_exclude_pattern(self, temp_project):
        """Test excluding files from search."""
        result = subprocess.run(
            ['rg', 'import', '--glob', '!**/nested/*', temp_project],
            capture_output=True,
            text=True
        )
        
        assert result.returncode == 0
        assert 'utils.py' in result.stdout
        assert 'nested/deep.py' not in result.stdout

    def test_no_matches_found(self, temp_project):
        """Test behavior when no matches are found."""
        result = subprocess.run(
            ['rg', 'NonExistentPattern', temp_project],
            capture_output=True,
            text=True
        )
        
        assert result.returncode == 1  # rg returns 1 when no matches found
        assert result.stdout == ''

    def test_max_count_per_file(self, temp_project):
        """Test limiting matches per file."""
        result = subprocess.run(
            ['rg', 'def', '-m', '1', temp_project],
            capture_output=True,
            text=True
        )
        
        assert result.returncode == 0
        # Should only show first match in each file
        stdout_lines = result.stdout.strip().split('\n')
        # Count occurrences of each file
        file_counts = {}
        for line in stdout_lines:
            if ':' in line:
                file_name = line.split(':')[0]
                file_counts[file_name] = file_counts.get(file_name, 0) + 1
        
        # Each file should appear at most once
        for count in file_counts.values():
            assert count == 1


if __name__ == "__main__":
    # Run tests if executed directly
    pytest.main([__file__, "-v"])