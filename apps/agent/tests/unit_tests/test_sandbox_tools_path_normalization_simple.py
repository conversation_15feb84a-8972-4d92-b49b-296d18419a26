"""
Unit tests for sandbox tools path normalization.

Tests the path normalization logic directly without the @tool decorator complexity.
"""

import pytest
from agents.tools.sandbox_tools_state import sandbox_edit_file, sandbox_write_file


class TestPathNormalizationLogic:
    """Test the path normalization logic in sandbox tools."""

    def test_normalize_relative_path(self):
        """Test that relative paths get /workspace/ prepended."""
        # The normalization logic from sandbox_edit_file
        file_path = "src/main.py"
        
        # Normalize file path - handle various input formats
        if file_path.startswith("./"):
            file_path = file_path[2:]  # Remove ./
        
        # Only prepend /workspace/ if not already present and not an absolute path
        if not file_path.startswith("/workspace/") and not file_path.startswith("/"):
            file_path = f"/workspace/{file_path}"
        elif file_path.startswith("/") and not file_path.startswith("/workspace/"):
            # If it's an absolute path not in workspace, prepend workspace
            file_path = f"/workspace{file_path}"
            
        assert file_path == "/workspace/src/main.py"

    def test_normalize_dot_slash_path(self):
        """Test that ./ is stripped and /workspace/ is prepended."""
        file_path = "./src/main.py"
        
        # Normalize file path - handle various input formats
        if file_path.startswith("./"):
            file_path = file_path[2:]  # Remove ./
        
        # Only prepend /workspace/ if not already present and not an absolute path
        if not file_path.startswith("/workspace/") and not file_path.startswith("/"):
            file_path = f"/workspace/{file_path}"
        elif file_path.startswith("/") and not file_path.startswith("/workspace/"):
            # If it's an absolute path not in workspace, prepend workspace
            file_path = f"/workspace{file_path}"
            
        assert file_path == "/workspace/src/main.py"

    def test_workspace_path_not_duplicated(self):
        """Test that /workspace/ is not duplicated if already present."""
        file_path = "/workspace/src/main.py"
        
        # Normalize file path - handle various input formats
        if file_path.startswith("./"):
            file_path = file_path[2:]  # Remove ./
        
        # Only prepend /workspace/ if not already present and not an absolute path
        if not file_path.startswith("/workspace/") and not file_path.startswith("/"):
            file_path = f"/workspace/{file_path}"
        elif file_path.startswith("/") and not file_path.startswith("/workspace/"):
            # If it's an absolute path not in workspace, prepend workspace
            file_path = f"/workspace{file_path}"
            
        assert file_path == "/workspace/src/main.py"

    def test_absolute_path_outside_workspace(self):
        """Test that absolute paths outside workspace get /workspace prepended."""
        file_path = "/src/main.py"
        
        # Normalize file path - handle various input formats
        if file_path.startswith("./"):
            file_path = file_path[2:]  # Remove ./
        
        # Only prepend /workspace/ if not already present and not an absolute path
        if not file_path.startswith("/workspace/") and not file_path.startswith("/"):
            file_path = f"/workspace/{file_path}"
        elif file_path.startswith("/") and not file_path.startswith("/workspace/"):
            # If it's an absolute path not in workspace, prepend workspace
            file_path = f"/workspace{file_path}"
            
        assert file_path == "/workspace/src/main.py"

    def test_dot_slash_with_workspace(self):
        """Test edge case: ./workspace/file.py should become /workspace/workspace/file.py."""
        file_path = "./workspace/file.py"
        
        # Normalize file path - handle various input formats
        if file_path.startswith("./"):
            file_path = file_path[2:]  # Remove ./
        
        # Only prepend /workspace/ if not already present and not an absolute path
        if not file_path.startswith("/workspace/") and not file_path.startswith("/"):
            file_path = f"/workspace/{file_path}"
        elif file_path.startswith("/") and not file_path.startswith("/workspace/"):
            # If it's an absolute path not in workspace, prepend workspace
            file_path = f"/workspace{file_path}"
            
        assert file_path == "/workspace/workspace/file.py"

    def test_empty_path_stays_relative(self):
        """Test that empty path handling."""
        file_path = ""
        
        # This should fail early in the actual function with validation
        # But let's test the normalization logic
        if file_path and file_path.startswith("./"):
            file_path = file_path[2:]  # Remove ./
        
        # Only prepend /workspace/ if not already present and not an absolute path
        if file_path and not file_path.startswith("/workspace/") and not file_path.startswith("/"):
            file_path = f"/workspace/{file_path}"
        elif file_path and file_path.startswith("/") and not file_path.startswith("/workspace/"):
            # If it's an absolute path not in workspace, prepend workspace
            file_path = f"/workspace{file_path}"
            
        assert file_path == ""

    def test_multiple_slashes(self):
        """Test paths with multiple slashes are handled correctly."""
        file_path = "src//main.py"
        
        # Normalize file path - handle various input formats
        if file_path.startswith("./"):
            file_path = file_path[2:]  # Remove ./
        
        # Only prepend /workspace/ if not already present and not an absolute path
        if not file_path.startswith("/workspace/") and not file_path.startswith("/"):
            file_path = f"/workspace/{file_path}"
        elif file_path.startswith("/") and not file_path.startswith("/workspace/"):
            # If it's an absolute path not in workspace, prepend workspace
            file_path = f"/workspace{file_path}"
            
        # Multiple slashes are preserved
        assert file_path == "/workspace/src//main.py"

    def test_trailing_slash(self):
        """Test that trailing slashes are preserved."""
        file_path = "src/"
        
        # Normalize file path - handle various input formats
        if file_path.startswith("./"):
            file_path = file_path[2:]  # Remove ./
        
        # Only prepend /workspace/ if not already present and not an absolute path
        if not file_path.startswith("/workspace/") and not file_path.startswith("/"):
            file_path = f"/workspace/{file_path}"
        elif file_path.startswith("/") and not file_path.startswith("/workspace/"):
            # If it's an absolute path not in workspace, prepend workspace
            file_path = f"/workspace{file_path}"
            
        assert file_path == "/workspace/src/"


def test_docstring_examples():
    """Test that the examples in the docstring work as documented."""
    # From sandbox_edit_file docstring:
    # - Relative path: "src/main.py" or "main.py"
    # - Path with ./: "./src/main.py" (the ./ will be stripped)
    # - DO NOT include /workspace/ prefix - it's added automatically
    
    paths_and_expected = [
        ("src/main.py", "/workspace/src/main.py"),
        ("main.py", "/workspace/main.py"),
        ("./src/main.py", "/workspace/src/main.py"),
        ("./main.py", "/workspace/main.py"),
        ("/workspace/src/main.py", "/workspace/src/main.py"),
        ("/src/main.py", "/workspace/src/main.py"),
        ("tests/test.py", "/workspace/tests/test.py"),
        ("./tests/test.py", "/workspace/tests/test.py"),
    ]
    
    for input_path, expected in paths_and_expected:
        file_path = input_path
        
        # Normalize file path - handle various input formats
        if file_path.startswith("./"):
            file_path = file_path[2:]  # Remove ./
        
        # Only prepend /workspace/ if not already present and not an absolute path
        if not file_path.startswith("/workspace/") and not file_path.startswith("/"):
            file_path = f"/workspace/{file_path}"
        elif file_path.startswith("/") and not file_path.startswith("/workspace/"):
            # If it's an absolute path not in workspace, prepend workspace
            file_path = f"/workspace{file_path}"
            
        assert file_path == expected, f"Failed for {input_path}: got {file_path}, expected {expected}"