"""Simple tests for Claude E2B functionality that work without complex imports."""

import pytest
import json
import sys
from unittest.mock import <PERSON><PERSON>, MagicMock, patch
from datetime import datetime

# Mock the e2b module to avoid import issues
sys.modules['e2b_code_interpreter'] = MagicMock()

# Now we can import our code
from agents.claude_e2b.claude import <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, handle_claude_stream


def test_claude_output_basic():
    """Test basic ClaudeOutput functionality."""
    output = ClaudeOutput(
        timestamp=123.45,
        type="test",
        content="hello"
    )
    assert output.timestamp == 123.45
    assert output.type == "test"
    assert output.content == "hello"
    assert str(output) == "[123.45] test: hello"


def test_claude_session_basic():
    """Test basic ClaudeSession functionality."""
    session = ClaudeSession(session_id="test-123", prompt="test prompt")
    
    assert session.session_id == "test-123"
    assert session.prompt == "test prompt"
    assert len(session.outputs) == 0
    assert session.success is False
    
    # Test adding output
    output = ClaudeOutput(timestamp=123.0, type="test", content="content")
    session.add_output(output)
    assert len(session.outputs) == 1
    
    # Test finalization
    session.finalize(success=True)
    assert session.success is True
    assert session.end_time is not None


def test_handle_claude_stream_basic():
    """Test the handle_claude_stream function with basic events."""
    # Test empty line
    result = handle_claude_stream("")
    assert result is None
    
    # Test invalid JSON
    result = handle_claude_stream("not json")
    assert result is None
    
    # Test system event
    system_event = {
        "type": "system",
        "cwd": "/home/<USER>",
        "model": "claude-3-5-sonnet"
    }
    result = handle_claude_stream(json.dumps(system_event))
    assert result is not None
    assert result.type == "system"
    assert result.content["cwd"] == "/home/<USER>"
    
    # Test Claude message
    message_event = {
        "type": "assistant", 
        "message": {
            "content": [{"type": "text", "text": "Hello!"}]
        }
    }
    result = handle_claude_stream(json.dumps(message_event))
    assert result is not None
    assert result.type == "claude_message"
    assert result.content == "Hello!"
    
    # Test tool call
    tool_event = {
        "type": "assistant",
        "message": {
            "content": [{
                "type": "tool_use",
                "name": "Write",
                "input": {"file_path": "/tmp/test.py"}
            }]
        }
    }
    result = handle_claude_stream(json.dumps(tool_event))
    assert result is not None
    assert result.type == "tool_call"
    assert result.content["name"] == "Write"
    
    # Test final result
    result_event = {
        "type": "result",
        "is_error": False,
        "result": "Success",
        "duration_ms": 1000,
        "total_cost_usd": 0.01
    }
    result = handle_claude_stream(json.dumps(result_event))
    assert result is not None
    assert result.type == "result"
    assert result.content["result"] == "Success"


def test_session_tracking_with_stream():
    """Test that session properly tracks stream events."""
    session = ClaudeSession(session_id="test", prompt="test")
    
    # Process a system event
    system_event = {"type": "system", "cwd": "/home", "model": "claude"}
    handle_claude_stream(json.dumps(system_event), session)
    assert len(session.outputs) == 1
    
    # Process a result event that should finalize the session
    result_event = {
        "type": "result",
        "is_error": False,
        "result": "Task completed",
        "duration_ms": 2000,
        "total_cost_usd": 0.005
    }
    handle_claude_stream(json.dumps(result_event), session)
    
    assert len(session.outputs) == 2
    assert session.success is True
    assert session.duration_ms == 2000
    assert session.total_cost_usd == 0.005
    assert session.end_time is not None


if __name__ == "__main__":
    # Allow running this file directly
    print("🧪 Running simple Claude E2B tests...")
    
    test_claude_output_basic()
    print("✅ Claude Output tests passed")
    
    test_claude_session_basic()
    print("✅ Claude Session tests passed")
    
    test_handle_claude_stream_basic()
    print("✅ Stream handling tests passed")
    
    test_session_tracking_with_stream()
    print("✅ Session tracking tests passed")
    
    print("🎉 All tests passed!")