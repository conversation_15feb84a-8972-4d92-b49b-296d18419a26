"""
Unit tests for sandbox_run_command tool with mocked sandbox.
Fast tests focusing on tool logic without external dependencies.
"""

import pytest
from unittest.mock import Mock
from agents.tools.sandbox_tools_state import sandbox_run_command

# Mark all tests in this module as async using anyio
pytestmark = pytest.mark.anyio


class TestSandboxRunCommand:
    """Test the sandbox_run_command tool with mocked sandbox."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.mock_sandbox = Mock()
        self.mock_process = Mock()
        self.mock_sandbox.process = self.mock_process
    
    async def test_successful_command_execution_with_result(self):
        """Test successful command execution when result has 'result' attribute."""
        # Setup
        self.mock_process.exec.return_value = Mock(result="Hello World")
        
        # Execute function directly for unit testing (bypassing pydantic validation)
        result = await sandbox_run_command.coroutine("echo 'Hello World'", self.mock_sandbox)
        
        # Assert
        assert result == "Hello World"
        self.mock_process.exec.assert_called_once_with("echo 'Hello World'")
    
    async def test_successful_command_execution_with_stdout(self):
        """Test successful command execution when result has 'stdout' attribute."""
        # Setup - mock without 'result' attribute so it falls back to 'stdout'
        mock_result = Mock()
        del mock_result.result  # Remove result attribute so hasattr returns False
        mock_result.stdout = "Output from stdout"
        self.mock_process.exec.return_value = mock_result
        
        # Execute function directly for unit testing (bypassing pydantic validation)
        result = await sandbox_run_command.coroutine("ls", self.mock_sandbox)
        
        # Assert
        assert result == "Output from stdout"
    
    async def test_fallback_to_str_conversion(self):
        """Test fallback when result has neither 'result' nor 'stdout'."""
        # Setup - mock without 'result' or 'stdout' attributes so it falls back to str()
        mock_result = Mock()
        del mock_result.result  # Remove result attribute so hasattr returns False
        del mock_result.stdout  # Remove stdout attribute so hasattr returns False
        mock_result.__str__ = Mock(return_value="String representation")
        self.mock_process.exec.return_value = mock_result
        
        # Execute function directly for unit testing (bypassing pydantic validation)
        result = await sandbox_run_command.coroutine("some_command", self.mock_sandbox)
        
        # Assert
        assert result == "String representation"
    
    async def test_no_output_command(self):
        """Test command that produces no output."""
        # Setup
        self.mock_process.exec.return_value = Mock(result="")
        
        # Execute function directly for unit testing (bypassing pydantic validation)
        result = await sandbox_run_command.coroutine("touch test.txt", self.mock_sandbox)
        
        # Assert
        assert result == "Command executed successfully (no output)"
    
    async def test_whitespace_only_output(self):
        """Test command that produces only whitespace."""
        # Setup
        self.mock_process.exec.return_value = Mock(result="   \n\t  ")
        
        # Execute function directly for unit testing (bypassing pydantic validation)
        result = await sandbox_run_command.coroutine("echo '   '", self.mock_sandbox)
        
        # Assert
        assert result == "Command executed successfully (no output)"
    
    async def test_command_execution_exception(self):
        """Test handling of command execution exceptions."""
        # Setup
        self.mock_process.exec.side_effect = Exception("Connection failed")
        
        # Execute function directly for unit testing (bypassing pydantic validation)
        result = await sandbox_run_command.coroutine("ls", self.mock_sandbox)
        
        # Assert
        assert result == "Command execution failed: Connection failed"
    
    async def test_complex_command_with_special_characters(self):
        """Test command with special characters and quotes."""
        # Setup
        self.mock_process.exec.return_value = Mock(result="Complex output")
        
        # Execute function directly for unit testing (bypassing pydantic validation)
        result = await sandbox_run_command.coroutine("echo 'test \"quoted\" string' | grep test", self.mock_sandbox)
        
        # Assert
        assert result == "Complex output"
        self.mock_process.exec.assert_called_once_with("echo 'test \"quoted\" string' | grep test")
    
    async def test_multiline_output(self):
        """Test command that returns multiline output."""
        # Setup
        multiline_output = "line1\nline2\nline3"
        self.mock_process.exec.return_value = Mock(result=multiline_output)
        
        # Execute function directly for unit testing (bypassing pydantic validation)
        result = await sandbox_run_command.coroutine("ls -la", self.mock_sandbox)
        
        # Assert
        assert result == multiline_output
        assert "line1" in result
        assert "line2" in result
        assert "line3" in result