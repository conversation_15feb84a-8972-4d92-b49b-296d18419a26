"""Basic tests for Claude Daytona integration."""

import pytest
import asyncio
from unittest.mock import Async<PERSON>ock, MagicMock, patch
from dataclasses import dataclass

from agents.claude_daytona.claude import (
    <PERSON><PERSON>ut<PERSON>,
    <PERSON>Session,
    handle_claude_stream,
    run_claude_in_daytona_sandbox,
    stream_claude_in_daytona_sandbox
)

pytestmark = pytest.mark.anyio


class TestClaudeOutput:
    """Test ClaudeOutput dataclass."""
    
    def test_claude_output_creation(self):
        """Test creating a ClaudeOutput instance."""
        output = ClaudeOutput(
            timestamp=1234567890.0,
            type="test",
            content="test content"
        )
        
        assert output.timestamp == 1234567890.0
        assert output.type == "test"
        assert output.content == "test content"
        assert output.raw_event is None
    
    def test_claude_output_str(self):
        """Test string representation of ClaudeOutput."""
        output = ClaudeOutput(
            timestamp=1234567890.0,
            type="test",
            content="test content"
        )
        
        expected = "[1234567890.00] test: test content"
        assert str(output) == expected


class TestClaudeSession:
    """Test ClaudeSession dataclass."""
    
    def test_claude_session_creation(self):
        """Test creating a <PERSON>Session instance."""
        session = ClaudeSession(
            session_id="test-session",
            prompt="test prompt"
        )
        
        assert session.session_id == "test-session"
        assert session.prompt == "test prompt"
        assert session.outputs == []
        assert session.success is False
        assert session.error is None
    
    def test_add_output(self):
        """Test adding outputs to session."""
        session = ClaudeSession(
            session_id="test-session",
            prompt="test prompt"
        )
        
        output = ClaudeOutput(
            timestamp=1234567890.0,
            type="test",
            content="test content"
        )
        
        session.add_output(output)
        assert len(session.outputs) == 1
        assert session.outputs[0] == output
    
    def test_finalize_success(self):
        """Test finalizing session with success."""
        session = ClaudeSession(
            session_id="test-session",
            prompt="test prompt"
        )
        
        session.finalize(success=True)
        
        assert session.success is True
        assert session.error is None
        assert session.end_time is not None
    
    def test_finalize_error(self):
        """Test finalizing session with error."""
        session = ClaudeSession(
            session_id="test-session",
            prompt="test prompt"
        )
        
        session.finalize(success=False, error="test error")
        
        assert session.success is False
        assert session.error == "test error"
        assert session.end_time is not None
    
    def test_elapsed_time(self):
        """Test elapsed time calculation."""
        session = ClaudeSession(
            session_id="test-session",
            prompt="test prompt"
        )
        
        # Should return time since start
        elapsed = session.elapsed_time
        assert elapsed >= 0
        
        # After finalization, should return actual duration
        session.finalize()
        final_elapsed = session.elapsed_time
        assert final_elapsed >= elapsed


class TestHandleClaudeStream:
    """Test stream handling functions."""
    
    def test_handle_empty_line(self):
        """Test handling empty lines."""
        result = handle_claude_stream("")
        assert result is None
        
        result = handle_claude_stream("   ")
        assert result is None
    
    def test_handle_invalid_json(self):
        """Test handling invalid JSON."""
        result = handle_claude_stream("not json")
        assert result is None
    
    def test_handle_system_event(self):
        """Test handling system events."""
        json_line = '{"type": "system", "cwd": "/test", "model": "claude-3", "permissionMode": "safe"}'
        
        result = handle_claude_stream(json_line)
        
        assert result is not None
        assert result.type == "system"
        assert result.content["cwd"] == "/test"
        assert result.content["model"] == "claude-3"
        assert result.content["permissionMode"] == "safe"
    
    def test_handle_assistant_text_event(self):
        """Test handling assistant text events."""
        json_line = '{"type": "assistant", "message": {"content": [{"type": "text", "text": "Hello world"}]}}'
        
        result = handle_claude_stream(json_line)
        
        assert result is not None
        assert result.type == "claude_message"
        assert result.content == "Hello world"
    
    def test_handle_tool_use_event(self):
        """Test handling tool use events."""
        json_line = '{"type": "assistant", "message": {"content": [{"type": "tool_use", "name": "Bash", "input": {"command": "ls"}}]}}'
        
        result = handle_claude_stream(json_line)
        
        assert result is not None
        assert result.type == "tool_call"
        assert result.content["name"] == "Bash"
        assert result.content["input"]["command"] == "ls"
    
    def test_handle_tool_result_event(self):
        """Test handling tool result events."""
        json_line = '{"type": "user", "message": {"content": [{"type": "tool_result", "tool_use_id": "123", "is_error": false, "content": "file1.txt"}]}}'
        
        result = handle_claude_stream(json_line)
        
        assert result is not None
        assert result.type == "tool_result"
        assert result.content["tool_use_id"] == "123"
        assert result.content["is_error"] is False
        assert result.content["content"] == "file1.txt"
    
    def test_handle_result_event(self):
        """Test handling final result events."""
        json_line = '{"type": "result", "is_error": false, "result": "Task completed", "duration_ms": 1000, "total_cost_usd": 0.01}'
        
        result = handle_claude_stream(json_line)
        
        assert result is not None
        assert result.type == "result"
        assert result.content["is_error"] is False
        assert result.content["result"] == "Task completed"
        assert result.content["duration_ms"] == 1000
        assert result.content["total_cost_usd"] == 0.01
    
    def test_handle_error_event(self):
        """Test handling error events."""
        json_line = '{"type": "error", "error": "Something went wrong"}'
        
        result = handle_claude_stream(json_line)
        
        assert result is not None
        assert result.type == "error"
        assert result.content == "Something went wrong"
    
    def test_session_update_on_result(self):
        """Test that session is updated on result events."""
        session = ClaudeSession(session_id="test", prompt="test")
        json_line = '{"type": "result", "is_error": false, "result": "Done", "duration_ms": 1000, "total_cost_usd": 0.01}'
        
        result = handle_claude_stream(json_line, session)
        
        assert session.duration_ms == 1000
        assert session.total_cost_usd == 0.01
        assert session.success is True
        assert session.end_time is not None
    
    def test_session_update_on_error(self):
        """Test that session is updated on error events."""
        session = ClaudeSession(session_id="test", prompt="test")
        json_line = '{"type": "error", "error": "Failed"}'
        
        result = handle_claude_stream(json_line, session)
        
        assert session.success is False
        assert session.error == "Failed"
        assert session.end_time is not None


class TestDaytonaSandboxIntegration:
    """Test Daytona sandbox integration functions."""
    
    @pytest.fixture
    def mock_sandbox(self):
        """Create a mock Daytona sandbox."""
        sandbox = MagicMock()
        sandbox.process = MagicMock()
        return sandbox
    
    @pytest.fixture
    def mock_result(self):
        """Create a mock command result."""
        result = MagicMock()
        result.stdout = '{"type": "result", "is_error": false, "result": "Success", "duration_ms": 1000, "total_cost_usd": 0.01}'
        result.exit_code = 0
        return result
    
    async def test_run_claude_in_daytona_sandbox_success(self, mock_sandbox, mock_result):
        """Test successful Claude execution in Daytona sandbox."""
        # Mock the async execution
        mock_sandbox.process.execute_session_command = AsyncMock(return_value=mock_result)
        
        session = await run_claude_in_daytona_sandbox(
            sandbox=mock_sandbox,
            run_id="test-run",
            prompt="test prompt",
            timeout=60
        )
        
        assert session.success is True
        assert session.prompt == "test prompt"
        assert len(session.outputs) > 0
        
        # Verify the command was called
        mock_sandbox.process.execute_session_command.assert_called_once()
    
    async def test_run_claude_with_options(self, mock_sandbox, mock_result):
        """Test Claude execution with additional options."""
        mock_sandbox.process.execute_session_command = AsyncMock(return_value=mock_result)
        
        session = await run_claude_in_daytona_sandbox(
            sandbox=mock_sandbox,
            run_id="test-run",
            prompt="test prompt",
            claude_options={"max-turns": "10", "model": "claude-3-5-sonnet-20241022"},
            cwd="/custom/path",
            timeout=120
        )
        
        assert session.success is True
        
        # Verify the command included the options
        call_args = mock_sandbox.process.execute_session_command.call_args
        command = call_args[0][1].command  # SessionExecuteRequest.command
        
        assert "--max-turns" in command
        assert "10" in command
        assert "--model" in command
        assert "claude-3-5-sonnet-20241022" in command
        assert "/custom/path" in command
    
    async def test_stream_claude_in_daytona_sandbox(self, mock_sandbox):
        """Test streaming Claude execution."""
        # Mock multiple output lines
        mock_result = MagicMock()
        mock_result.stdout = '\n'.join([
            '{"type": "system", "cwd": "/test"}',
            '{"type": "assistant", "message": {"content": [{"type": "text", "text": "Hello"}]}}',
            '{"type": "result", "is_error": false, "result": "Done", "duration_ms": 1000, "total_cost_usd": 0.01}'
        ])
        mock_result.exit_code = 0
        
        mock_sandbox.process.execute_session_command = AsyncMock(return_value=mock_result)
        
        outputs = []
        async for output in stream_claude_in_daytona_sandbox(
            sandbox=mock_sandbox,
            run_id="test-run",
            prompt="test prompt"
        ):
            outputs.append(output)
        
        # Should have collected multiple outputs
        assert len(outputs) >= 3
        
        # Check output types
        output_types = [output.type for output in outputs]
        assert "system" in output_types
        assert "claude_message" in output_types
        assert "result" in output_types
    
    async def test_run_claude_command_failure(self, mock_sandbox):
        """Test handling of command failures."""
        mock_result = MagicMock()
        mock_result.stdout = ""
        mock_result.exit_code = 1
        
        mock_sandbox.process.execute_session_command = AsyncMock(return_value=mock_result)
        
        session = await run_claude_in_daytona_sandbox(
            sandbox=mock_sandbox,
            run_id="test-run",
            prompt="test prompt"
        )
        
        assert session.success is False
        assert "exited with code 1" in session.error