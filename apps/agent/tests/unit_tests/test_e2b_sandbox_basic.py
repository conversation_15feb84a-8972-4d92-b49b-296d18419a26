"""Basic unit tests for E2B sandbox functionality."""

import pytest
from unittest.mock import Mock, AsyncMock, patch, MagicMock
import uuid
import os

pytestmark = pytest.mark.anyio

# Mock the e2b_code_interpreter module since it's not available in test environment
class MockAsyncSandbox:
    def __init__(self, template=None, envs=None, timeout=None):
        self.template = template
        self.envs = envs
        self.timeout = timeout
        self.id = f"sandbox-{uuid.uuid4()}"
        self.commands = MockCommands()
        self._session_id = None
    
    @classmethod
    async def create(cls, template=None, envs=None, timeout=None):
        return cls(template=template, envs=envs, timeout=timeout)
    
    async def kill(self):
        pass

class MockCommands:
    def __init__(self):
        pass
    
    async def run(self, command, cwd=None, timeout=None, on_stdout=None, on_stderr=None):
        # Mock successful execution
        result = MockResult()
        
        # Simulate different command responses
        if "claude --version" in command:
            result.stdout = "Claude Code v1.0.0"
            result.exit_code = 0
        elif "pwd" in command:
            result.stdout = "/home/<USER>"
            result.exit_code = 0
        elif "whoami" in command:
            result.stdout = "user"
            result.exit_code = 0
        elif "mkdir" in command:
            result.stdout = ""
            result.exit_code = 0
        else:
            result.stdout = "Command executed"
            result.exit_code = 0
        
        # Call callbacks if provided
        if on_stdout and result.stdout:
            await on_stdout(result.stdout)
        if on_stderr and result.stderr:
            await on_stderr(result.stderr)
        
        return result

class MockResult:
    def __init__(self):
        self.exit_code = 0
        self.stdout = ""
        self.stderr = ""


# Patch the import
with patch.dict('sys.modules', {'e2b_code_interpreter': MagicMock()}):
    from agents.claude_e2b.e2b_sandbox import (
        create_sandbox,
        cleanup_sandbox,
        sandbox_context,
        run_command_in_sandbox
    )


class TestCreateSandbox:
    """Test the create_sandbox function."""
    
    @patch('agents.claude_e2b.e2b_sandbox.AsyncSandbox', MockAsyncSandbox)
    @patch.dict(os.environ, {
        'ANTHROPIC_API_KEY': 'test-anthropic-key',
        'GH_TOKEN': 'test-github-token'
    })
    async def test_create_sandbox_with_defaults(self):
        """Test creating sandbox with default parameters."""
        sandbox, session_id = await create_sandbox()
        
        assert sandbox is not None
        assert isinstance(session_id, str)
        assert len(session_id) > 0
        assert hasattr(sandbox, '_session_id')
        assert sandbox._session_id == session_id
    
    @patch('agents.claude_e2b.e2b_sandbox.AsyncSandbox', MockAsyncSandbox)
    @patch.dict(os.environ, {
        'ANTHROPIC_API_KEY': 'test-anthropic-key',
        'GITHUB_TOKEN': 'test-github-token-alt'  # Test alternate env var
    })
    async def test_create_sandbox_with_github_token_alt(self):
        """Test creating sandbox with GITHUB_TOKEN env var."""
        sandbox, session_id = await create_sandbox()
        
        # Should work with either GH_TOKEN or GITHUB_TOKEN
        assert sandbox is not None
        assert isinstance(session_id, str)
    
    @patch('agents.claude_e2b.e2b_sandbox.AsyncSandbox', MockAsyncSandbox)
    @patch.dict(os.environ, {'ANTHROPIC_API_KEY': 'test-key'}, clear=True)
    async def test_create_sandbox_without_github_token(self):
        """Test creating sandbox without GitHub token (should still work with warning)."""
        sandbox, session_id = await create_sandbox()
        
        assert sandbox is not None
        assert isinstance(session_id, str)
    
    @patch('agents.claude_e2b.e2b_sandbox.AsyncSandbox', MockAsyncSandbox)
    @patch.dict(os.environ, {'ANTHROPIC_API_KEY': 'test-key'})
    async def test_create_sandbox_with_custom_session_id(self):
        """Test creating sandbox with custom session ID."""
        custom_id = "my-custom-session-123"
        
        sandbox, session_id = await create_sandbox(session_id=custom_id)
        
        assert session_id == custom_id
        assert sandbox._session_id == custom_id
    
    @patch('agents.claude_e2b.e2b_sandbox.AsyncSandbox', MockAsyncSandbox)
    @patch.dict(os.environ, {'ANTHROPIC_API_KEY': 'test-key'})
    async def test_create_sandbox_with_custom_template(self):
        """Test creating sandbox with custom template ID."""
        custom_template = "custom-template-123"
        
        sandbox, session_id = await create_sandbox(template_id=custom_template)
        
        assert sandbox.template == custom_template
    
    @patch('agents.claude_e2b.e2b_sandbox.AsyncSandbox', MockAsyncSandbox)
    @patch.dict(os.environ, {'ANTHROPIC_API_KEY': 'test-key'})
    async def test_create_sandbox_with_additional_envs(self):
        """Test creating sandbox with additional environment variables."""
        additional_envs = {
            "CUSTOM_VAR": "custom_value",
            "DEBUG": "true"
        }
        
        sandbox, session_id = await create_sandbox(envs=additional_envs)
        
        # Check that both default and additional envs are included
        expected_envs = {
            "ANTHROPIC_API_KEY": "test-key",
            "WORKSPACE_PATH": "/home/<USER>/workspace",
            "CUSTOM_VAR": "custom_value",
            "DEBUG": "true"
        }
        
        for key, value in expected_envs.items():
            assert key in sandbox.envs
            assert sandbox.envs[key] == value
    
    @patch('src.agents.claude_e2b.e2b_sandbox.AsyncSandbox')
    @patch.dict(os.environ, {'ANTHROPIC_API_KEY': 'test-key'})
    async def test_create_sandbox_handles_creation_failure(self):
        """Test that sandbox creation failure is handled properly."""
        # Mock AsyncSandbox.create to raise an exception
        MockAsyncSandbox.create = AsyncMock(side_effect=Exception("Failed to create sandbox"))
        
        with pytest.raises(Exception, match="Failed to create sandbox"):
            await create_sandbox()


class TestCleanupSandbox:
    """Test the cleanup_sandbox function."""
    
    async def test_cleanup_sandbox_success(self):
        """Test successful sandbox cleanup."""
        mock_sandbox = AsyncMock()
        mock_sandbox.kill = AsyncMock()
        mock_sandbox._session_id = "test-session"
        
        # Should not raise any exception
        await cleanup_sandbox(mock_sandbox)
        
        # Verify kill was called
        mock_sandbox.kill.assert_called_once()
    
    async def test_cleanup_sandbox_none(self):
        """Test cleanup with None sandbox (should not error)."""
        # Should not raise any exception
        await cleanup_sandbox(None)
    
    async def test_cleanup_sandbox_handles_failure(self):
        """Test that cleanup failure is handled and re-raised."""
        mock_sandbox = AsyncMock()
        mock_sandbox.kill = AsyncMock(side_effect=Exception("Cleanup failed"))
        mock_sandbox._session_id = "test-session"
        
        with pytest.raises(Exception, match="Cleanup failed"):
            await cleanup_sandbox(mock_sandbox)


class TestRunCommandInSandbox:
    """Test the run_command_in_sandbox function."""
    
    async def test_run_command_success(self):
        """Test running a successful command."""
        mock_sandbox = Mock()
        mock_sandbox.commands = AsyncMock()
        
        mock_result = Mock()
        mock_result.exit_code = 0
        mock_result.stdout = "Hello world"
        mock_result.stderr = ""
        
        mock_sandbox.commands.run = AsyncMock(return_value=mock_result)
        
        result = await run_command_in_sandbox(mock_sandbox, "echo 'Hello world'")
        
        assert result["exit_code"] == 0
        assert result["stdout"] == "Hello world"
        assert result["stderr"] == ""
        assert "duration" in result
        assert result["duration"] >= 0
        
        # Verify command was called correctly
        mock_sandbox.commands.run.assert_called_once_with(
            "echo 'Hello world'",
            cwd=None,
            timeout=120
        )
    
    async def test_run_command_failure(self):
        """Test running a command that fails."""
        mock_sandbox = Mock()
        mock_sandbox.commands = AsyncMock()
        
        mock_result = Mock()
        mock_result.exit_code = 1
        mock_result.stdout = ""
        mock_result.stderr = "Command not found"
        
        mock_sandbox.commands.run = AsyncMock(return_value=mock_result)
        
        result = await run_command_in_sandbox(mock_sandbox, "nonexistent-command")
        
        assert result["exit_code"] == 1
        assert result["stderr"] == "Command not found"
    
    async def test_run_command_with_custom_options(self):
        """Test running command with custom cwd and timeout."""
        mock_sandbox = Mock()
        mock_sandbox.commands = AsyncMock()
        
        mock_result = Mock()
        mock_result.exit_code = 0
        mock_result.stdout = "success"
        mock_result.stderr = ""
        
        mock_sandbox.commands.run = AsyncMock(return_value=mock_result)
        
        result = await run_command_in_sandbox(
            mock_sandbox, 
            "ls", 
            cwd="/tmp", 
            timeout=60
        )
        
        assert result["exit_code"] == 0
        
        # Verify custom options were passed
        mock_sandbox.commands.run.assert_called_once_with(
            "ls",
            cwd="/tmp",
            timeout=60
        )
    
    async def test_run_command_handles_exception(self):
        """Test that command execution exceptions are handled."""
        mock_sandbox = Mock()
        mock_sandbox.commands = AsyncMock()
        mock_sandbox.commands.run = AsyncMock(side_effect=Exception("Connection lost"))
        
        with pytest.raises(Exception, match="Connection lost"):
            await run_command_in_sandbox(mock_sandbox, "ls")


class TestSandboxContext:
    """Test the sandbox_context context manager."""
    
    @patch('agents.claude_e2b.e2b_sandbox.create_sandbox')
    @patch('agents.claude_e2b.e2b_sandbox.cleanup_sandbox')
    async def test_sandbox_context_success(self, mock_cleanup, mock_create):
        """Test successful sandbox context manager usage."""
        mock_sandbox = Mock()
        mock_session_id = "test-session"
        
        mock_create.return_value = (mock_sandbox, mock_session_id)
        mock_cleanup.return_value = None
        
        async with sandbox_context() as sandbox:
            assert sandbox == mock_sandbox
            # Do something with sandbox
            pass
        
        # Verify create and cleanup were called
        mock_create.assert_called_once()
        mock_cleanup.assert_called_once_with(mock_sandbox)
    
    @patch('agents.claude_e2b.e2b_sandbox.create_sandbox')
    @patch('agents.claude_e2b.e2b_sandbox.cleanup_sandbox')
    async def test_sandbox_context_with_custom_options(self, mock_cleanup, mock_create):
        """Test sandbox context with custom template and envs."""
        mock_sandbox = Mock()
        mock_create.return_value = (mock_sandbox, "session")
        
        custom_envs = {"TEST": "value"}
        
        async with sandbox_context(template_id="custom", envs=custom_envs) as sandbox:
            assert sandbox == mock_sandbox
        
        # Verify create was called with custom options
        mock_create.assert_called_once_with(template_id="custom", envs=custom_envs)
        mock_cleanup.assert_called_once_with(mock_sandbox)
    
    @patch('agents.claude_e2b.e2b_sandbox.create_sandbox')
    @patch('agents.claude_e2b.e2b_sandbox.cleanup_sandbox')
    async def test_sandbox_context_handles_creation_failure(self, mock_cleanup, mock_create):
        """Test that context manager handles creation failure properly."""
        mock_create.side_effect = Exception("Creation failed")
        
        with pytest.raises(Exception, match="Creation failed"):
            async with sandbox_context():
                pass
        
        # Cleanup should not be called if creation failed
        mock_cleanup.assert_not_called()
    
    @patch('agents.claude_e2b.e2b_sandbox.create_sandbox')
    @patch('agents.claude_e2b.e2b_sandbox.cleanup_sandbox')
    async def test_sandbox_context_ensures_cleanup_on_exception(self, mock_cleanup, mock_create):
        """Test that cleanup is called even if an exception occurs in the context."""
        mock_sandbox = Mock()
        mock_create.return_value = (mock_sandbox, "session")
        
        with pytest.raises(ValueError, match="Test error"):
            async with sandbox_context() as sandbox:
                # Simulate an error in user code
                raise ValueError("Test error")
        
        # Cleanup should still be called
        mock_cleanup.assert_called_once_with(mock_sandbox)