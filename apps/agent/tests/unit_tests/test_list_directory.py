"""
Unit tests for the enhanced list_directory tool with depth support.
"""

import pytest
import os
import tempfile
import shutil
from pathlib import Path
import sys

# Add the src directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../src'))

from agents.tools.local_tools import list_directory

pytestmark = pytest.mark.anyio


class TestListDirectory:
    """Test cases for the list_directory function."""
    
    @pytest.fixture
    async def temp_dir_structure(self):
        """Create a temporary directory structure for testing."""
        temp_dir = tempfile.mkdtemp()
        
        # Create a nested directory structure
        # Root level
        Path(temp_dir, "file1.txt").write_text("content1")
        Path(temp_dir, "file2.py").write_text("print('hello')")
        Path(temp_dir, ".hidden_file").write_text("hidden")
        
        # Level 1 directories
        os.makedirs(Path(temp_dir, "src"))
        os.makedirs(Path(temp_dir, "tests"))
        os.makedirs(Path(temp_dir, ".git"))
        
        # Level 1 files
        Path(temp_dir, "src", "main.py").write_text("# main file")
        Path(temp_dir, "src", "utils.py").write_text("# utils")
        Path(temp_dir, "tests", "test_main.py").write_text("# tests")
        
        # Level 2 directories
        os.makedirs(Path(temp_dir, "src", "components"))
        os.makedirs(Path(temp_dir, "src", "models"))
        os.makedirs(Path(temp_dir, "tests", "unit"))
        
        # Level 2 files
        Path(temp_dir, "src", "components", "button.tsx").write_text("export Button")
        Path(temp_dir, "src", "components", "input.tsx").write_text("export Input")
        Path(temp_dir, "src", "models", "user.py").write_text("class User")
        Path(temp_dir, "tests", "unit", "test_user.py").write_text("test user")
        
        # Level 3 directories and files
        os.makedirs(Path(temp_dir, "src", "components", "common"))
        Path(temp_dir, "src", "components", "common", "index.ts").write_text("export *")
        
        yield temp_dir
        
        # Cleanup
        shutil.rmtree(temp_dir)
    
    async def test_default_depth_2(self, temp_dir_structure):
        """Test listing with default depth of 2."""
        result = await list_directory.ainvoke({"path": temp_dir_structure})
        
        # Should show root level and one level deep
        assert "📁 src/" in result
        assert "📁 tests/" in result
        assert "📄 file1.txt" in result
        assert "📄 file2.py" in result
        
        # Should show level 1 subdirectories
        assert "📁 components/" in result
        assert "📁 models/" in result
        assert "📁 unit/" in result
        
        # Should show level 1 files
        assert "main.py" in result
        assert "utils.py" in result
        
        # Should NOT show level 2 files (depth limit)
        assert "button.tsx" not in result
        assert "user.py" not in result
        
        # Should NOT show hidden files by default
        assert ".hidden_file" not in result
        assert ".git" not in result
    
    async def test_depth_1(self, temp_dir_structure):
        """Test listing with depth 1 (immediate contents only)."""
        result = await list_directory.ainvoke({"path": temp_dir_structure, "max_depth": 1})
        
        # Should show only root level
        assert "📁 src/" in result
        assert "📁 tests/" in result
        assert "📄 file1.txt" in result
        assert "📄 file2.py" in result
        
        # Should NOT show any subdirectories or their contents
        assert "components" not in result
        assert "models" not in result
        assert "main.py" not in result
        assert "utils.py" not in result
    
    async def test_depth_3(self, temp_dir_structure):
        """Test listing with depth 3."""
        result = await list_directory.ainvoke({"path": temp_dir_structure, "max_depth": 3})
        
        # Should show all three levels
        assert "📁 src/" in result
        assert "📁 components/" in result
        assert "📁 common/" in result  # Level 3 directory
        
        # Should show level 2 files
        assert "button.tsx" in result
        assert "input.tsx" in result
        assert "user.py" in result
        
        # Should NOT show level 3 files (at depth limit)
        assert "index.ts" not in result
    
    async def test_depth_4(self, temp_dir_structure):
        """Test listing with depth 4 (should show everything in our test structure)."""
        result = await list_directory.ainvoke({"path": temp_dir_structure, "max_depth": 4})
        
        # Should show everything including level 3 files
        assert "index.ts" in result
    
    async def test_show_hidden_files(self, temp_dir_structure):
        """Test showing hidden files."""
        result = await list_directory.ainvoke({"path": temp_dir_structure, "show_hidden": True, "max_depth": 2})
        
        # Should show hidden files and directories
        assert ".hidden_file" in result
        assert ".git/" in result
    
    async def test_tree_structure_formatting(self, temp_dir_structure):
        """Test that tree structure is properly formatted."""
        result = await list_directory.ainvoke({"path": str(Path(temp_dir_structure, "src")), "max_depth": 3})
        
        # Check for tree-like formatting
        assert "├──" in result or "└──" in result
        assert "│   " in result  # Indentation for nested items
    
    async def test_file_sizes(self, temp_dir_structure):
        """Test that file sizes are displayed."""
        # Create a larger file
        large_file_path = Path(temp_dir_structure, "large.bin")
        large_file_path.write_bytes(b"x" * 2048)  # 2KB file
        
        result = await list_directory.ainvoke({"path": temp_dir_structure, "max_depth": 1})
        
        # Check for size indicators
        assert "B)" in result  # Bytes for small files
        assert "2.0KB)" in result  # KB for the larger file
    
    async def test_nonexistent_path(self):
        """Test handling of nonexistent paths."""
        result = await list_directory.ainvoke({"path": "/this/path/does/not/exist"})
        assert "Path does not exist" in result
    
    async def test_file_instead_of_directory(self, temp_dir_structure):
        """Test handling when a file path is provided instead of directory."""
        file_path = Path(temp_dir_structure, "file1.txt")
        result = await list_directory.ainvoke({"path": str(file_path)})
        assert "Path is not a directory" in result
    
    async def test_empty_directory(self, temp_dir_structure):
        """Test handling of empty directories."""
        empty_dir = Path(temp_dir_structure, "empty")
        empty_dir.mkdir()
        
        result = await list_directory.ainvoke({"path": str(empty_dir)})
        assert "Directory is empty" in result
    
    async def test_permission_error_handling(self, temp_dir_structure):
        """Test handling of permission errors (if possible to test)."""
        # This test might be skipped on some systems where we can't create permission issues
        restricted_dir = Path(temp_dir_structure, "restricted")
        restricted_dir.mkdir()
        
        try:
            # Try to restrict permissions (may not work on all systems)
            os.chmod(restricted_dir, 0o000)
            result = await list_directory.ainvoke({"path": temp_dir_structure, "max_depth": 2})
            # Should handle the error gracefully
            assert "Permission denied" in result or "restricted" in result
        finally:
            # Restore permissions for cleanup
            os.chmod(restricted_dir, 0o755)
    
    async def test_depth_parameter_validation(self, temp_dir_structure):
        """Test that depth parameter is properly validated."""
        # Test with depth 0 (should be adjusted to 1)
        result = await list_directory.ainvoke({"path": temp_dir_structure, "max_depth": 0})
        assert "depth: 1" in result  # Should show minimum depth of 1
        
        # Test with negative depth (should be adjusted to 1)
        result = await list_directory.ainvoke({"path": temp_dir_structure, "max_depth": -5})
        assert "depth: 1" in result


# Integration test to demonstrate usage
async def test_integration_example():
    """Integration test showing practical usage."""
    # Test on the actual project structure
    result = await list_directory.ainvoke({"path": "src/graphs", "max_depth": 2})
    
    print("\n" + "="*80)
    print("Example output for src/graphs (depth 2):")
    print("="*80)
    print(result)
    
    # Verify it contains expected directories
    assert "📁 codegen/" in result


if __name__ == "__main__":
    # Run the tests
    pytest.main([__file__, "-v", "-s"])