"""Integration test for codegen graph with edit_file tool"""

import pytest
import tempfile
import os
from agents.heal_agent import Codegen

pytestmark = pytest.mark.anyio


class TestCodegenEditFile:
    """Test the codegen graph's ability to use the edit_file tool."""
    
    @pytest.fixture
    async def temp_python_file(self):
        """Create a temporary Python file for testing."""
        with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.py') as f:
            test_content = '''def calculate_area(length, width):
    """Calculate the area of a rectangle."""
    return length * width

def calculate_volume(length, width, height):
    # Calculate volume
    area = calculate_area(length, width)
    return area * height

class Shape:
    def __init__(self, name):
        self.name = name
    
    def get_name(self):
        return self.name
'''
            f.write(test_content)
            temp_path = f.name
        
        yield temp_path
        
        # Cleanup
        if os.path.exists(temp_path):
            os.unlink(temp_path)
    
    async def test_codegen_edit_single_function(self, temp_python_file):
        """Test codegen editing a single function."""
        # Create a request to edit the calculate_area function
        query = f"""Edit the file {temp_python_file} and update the calculate_area function to:
        1. Add parameter validation to ensure length and width are positive numbers
        2. Improve the docstring to be more descriptive
        3. Raise ValueError if parameters are invalid
        """
        
        # Create codegen agent and run
        agent = Codegen()
        result = await agent.run(query)
        
        # Check that the file was edited
        with open(temp_python_file, 'r') as f:
            content = f.read()
        
        # Verify the changes
        assert "ValueError" in content
        assert "def calculate_area(length, width):" in content
        # Should have some validation
        assert ("if length <= 0" in content) or ("if not length > 0" in content) or ("raise ValueError" in content)
    
    async def test_codegen_edit_class_method(self, temp_python_file):
        """Test codegen editing a class method."""
        query = f"""Edit the file {temp_python_file} and add a __str__ method to the Shape class that returns "Shape: {{self.name}}"."""
        
        agent = Codegen()
        result = await agent.run(query)
        
        # Verify the changes
        with open(temp_python_file, 'r') as f:
            content = f.read()
        
        assert "def __str__(self):" in content
        assert ("return f\"Shape: {self.name}\"" in content) or ("return \"Shape: \" + self.name" in content) or ("'Shape: '") in content
    
    async def test_codegen_multiple_edits(self, temp_python_file):
        """Test codegen making multiple edits to a file."""
        query = f"""Update the file {temp_python_file} with these changes:
        1. Add type hints to the calculate_area function (float parameters and return)
        2. Update calculate_volume to also have parameter validation"""
        
        agent = Codegen()
        result = await agent.run(query)
        
        with open(temp_python_file, 'r') as f:
            content = f.read()
        
        # Check for type hints
        assert ("-> float" in content) or ("float)" in content)
        # Check that calculate_volume still exists
        assert "def calculate_volume" in content
    
    async def test_codegen_complex_refactoring(self, temp_python_file):
        """Test a more complex refactoring task."""
        query = f"""Refactor the Shape class in {temp_python_file}:
        
        1. Add a method called area() that takes length and width parameters
        2. This method should call the module-level calculate_area function
        3. Add a proper docstring to the new method
        """
        
        agent = Codegen()
        result = await agent.run(query)
        
        with open(temp_python_file, 'r') as f:
            content = f.read()
        
        # Verify the Shape class now has an area method
        lines = content.split('\n')
        in_shape_class = False
        found_area_method = False
        
        for i, line in enumerate(lines):
            if "class Shape:" in line:
                in_shape_class = True
            elif line.strip() and not line.startswith(' ') and not line.startswith('\t'):
                in_shape_class = False
            elif in_shape_class and "def area" in line:
                found_area_method = True
                # Check that it calls calculate_area somewhere nearby
                for j in range(i, min(i+10, len(lines))):
                    if "calculate_area" in lines[j]:
                        break
        
        assert found_area_method, "Shape class should have an area method"
    
    async def test_codegen_preserves_functionality(self, temp_python_file):
        """Test that edits preserve existing functionality."""
        # First, let's read the original functionality
        with open(temp_python_file, 'r') as f:
            original_content = f.read()
        
        query = f"""In {temp_python_file}, update the get_name method in the Shape class to:
        1. Add a docstring
        2. Keep the exact same functionality (returning self.name)
        """
        
        agent = Codegen()
        result = await agent.run(query)
        
        with open(temp_python_file, 'r') as f:
            new_content = f.read()
        
        # Verify the method still exists and returns self.name
        assert "def get_name(self):" in new_content
        assert "return self.name" in new_content
        # Should have added a docstring
        assert ('"""' in new_content) or ("'''" in new_content)
    
    async def test_codegen_handles_string_not_found(self, temp_python_file):
        """Test that codegen handles cases where the string to edit is not found."""
        query = f"""Edit the file {temp_python_file} and update the non_existent_function to add error handling."""
        
        agent = Codegen()
        result = await agent.run(query)
        
        # The agent should handle this gracefully and explain the function doesn't exist
        assert result is not None
        assert ("not found" in result.lower()) or ("doesn't exist" in result.lower()) or ("could not find" in result.lower()) or ("unable to find" in result.lower())
    
    async def test_codegen_handles_ambiguous_edits(self, temp_python_file):
        """Test handling of ambiguous edit requests."""
        # First add duplicate code to create ambiguity
        with open(temp_python_file, 'a') as f:
            f.write('\n\ndef another_function():\n    """Calculate the area of a rectangle."""\n    pass\n')
        
        query = f"""Edit the file {temp_python_file} and update the docstring that says "Calculate the area of a rectangle." to be more specific."""
        
        agent = Codegen()
        result = await agent.run(query)
        
        # The agent should recognize the ambiguity
        assert result is not None
        # Should mention multiple occurrences or ask for clarification
        assert ("multiple" in result.lower()) or ("ambiguous" in result.lower()) or ("which" in result.lower()) or ("both" in result.lower())