import pytest
from unittest.mock import Mock, AsyncMock, patch
from agents.branch.graph import BranchGraph
from agents.scanner.states import ScanType
from agents.branch.prompts import get_scanner_query, get_coder_query
from langchain_core.messages import HumanMessage, AIMessage
from langchain_core.tools import BaseTool

pytestmark = pytest.mark.anyio


class MockTool(BaseTool):
    """Mock tool for testing"""
    name: str = "mock_tool"
    description: str = "A mock tool"
    
    def _run(self, *args, **kwargs):
        return "Mock tool response"
    
    async def _arun(self, *args, **kwargs):
        return "Mock tool response"


class TestBranchGraph:
    """Integration tests for the Branch Graph that combines scanner and coder."""
    
    @pytest.fixture
    def mock_llm(self):
        """Create a mock LLM that returns predictable responses."""
        llm = Mock()
        
        async def mock_ainvoke(messages):
            return AIMessage(content="Mock LLM response")
                
        llm.ainvoke = AsyncMock(side_effect=mock_ainvoke)
        llm.bind_tools = Mock(return_value=llm)
        
        return llm
    
    @pytest.fixture
    def mock_tools(self):
        """Create mock tools for the branch graph."""
        return [MockTool()]
    
    @pytest.fixture
    def mock_scanner_graph(self):
        """Create a mock scanner graph that returns predictable results."""
        mock_graph = Mock()
        
        async def mock_scanner_invoke(input_data, config):
            scan_type = input_data.get("scan_type")
            if scan_type == ScanType.SECURITY:
                analysis_result = """
## Security Analysis Results

**Score: 65/100**

**Critical Issues Found:**
1. SQL injection vulnerability in user.py:234
2. XSS vulnerability in templates/user_profile.html:45
3. Weak password validation in auth.py:123

**Recommendations:**
- Use parameterized queries for database operations
- Implement proper input sanitization
- Strengthen password requirements
                """
            else:
                analysis_result = """
## Comprehensive Code Analysis

**Overall Score: 72/100**

**Issues Found:**
1. Test coverage at 45% - missing tests in auth module
2. Performance bottleneck in data processing loop
3. Security vulnerability in user input handling

**Recommendations:**
- Add unit tests for authentication flows
- Optimize database queries
- Implement input validation
                """
            
            return {
                "messages": [AIMessage(content=analysis_result)],
                "scan_results": analysis_result
            }
        
        mock_graph.ainvoke = AsyncMock(side_effect=mock_scanner_invoke)
        return mock_graph
    
    @pytest.fixture
    def mock_coder_graph(self):
        """Create a mock coider graph that returns predictable results."""
        mock_graph = Mock()
        
        async def mock_coder_invoke(input_data, config):
            query = input_data.get("query", "")
            
            if "SQL injection" in query:
                coder_result = """
## Code Fixes Implemented

**Security Fixes:**
1. ✅ Fixed SQL injection in user.py:234 - replaced string concatenation with parameterized queries
2. ✅ Added input sanitization in templates/user_profile.html:45
3. ✅ Enhanced password validation in auth.py:123

**Pull Request Created:** #123 - Security vulnerability fixes
**Files Modified:** 3
**Tests Added:** 5 new security tests
                """
            else:
                coder_result = """
## Comprehensive Fixes Implemented

**Fixes Applied:**
1. ✅ Added 15 new unit tests - coverage increased to 85%
2. ✅ Optimized database queries - 40% performance improvement
3. ✅ Implemented input validation - security score improved

**Pull Request Created:** #124 - Comprehensive code improvements
**Files Modified:** 8
**Tests Added:** 15 new tests
                """
            
            return {
                "messages": [AIMessage(content=coder_result)]
            }
        
        mock_graph.ainvoke = AsyncMock(side_effect=mock_coder_invoke)
        return mock_graph
    
    @pytest.fixture
    def branch_graph(self, mock_llm, mock_tools, mock_scanner_graph, mock_coder_graph):
        """Create a branch graph instance with mocked subgraphs."""
        graph = BranchGraph(llm=mock_llm, tools=mock_tools)
        graph.scanner_graph = mock_scanner_graph
        graph.coder_graph = mock_coder_graph
        return graph
    
    async def test_scanner_subgraph_execution(self, branch_graph):
        """Test that scanner subgraph executes correctly and returns results."""
        state = {
            "scan_type": ScanType.SECURITY,
            "testing": False,
            "repo_id": "test-repo",
            "messages": []
        }
        
        # Execute scanner subgraph
        result = await branch_graph.scanner_subgraph(state, {})
        
        # Verify scanner was called with correct input
        branch_graph.scanner_graph.ainvoke.assert_called_once()
        call_args = branch_graph.scanner_graph.ainvoke.call_args[0][0]
        # Query is now auto-generated from scan_type
        assert "Scan for security vulnerabilities and issues" in call_args["query"]
        assert call_args["scan_type"] == ScanType.SECURITY
        assert call_args["repo_id"] == "test-repo"
        
        # Verify scan results are captured
        assert "scan_results" in result
        assert "SQL injection vulnerability" in result["scan_results"]
        assert "Security Analysis Results" in result["scan_results"]
        assert len(result["messages"]) == 1
        assert isinstance(result["messages"][0], HumanMessage)
    
    async def test_coder_subgraph_execution(self, branch_graph):
        """Test that coder subgraph executes with scanner results."""
        state = {
            "query": "Fix security issues",
            "scan_results": "SQL injection vulnerability in user.py:234",
            "repo_id": "test-repo",
            "db_issue_id": "issue-123",
            "messages": []
        }
        
        # Execute coder subgraph
        result = await branch_graph.coder_subgraph(state, {})
        
        # Verify coder was called with formatted query
        branch_graph.coder_graph.ainvoke.assert_called_once()
        call_args = branch_graph.coder_graph.ainvoke.call_args[0][0]
        
        # Check that query includes scan results
        coder_query = call_args["query"]
        assert "SQL injection vulnerability in user.py:234" in coder_query
        assert "implement fixes" in coder_query.lower()
        assert "pull request" in coder_query.lower()
        
        # Check other parameters
        assert call_args["repo_id"] == "test-repo"
        assert call_args["db_issue_id"] == "issue-123"
        
        # Verify coder results are returned
        assert "messages" in result
        assert len(result["messages"]) == 1
        assert isinstance(result["messages"][0], AIMessage)
        assert "Security Fixes" in result["messages"][0].content
    
    async def test_full_branch_graph_execution(self, mock_llm, mock_tools):
        """Test the complete branch graph flow: scanner -> coder."""
        with patch('agents.branch.graph.ScannerAgent') as mock_scanner_agent, \
             patch('agents.branch.graph.CoderAgent') as mock_coder_agent:
            
            # Setup mock scanner agent
            mock_scanner_instance = Mock()
            mock_scanner_graph = Mock()
            mock_scanner_graph.ainvoke = AsyncMock(return_value={
                "messages": [AIMessage(content="Security issues found in auth.py")],
                "scan_results": "Security analysis complete"
            })
            mock_scanner_instance.graph = mock_scanner_graph
            mock_scanner_agent.return_value = mock_scanner_instance
            
            # Setup mock coder agent
            mock_coder_instance = Mock()
            mock_coder_graph = Mock()
            mock_coder_graph.ainvoke = AsyncMock(return_value={
                "messages": [AIMessage(content="Fixes implemented, PR created #123")]
            })
            mock_coder_instance.graph = mock_coder_graph
            mock_coder_agent.return_value = mock_coder_instance
            
            # Create and compile branch graph
            branch_graph = BranchGraph(llm=mock_llm, tools=mock_tools)
            compiled_graph = branch_graph.compile()
            
            # Execute full graph
            result = await compiled_graph.ainvoke({
                "query": "Scan for security issues and fix them",
                "scan_type": ScanType.SECURITY,
                "repo_id": "test-repo"
            })
            
            # Verify both subgraphs were called
            mock_scanner_graph.ainvoke.assert_called_once()
            mock_coder_graph.ainvoke.assert_called_once()
            
            # Verify final result
            assert "messages" in result
            final_message = result["messages"][-1]
            assert isinstance(final_message, AIMessage)
            assert "Fixes implemented" in final_message.content
    
    async def test_branch_graph_stream_execution(self, mock_llm, mock_tools):
        """Test streaming through the branch graph."""
        with patch('agents.branch.graph.ScannerAgent') as mock_scanner_agent, \
             patch('agents.branch.graph.CoderAgent') as mock_coder_agent:
            
            # Setup mocks
            mock_scanner_instance = Mock()
            mock_scanner_graph = Mock()
            mock_scanner_graph.ainvoke = AsyncMock(return_value={
                "messages": [AIMessage(content="Test coverage at 45%")],
                "scan_results": "Test coverage analysis complete"
            })
            mock_scanner_instance.graph = mock_scanner_graph
            mock_scanner_agent.return_value = mock_scanner_instance
            
            mock_coder_instance = Mock()
            mock_coder_graph = Mock()
            mock_coder_graph.ainvoke = AsyncMock(return_value={
                "messages": [AIMessage(content="Added 15 new tests, PR #124 created")]
            })
            mock_coder_instance.graph = mock_coder_graph
            mock_coder_agent.return_value = mock_coder_instance
            
            # Create and compile branch graph
            branch_graph = BranchGraph(llm=mock_llm, tools=mock_tools)
            compiled_graph = branch_graph.compile()
            
            # Stream execution
            chunks = []
            async for chunk in compiled_graph.astream({
                "query": "Analyze test coverage and improve it",
                "scan_type": ScanType.TEST_COVERAGE,
                "repo_id": "test-repo"
            }):
                chunks.append(chunk)
            
            # Verify we got chunks for both scanner and coder
            assert len(chunks) >= 2
            
            # Find scanner and coder chunks
            scanner_chunk = None
            coder_chunk = None
            
            for chunk in chunks:
                if "scanner" in chunk:
                    scanner_chunk = chunk
                elif "coder" in chunk:
                    coder_chunk = chunk
            
            assert scanner_chunk is not None, "Scanner chunk not found"
            assert coder_chunk is not None, "Coder chunk not found"
            
            # Verify scanner results
            scanner_data = scanner_chunk["scanner"]
            assert "scan_results" in scanner_data
            assert "Test coverage at 45%" in scanner_data["scan_results"]
            
            # Verify coder results
            coder_data = coder_chunk["coder"]
            assert "messages" in coder_data
            coder_message = coder_data["messages"][-1]
            assert "Added 15 new tests" in coder_message.content
    
    async def test_different_scan_types_affect_coder_input(self, branch_graph):
        """Test that different scan types produce different coder inputs."""
        # Test security scan
        security_state = {
            "scan_results": "Security vulnerability: SQL injection in user.py",
            "repo_id": "test-repo"
        }
        
        await branch_graph.coder_subgraph(security_state, {})
        security_call = branch_graph.coder_graph.ainvoke.call_args[0][0]
        security_query = security_call["query"]
        
        # Reset mock
        branch_graph.coder_graph.ainvoke.reset_mock()
        
        # Test performance scan
        performance_state = {
            "scan_results": "Performance issue: Slow database queries in data.py",
            "repo_id": "test-repo"
        }
        
        await branch_graph.coder_subgraph(performance_state, {})
        performance_call = branch_graph.coder_graph.ainvoke.call_args[0][0]
        performance_query = performance_call["query"]
        
        # Verify different scan results produce different coder queries
        assert "SQL injection" in security_query
        assert "Slow database queries" in performance_query
        assert security_query != performance_query
        
        # Both should have instructions to fix and create PR
        assert "implement fixes" in security_query.lower()
        assert "implement fixes" in performance_query.lower()
        assert "pull request" in security_query.lower()
        assert "pull request" in performance_query.lower()

    def test_jinja2_templates(self):
        """Test that Jinja2 templates work correctly for different modes."""
        # Test scanner query template - normal mode
        normal_query = get_scanner_query("security", testing=False)
        assert "Scan for security vulnerabilities and issues" in normal_query
        assert "TESTING MODE" not in normal_query
        
        # Test scanner query template - testing mode
        testing_query = get_scanner_query("security", testing=True)
        assert "Scan for security vulnerabilities and issues" in testing_query
        assert "⚡ TESTING MODE - URGENT INSTRUCTIONS ⚡" in testing_query
        assert "SCAN ONLY ONE FILE" in testing_query
        assert "SPEED IS CRITICAL" in testing_query
        
        # Test coder query template - normal mode
        scan_results = "Found SQL injection in user.py:234"
        normal_coder = get_coder_query(scan_results, testing=False)
        assert scan_results in normal_coder
        assert "implement fixes and create a pull request" in normal_coder
        assert "TESTING MODE" not in normal_coder
        
        # Test coder query template - testing mode
        testing_coder = get_coder_query(scan_results, testing=True)
        assert scan_results in testing_coder
        assert "⚡ TESTING MODE - ULTRA FAST ⚡" in testing_coder
        assert "Make ONE small fix only" in testing_coder
        assert "SPEED OVER PERFECTION" in testing_coder