"""Debug test to understand the codegen graph flow."""

import pytest
from langchain_core.messages import HumanMessage, AIMessage, ToolMessage
from agents.heal_agent import codegen_graph
import json


def test_trace_graph_flow():
    """Trace the flow of messages through the graph."""
    graph = codegen_graph()
    
    # Simple query that should use a tool
    query = "Just run the command 'echo Hello World' and tell me the output."
    
    print("\n" + "="*80)
    print(f"Query: {query}")
    print("="*80)
    
    # Track all chunks
    chunks = []
    try:
        for chunk in graph.stream({
            "messages": [HumanMessage(content=query)]
        }, config={"recursion_limit": 10}):
            chunks.append(chunk)
            
            # Print what's happening
            if "reasoner" in chunk:
                print(f"\n[Chunk {len(chunks)}] REASONER:")
                messages = chunk["reasoner"].get("messages", [])
                for msg in messages:
                    if isinstance(msg, AIMessage):
                        if msg.content:
                            print(f"  Content: {msg.content[:100]}...")
                        if hasattr(msg, 'tool_calls') and msg.tool_calls:
                            print(f"  Tool calls: {[tc['name'] for tc in msg.tool_calls]}")
            
            elif "tools" in chunk:
                print(f"\n[Chunk {len(chunks)}] TOOLS:")
                messages = chunk["tools"].get("messages", [])
                for msg in messages:
                    if isinstance(msg, ToolMessage):
                        print(f"  Tool: {msg.name}")
                        print(f"  Output: {msg.content[:100]}...")
            
            else:
                print(f"\n[Chunk {len(chunks)}] OTHER: {list(chunk.keys())}")
    
    except Exception as e:
        print(f"\nError after {len(chunks)} chunks: {e}")
        
        # Print the last few messages to understand the state
        if chunks:
            last_chunk = chunks[-1]
            if "reasoner" in last_chunk:
                messages = last_chunk["reasoner"].get("messages", [])
                if messages:
                    print(f"\nLast message from reasoner:")
                    last_msg = messages[-1]
                    if isinstance(last_msg, AIMessage):
                        print(f"  Content: {last_msg.content}")
                        print(f"  Tool calls: {getattr(last_msg, 'tool_calls', None)}")
    
    print(f"\nTotal chunks processed: {len(chunks)}")
    
    # Analyze the pattern
    print("\nChunk sequence:")
    for i, chunk in enumerate(chunks):
        node = list(chunk.keys())[0]
        print(f"  {i+1}: {node}")


if __name__ == "__main__":
    test_trace_graph_flow()