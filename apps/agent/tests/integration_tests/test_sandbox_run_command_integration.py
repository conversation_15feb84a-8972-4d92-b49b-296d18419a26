"""
Integration tests for sandbox_run_command tool using real Daytona sandbox.
"""

import pytest
import os
from daytona import Daytona, CreateSandboxBaseParams
from utils.github_auth import GitHubInstallationManager
from agents.heal_agent.sandbox_tools import sandbox_run_command


class TestSandboxRunCommandIntegration:
    """Integration test for sandbox_run_command tool with real sandbox."""
    
    @pytest.fixture(scope="class")
    def real_sandbox(self):
        """Create a real Daytona sandbox for testing."""
        # Initialize Daytona sandbox
        installation_id = int(os.getenv("GITHUB_INSTALLATION_ID"))
        manager = GitHubInstallationManager()
        fresh_github_token = manager.get_installation_token(installation_id)

        params = CreateSandboxBaseParams(
            image="backspaceinc/backspace-org-backspace-mono:v20250530-130017",
            env_vars={
                "GITHUB_TOKEN": fresh_github_token,
                "GH_TOKEN": fresh_github_token,
            }
        )

        sandbox = Daytona.create(params=params)
        print(f"Created sandbox: {sandbox.id}")
        
        # Yield sandbox for tests
        yield sandbox
        
        # Cleanup: destroy the sandbox
        try:
            Daytona.remove(sandbox)
            print(f"Cleaned up sandbox: {sandbox.id}")
        except Exception as e:
            print(f"Warning: Failed to cleanup sandbox {sandbox.id}: {e}")
    
    @pytest.mark.asyncio
    async def test_echo_command_in_real_sandbox(self, real_sandbox):
        """Test echo command in real sandbox."""
        # Execute using ainvoke as it's a LangGraph tool
        result = await sandbox_run_command.ainvoke({
            "command": "echo 'Hello from real sandbox'", 
            "sandbox": real_sandbox
        })
        
        # Assert
        assert "Hello from real sandbox" in result
    
    @pytest.mark.asyncio
    async def test_ls_command_in_real_sandbox(self, real_sandbox):
        """Test ls command in real sandbox."""
        # Execute
        result = await sandbox_run_command.ainvoke({
            "command": "ls -la", 
            "sandbox": real_sandbox
        })
        
        # Assert - should show directory listing
        assert "total" in result or "." in result
    
    @pytest.mark.asyncio
    async def test_python_command_in_real_sandbox(self, real_sandbox):
        """Test Python command in real sandbox."""
        # Execute
        result = await sandbox_run_command.ainvoke({
            "command": "python -c 'print(\"Python works in sandbox\")'", 
            "sandbox": real_sandbox
        })
        
        # Assert
        assert "Python works in sandbox" in result
    
    @pytest.mark.asyncio
    async def test_git_command_in_real_sandbox(self, real_sandbox):
        """Test git command in real sandbox."""
        # Execute
        result = await sandbox_run_command.ainvoke({
            "command": "git --version", 
            "sandbox": real_sandbox
        })
        
        # Assert
        assert "git version" in result
    
    @pytest.mark.asyncio
    async def test_file_creation_in_real_sandbox(self, real_sandbox):
        """Test file creation and reading in real sandbox."""
        # Create a file
        create_result = await sandbox_run_command.ainvoke({
            "command": "echo 'test content' > test_file.txt", 
            "sandbox": real_sandbox
        })
        
        # Read the file
        read_result = await sandbox_run_command.ainvoke({
            "command": "cat test_file.txt", 
            "sandbox": real_sandbox
        })
        
        # Assert
        assert "test content" in read_result
        
        # Cleanup - remove the file
        await sandbox_run_command.ainvoke({
            "command": "rm test_file.txt", 
            "sandbox": real_sandbox
        })
    
    @pytest.mark.asyncio
    async def test_environment_variables_in_real_sandbox(self, real_sandbox):
        """Test that environment variables are set correctly in sandbox."""
        # Check if GitHub token is available
        result = await sandbox_run_command.ainvoke({
            "command": "echo $GITHUB_TOKEN | head -c 10", 
            "sandbox": real_sandbox
        })
        
        # Assert - should show first 10 characters of token
        assert len(result.strip()) == 10
    
    @pytest.mark.asyncio
    async def test_command_with_no_output(self, real_sandbox):
        """Test command that produces no output."""
        # Execute
        result = await sandbox_run_command.ainvoke({
            "command": "touch empty_test_file.txt", 
            "sandbox": real_sandbox
        })
        
        # Assert
        assert result == "Command executed successfully (no output)"
        
        # Cleanup
        await sandbox_run_command.ainvoke({
            "command": "rm empty_test_file.txt", 
            "sandbox": real_sandbox
        })
    
    @pytest.mark.asyncio
    async def test_command_execution_error(self, real_sandbox):
        """Test handling of command that fails."""
        # Execute a command that should fail
        result = await sandbox_run_command.ainvoke({
            "command": "cat nonexistent_file.txt", 
            "sandbox": real_sandbox
        })
        
        # Assert - should contain error message but not crash
        assert "No such file" in result or "cannot access" in result or result.startswith("Command execution failed:")
    
    @pytest.mark.asyncio
    async def test_complex_pipeline_command(self, real_sandbox):
        """Test complex shell pipeline in real sandbox."""
        # Execute
        result = await sandbox_run_command.ainvoke({
            "command": "echo -e 'line1\\nline2\\nline3' | grep line2", 
            "sandbox": real_sandbox
        })
        
        # Assert
        assert "line2" in result