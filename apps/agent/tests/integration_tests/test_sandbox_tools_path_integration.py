"""
Integration tests for sandbox tools path normalization.

These tests verify the full tool invocation with path normalization.
"""

import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from langchain_core.runnables.config import RunnableConfig

from agents.tools.sandbox_tools_state import sandbox_edit_file, sandbox_write_file
from utils.sandbox_manager import sandbox_manager

# Mark all async tests with anyio
pytestmark = pytest.mark.anyio


@pytest.fixture
def mock_sandbox():
    """Create a mock sandbox with filesystem operations."""
    sandbox = MagicMock()
    sandbox.fs = MagicMock()
    # Configure async mocks in each test as needed
    return sandbox


@pytest.fixture
def mock_config():
    """Create a mock RunnableConfig."""
    return RunnableConfig()


class TestSandboxEditFileIntegration:
    """Integration tests for sandbox_edit_file with path normalization."""

    async def test_edit_relative_path(self, mock_sandbox, mock_config):
        """Test editing a file with relative path."""
        # Set up mocks
        mock_sandbox.fs.download_file = MagicMock(return_value=b"old content")
        mock_sandbox.fs.replace_in_files = MagicMock(return_value=[
            MagicMock(success=True, error=None)
        ])
        
        with patch.object(sandbox_manager, 'get_sandbox', return_value=mock_sandbox):
            # Use ainvoke to properly invoke the tool
            result = await sandbox_edit_file.ainvoke({
                "file_path": "src/main.py",
                "old_str": "old content",
                "new_str": "new content",
                "sandbox_session_id": "test-session"
            }, config=mock_config)
        
        # Verify the path was normalized
        mock_sandbox.fs.download_file.assert_called_once_with("/workspace/src/main.py")
        assert "✅ Successfully replaced" in result

    async def test_edit_dot_slash_path(self, mock_sandbox, mock_config):
        """Test editing a file with ./ prefix."""
        # Set up mocks
        mock_sandbox.fs.download_file = MagicMock(return_value=b"old content")
        mock_sandbox.fs.replace_in_files = MagicMock(return_value=[
            MagicMock(success=True, error=None)
        ])
        
        with patch.object(sandbox_manager, 'get_sandbox', return_value=mock_sandbox):
            result = await sandbox_edit_file.ainvoke({
                "file_path": "./src/main.py",
                "old_str": "old content",
                "new_str": "new content",
                "sandbox_session_id": "test-session"
            }, config=mock_config)
        
        # Verify ./ was stripped and /workspace/ prepended
        mock_sandbox.fs.download_file.assert_called_once_with("/workspace/src/main.py")
        assert "✅ Successfully replaced" in result

    async def test_edit_workspace_path(self, mock_sandbox, mock_config):
        """Test editing a file already with /workspace/ prefix."""
        # Set up mocks
        mock_sandbox.fs.download_file = MagicMock(return_value=b"old content")
        mock_sandbox.fs.replace_in_files = MagicMock(return_value=[
            MagicMock(success=True, error=None)
        ])
        
        with patch.object(sandbox_manager, 'get_sandbox', return_value=mock_sandbox):
            result = await sandbox_edit_file.ainvoke({
                "file_path": "/workspace/src/main.py",
                "old_str": "old content",
                "new_str": "new content",
                "sandbox_session_id": "test-session"
            }, config=mock_config)
        
        # Verify /workspace/ was not duplicated
        mock_sandbox.fs.download_file.assert_called_once_with("/workspace/src/main.py")
        assert "✅ Successfully replaced" in result

    async def test_edit_file_not_found(self, mock_sandbox, mock_config):
        """Test error when file doesn't exist."""
        # Set up mock to raise exception
        mock_sandbox.fs.download_file = MagicMock(side_effect=Exception("File not found"))
        
        with patch.object(sandbox_manager, 'get_sandbox', return_value=mock_sandbox):
            result = await sandbox_edit_file.ainvoke({
                "file_path": "nonexistent.py",
                "old_str": "old content",
                "new_str": "new content",
                "sandbox_session_id": "test-session"
            }, config=mock_config)
        
        assert "❌ Error: File does not exist" in result
        assert "/workspace/nonexistent.py" in result


class TestSandboxWriteFileIntegration:
    """Integration tests for sandbox_write_file with path normalization."""

    async def test_write_relative_path(self, mock_sandbox, mock_config):
        """Test writing a file with relative path."""
        # Set up mocks
        mock_sandbox.fs.download_file = MagicMock(side_effect=Exception("Not found"))
        mock_sandbox.fs.upload_file = MagicMock(return_value=None)
        mock_sandbox.fs.create_folder = MagicMock(return_value=None)
        
        with patch.object(sandbox_manager, 'get_sandbox', return_value=mock_sandbox):
            result = await sandbox_write_file.ainvoke({
                "file_path": "src/new_file.py",
                "content": "print('hello')",
                "sandbox_session_id": "test-session"
            }, config=mock_config)
        
        # Verify the path was normalized
        mock_sandbox.fs.upload_file.assert_called_once()
        args = mock_sandbox.fs.upload_file.call_args[0]
        assert args[1] == "/workspace/src/new_file.py"
        assert "✅ Created file" in result

    async def test_write_dot_slash_path(self, mock_sandbox, mock_config):
        """Test writing a file with ./ prefix."""
        # Set up mocks
        mock_sandbox.fs.download_file = MagicMock(side_effect=Exception("Not found"))
        mock_sandbox.fs.upload_file = MagicMock(return_value=None)
        mock_sandbox.fs.create_folder = MagicMock(return_value=None)
        
        with patch.object(sandbox_manager, 'get_sandbox', return_value=mock_sandbox):
            result = await sandbox_write_file.ainvoke({
                "file_path": "./src/new_file.py",
                "content": "print('hello')",
                "sandbox_session_id": "test-session"
            }, config=mock_config)
        
        # Verify ./ was stripped and /workspace/ prepended
        mock_sandbox.fs.upload_file.assert_called_once()
        args = mock_sandbox.fs.upload_file.call_args[0]
        assert args[1] == "/workspace/src/new_file.py"
        assert "✅ Created file" in result

    async def test_overwrite_existing_file(self, mock_sandbox, mock_config):
        """Test overwriting an existing file."""
        # Set up mocks - file exists
        mock_sandbox.fs.download_file = MagicMock(return_value=b"old content")
        mock_sandbox.fs.upload_file = MagicMock(return_value=None)
        mock_sandbox.fs.create_folder = MagicMock(return_value=None)
        
        with patch.object(sandbox_manager, 'get_sandbox', return_value=mock_sandbox):
            result = await sandbox_write_file.ainvoke({
                "file_path": "existing.py",
                "content": "new content",
                "sandbox_session_id": "test-session"
            }, config=mock_config)
        
        assert "✅ Overwrote file" in result
        assert "/workspace/existing.py" in result