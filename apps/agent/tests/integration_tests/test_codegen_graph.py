import pytest
from langchain_core.messages import HumanMessage, AIMessage
from agents.heal_agent import codegen_graph
import json

pytestmark = pytest.mark.anyio


@pytest.mark.langsmith
async def test_codegen_graph_basic_query():
    """Test that the codegen graph responds to a basic query."""
    graph = codegen_graph()
    
    response = await graph.ainvoke({
        "messages": [HumanMessage(content="Hello, how are you?")]
    })
    
    assert response is not None
    assert "messages" in response
    assert len(response["messages"]) >= 2  # At least the human message and one AI response
    assert isinstance(response["messages"][-1], AIMessage)


@pytest.mark.langsmith
async def test_codegen_graph_tool_calling():
    """Test that the codegen graph properly calls tools when requested."""
    graph = codegen_graph()
    
    response = await graph.ainvoke({
        "messages": [HumanMessage(content="List the files in the current directory")]
    })
    
    assert response is not None
    assert "messages" in response
    # Should have at least: human message, AI message with tool call, tool response, final AI message
    assert len(response["messages"]) >= 2
    
    # Check if any message contains tool calls
    has_tool_calls = any(
        hasattr(msg, 'tool_calls') and msg.tool_calls 
        for msg in response["messages"] 
        if isinstance(msg, AIMessage)
    )
    assert has_tool_calls, "Expected at least one tool call for directory listing"


@pytest.mark.langsmith
async def test_codegen_graph_streaming():
    """Test that the codegen graph supports streaming."""
    graph = codegen_graph()
    
    chunks = []
    async for chunk in graph.astream({
        "messages": [HumanMessage(content="What tools do you have available?")]
    }):
        chunks.append(chunk)
    
    assert len(chunks) > 0
    # Should have chunks from reasoner node
    assert any("reasoner" in chunk for chunk in chunks)


@pytest.mark.langsmith
async def test_codegen_graph_streaming_events():
    """Test streaming with detailed events."""
    graph = codegen_graph()
    
    events = []
    async for event in graph.astream_events(
        {"messages": [HumanMessage(content="Can you search for Python files?")]},
        version="v2"
    ):
        events.append(event)
        # Print streaming output for debugging
        if event["event"] == "on_chat_model_stream":
            content = event["data"]["chunk"].content
            if content:
                print(content, end="", flush=True)
    
    assert len(events) > 0
    # Should have events from various stages
    event_types = {event["event"] for event in events}
    assert "on_chain_start" in event_types
    assert "on_chain_end" in event_types


@pytest.mark.langsmith
async def test_codegen_graph_sync():
    """Test async invocation of the codegen graph."""
    graph = codegen_graph()
    
    response = await graph.ainvoke({
        "messages": [HumanMessage(content="Hello from async test")]
    })
    
    assert response is not None
    assert "messages" in response
    assert len(response["messages"]) >= 2


@pytest.mark.langsmith
async def test_codegen_graph_stream_sync():
    """Test async streaming of the codegen graph."""
    graph = codegen_graph()
    
    chunks = []
    async for chunk in graph.astream({
        "messages": [HumanMessage(content="What can you help me with?")]
    }):
        chunks.append(chunk)
    
    assert len(chunks) > 0
    
    # Print formatted output from chunks
    for chunk in chunks:
        if "reasoner" in chunk and chunk["reasoner"]["messages"]:
            for msg in chunk["reasoner"]["messages"]:
                if hasattr(msg, "content") and msg.content:
                    print(f"\nReasoner: {msg.content}")
        elif "tools" in chunk and chunk["tools"]["messages"]:
            for msg in chunk["tools"]["messages"]:
                if hasattr(msg, "content") and msg.content:
                    print(f"\nTool: {msg.content}")