import pytest
from unittest.mock import Mock, AsyncMock, patch
from agents.scanner.graph import ScannerGraph
from agents.scanner.states import ScanType
from langchain_core.messages import HumanMessage, AIMessage
from langchain_core.tools import BaseTool

pytestmark = pytest.mark.anyio


class MockTool(BaseTool):
    """Mock tool for testing"""
    name: str = "mock_tool"
    description: str = "A mock tool"
    
    def _run(self, *args, **kwargs):
        return "Mock tool response"
    
    async def _arun(self, *args, **kwargs):
        return "Mock tool response"


class TestScannerGraph:
    """Integration tests for the Scanner Graph with dynamic prompts."""
    
    @pytest.fixture
    def mock_llm(self):
        """Create a mock LLM that returns predictable responses."""
        llm = Mock()
        
        # Mock the ainvoke method to return AI messages
        async def mock_ainvoke(messages):
            # Check if the system message contains metric-specific content
            system_msg = messages[0]["content"] if messages else ""
            
            # Also check user message for metric-specific content
            user_msg = ""
            if len(messages) > 1 and isinstance(messages[1], dict):
                user_msg = messages[1].get("content", "")
            elif len(messages) > 1 and hasattr(messages[1], 'content'):
                user_msg = messages[1].content
            
            if "SECURITY METRIC" in system_msg or "security analysis" in user_msg.lower():
                return AIMessage(content="Performing security-focused scan...")
            elif "TEST COVERAGE METRIC" in system_msg or "test coverage analysis" in user_msg.lower():
                return AIMessage(content="Performing test coverage-focused scan...")
            elif "THE 7 CRITICAL METRICS" in system_msg or "7 critical metrics" in user_msg.lower():
                return AIMessage(content="Performing comprehensive 7-metric scan...")
            else:
                return AIMessage(content="Performing scan...")
                
        llm.ainvoke = AsyncMock(side_effect=mock_ainvoke)
        
        # Mock bind_tools to return itself (used in BaseGraph.__init__)
        llm.bind_tools = Mock(return_value=llm)
        
        return llm
    
    @pytest.fixture
    def mock_tools(self):
        """Create mock tools for the scanner."""
        return [MockTool()]
    
    @pytest.fixture
    def scanner_graph(self, mock_llm, mock_tools):
        """Create a scanner graph instance with mocked dependencies."""
        return ScannerGraph(llm=mock_llm, tools=mock_tools)
    
    async def test_scanner_uses_security_specific_prompt(self, scanner_graph, mock_llm):
        """Test that scanner uses security-specific prompt when scan_type is SECURITY."""
        # Prepare initial state with security scan type
        state = {
            "messages": [],
            "query": "Scan for security vulnerabilities",
            "scan_type": ScanType.SECURITY,
            "repo_id": "test-repo"
        }
        
        # Execute the reasoner node
        result = await scanner_graph.reasoner(state)
        
        # Verify the LLM was called
        assert mock_llm.ainvoke.called
        
        # Check that the system message contains security-specific content
        call_args = mock_llm.ainvoke.call_args[0][0]
        system_message = call_args[0]["content"]
        
        # Verify it's using the security-specific prompt
        assert "SECURITY METRIC" in system_message
        assert "How secure is your codebase against vulnerabilities?" in system_message
        assert "SQL injection, XSS, auth issues" in system_message
        
        # Verify the response
        assert len(result["messages"]) == 2  # Human message + AI response
        assert isinstance(result["messages"][0], HumanMessage)
        assert isinstance(result["messages"][1], AIMessage)
        assert "security-focused scan" in result["messages"][1].content
    
    async def test_scanner_uses_test_coverage_specific_prompt(self, scanner_graph, mock_llm):
        """Test that scanner uses test coverage-specific prompt when scan_type is TEST_COVERAGE."""
        # Prepare initial state with test coverage scan type
        state = {
            "messages": [],
            "query": "Analyze test coverage",
            "scan_type": ScanType.TEST_COVERAGE,
            "repo_id": "test-repo"
        }
        
        # Execute the reasoner node
        result = await scanner_graph.reasoner(state)
        
        # Check that the system message contains test coverage-specific content
        call_args = mock_llm.ainvoke.call_args[0][0]
        system_message = call_args[0]["content"]
        
        # Verify it's using the test coverage-specific prompt
        assert "TEST COVERAGE METRIC" in system_message
        assert "How protected is the codebase against regressions?" in system_message
        assert "pytest --cov" in system_message
        
        # Verify the response
        assert "test coverage-focused scan" in result["messages"][1].content
    
    async def test_scanner_uses_comprehensive_prompt_by_default(self, scanner_graph, mock_llm):
        """Test that scanner uses comprehensive prompt when scan_type is None or COMPREHENSIVE."""
        # Test with None scan_type
        state = {
            "messages": [],
            "query": "Perform full analysis",
            "scan_type": None,
            "repo_id": "test-repo"
        }
        
        result = await scanner_graph.reasoner(state)
        
        # Check that the system message contains comprehensive content
        call_args = mock_llm.ainvoke.call_args[0][0]
        system_message = call_args[0]["content"]
        
        # Verify it's using the comprehensive prompt
        assert "THE 7 CRITICAL METRICS" in system_message
        assert "Test Coverage" in system_message
        assert "Security" in system_message
        assert "Performance" in system_message
        
        # Verify the response
        assert "comprehensive 7-metric scan" in result["messages"][1].content
    
    async def test_scanner_uses_comprehensive_prompt_for_comprehensive_type(self, scanner_graph, mock_llm):
        """Test that scanner uses comprehensive prompt when scan_type is COMPREHENSIVE."""
        state = {
            "messages": [],
            "query": "Perform full analysis",
            "scan_type": ScanType.COMPREHENSIVE,
            "repo_id": "test-repo"
        }
        
        await scanner_graph.reasoner(state)
        
        # Check that the system message contains comprehensive content
        call_args = mock_llm.ainvoke.call_args[0][0]
        system_message = call_args[0]["content"]
        
        # Verify it's using the comprehensive prompt
        assert "THE 7 CRITICAL METRICS" in system_message
        

    async def test_scanner_graph_stream_with_scan_type(self, mock_llm, mock_tools):
        """Test streaming through the graph with a specific scan type."""
        # Mock the sandbox manager
        with patch('agents.scanner.graph.sandbox_manager') as mock_sandbox_manager:
            mock_sandbox_manager.create_session = AsyncMock(return_value=("session-123", Mock()))
            mock_sandbox_manager.cleanup_session = AsyncMock(return_value=True)
            
            # Mock db_manager
            with patch('agents.scanner.graph.db_manager') as mock_db:
                mock_db.connect = AsyncMock()
                
                # Create and compile the graph
                graph = ScannerGraph(llm=mock_llm, tools=mock_tools)
                compiled_graph = graph.compile()
                
                # Stream with security scan type
                chunks = []
                async for chunk in compiled_graph.astream({
                    "query": "Scan for vulnerabilities",
                    "scan_type": ScanType.SECURITY,
                    "repo_id": "test-repo"
                }):
                    chunks.append(chunk)
                
                # Verify we got at least 2 chunks (start_sandbox and reasoner)
                assert len(chunks) >= 2
                
                # Find the reasoner chunk
                reasoner_chunk = None
                for chunk in chunks:
                    if "reasoner" in chunk:
                        reasoner_chunk = chunk
                        break
                
                assert reasoner_chunk is not None
                messages = reasoner_chunk["reasoner"].get("messages", [])
                assert len(messages) > 0
                
                # Verify the response indicates security scan
                ai_message = next((msg for msg in messages if isinstance(msg, AIMessage)), None)
                assert ai_message is not None
                assert "security-focused scan" in ai_message.content
                
                # Verify the user message is security-specific (not comprehensive)
                human_message = next((msg for msg in messages if isinstance(msg, HumanMessage)), None)
                assert human_message is not None
                user_content = human_message.content.lower()
                assert "security analysis" in user_content or "security" in user_content
                assert "7 critical metrics" not in user_content  # Should NOT mention all metrics
    

    async def test_all_scan_types_use_correct_prompts(self, scanner_graph, mock_llm):
        """Test that all scan types use their corresponding prompts."""
        scan_types_to_test = [
            (ScanType.TEST_COVERAGE, "TEST COVERAGE METRIC"),
            (ScanType.CLARITY, "CLARITY METRIC"),
            (ScanType.MODULARITY, "MODULARITY METRIC"),
            (ScanType.SECURITY, "SECURITY METRIC"),
            (ScanType.PERFORMANCE, "PERFORMANCE METRIC"),
            (ScanType.FAULT_RESILIENCE, "FAULT RESILIENCE METRIC"),
            (ScanType.AGENT_READINESS, "AGENT READINESS METRIC"),
        ]
        
        for scan_type, expected_text in scan_types_to_test:
            # Reset mock
            mock_llm.ainvoke.reset_mock()
            
            state = {
                "messages": [],
                "query": f"Analyze {scan_type.value}",
                "scan_type": scan_type,
                "repo_id": "test-repo"
            }
            
            await scanner_graph.reasoner(state)
            
            # Verify correct prompt was used
            call_args = mock_llm.ainvoke.call_args[0][0]
            system_message = call_args[0]["content"]
            assert expected_text in system_message, f"Expected '{expected_text}' in prompt for {scan_type}"