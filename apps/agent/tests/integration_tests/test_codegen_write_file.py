"""Integration test for codegen graph with write_file tool"""

import pytest
import tempfile
import os
from agents.heal_agent import Codegen

pytestmark = pytest.mark.anyio


class TestCodegenWriteFile:
    """Test the codegen graph's ability to use the write_file tool."""
    
    @pytest.fixture
    async def temp_dir(self):
        """Create a temporary directory for testing."""
        with tempfile.TemporaryDirectory() as tmpdir:
            yield tmpdir
    
    async def test_codegen_create_new_file(self, temp_dir):
        """Test codegen creating a new file."""
        file_path = os.path.join(temp_dir, "hello.py")
        
        query = f"""Create a new Python file at {file_path} with:
        1. A function called greet that takes a name parameter
        2. The function should return "Hello, {{name}}!"
        3. Add a docstring explaining what the function does
        4. Add a main block that tests the function
        """
        
        agent = Codegen()
        result = await agent.run(query)
        
        # Check that the file was created
        assert os.path.exists(file_path)
        
        with open(file_path, 'r') as f:
            content = f.read()
        
        # Verify the content includes expected elements
        assert "def greet" in content
        assert "return" in content
        assert '"""' in content or "'''" in content  # Has docstring
        assert "if __name__" in content  # Has main block
    
    async def test_codegen_create_config_file(self, temp_dir):
        """Test creating a configuration file."""
        file_path = os.path.join(temp_dir, "config.json")
        
        query = f"""Create a JSON configuration file at {file_path} with:
        - app_name: "MyApp"
        - version: "1.0.0"
        - debug: true
        - database settings with host: "localhost" and port: 5432
        """
        
        agent = Codegen()
        result = await agent.run(query)
        
        assert os.path.exists(file_path)
        
        # Verify it's valid JSON
        import json
        with open(file_path, 'r') as f:
            data = json.load(f)
            assert data["app_name"] == "MyApp"
            assert data["version"] == "1.0.0"
            assert data["debug"] is True
            assert "database" in data or "database_settings" in data
    
    async def test_codegen_create_readme(self, temp_dir):
        """Test creating a README file."""
        file_path = os.path.join(temp_dir, "README.md")
        
        query = f"""Create a README.md file at {file_path} for a Python project called "DataProcessor" that:
        - Has a title and description
        - Includes installation instructions
        - Has a usage example
        - Lists at least 2 features
        """
        
        agent = Codegen()
        result = await agent.run(query)
        
        assert os.path.exists(file_path)
        
        with open(file_path, 'r') as f:
            content = f.read()
        
        # Check for README elements
        assert "DataProcessor" in content
        assert "#" in content  # Has markdown headers
        assert "install" in content.lower()
        assert "usage" in content.lower() or "example" in content.lower()
    
    async def test_codegen_create_in_subdirectory(self, temp_dir):
        """Test creating a file in a subdirectory that doesn't exist."""
        file_path = os.path.join(temp_dir, "src", "utils", "helpers.py")
        
        query = f"""Create a Python file at {file_path} with:
        - A function called format_date that takes a datetime object
        - Returns a formatted string in "YYYY-MM-DD" format
        - Include proper imports
        """
        
        agent = Codegen()
        result = await agent.run(query)
        
        assert os.path.exists(file_path)
        
        with open(file_path, 'r') as f:
            content = f.read()
        
        assert "def format_date" in content
        assert "import" in content
        assert "datetime" in content
    
    async def test_codegen_overwrite_file(self, temp_dir):
        """Test overwriting an existing file."""
        file_path = os.path.join(temp_dir, "existing.txt")
        
        # Create initial file
        with open(file_path, 'w') as f:
            f.write("Old content that should be replaced")
        
        query = f"""Overwrite the file at {file_path} with a new Python script that:
        - Implements a simple calculator class
        - Has methods for add, subtract, multiply, and divide
        - Includes error handling for division by zero
        """
        
        agent = Codegen()
        result = await agent.run(query)
        
        with open(file_path, 'r') as f:
            content = f.read()
        
        # Old content should be gone
        assert "Old content" not in content
        # New content should be there
        assert "class" in content
        assert "def add" in content or "def divide" in content
        assert "zero" in content.lower()  # Division by zero handling
    
    async def test_codegen_handles_write_errors_gracefully(self):
        """Test that codegen handles write errors appropriately."""
        # Try to write to a path that will definitely fail
        query = """Create a new file at /root/system/critical.conf with some configuration data."""
        
        agent = Codegen()
        result = await agent.run(query)
        
        # Should handle the error gracefully
        assert result is not None
        # Should mention the error in the response
        assert ("permission" in result.lower()) or ("error" in result.lower()) or ("cannot" in result.lower())
    
    async def test_codegen_complex_file_creation(self, temp_dir):
        """Test creating a more complex file with multiple components."""
        file_path = os.path.join(temp_dir, "data_processor.py")
        
        query = f"""Create a Python file at {file_path} that:
        1. Imports pandas and numpy (use import statements even if not installed)
        2. Has a DataProcessor class with:
           - An __init__ method that takes a data_path parameter
           - A load_data method that returns a placeholder message
           - A process_data method with a docstring
           - A save_results method that takes an output_path parameter
        3. Add type hints where appropriate
        4. Include a main block that demonstrates usage
        """
        
        agent = Codegen()
        result = await agent.run(query)
        
        assert os.path.exists(file_path)
        
        with open(file_path, 'r') as f:
            content = f.read()
        
        # Check for all required components
        assert "import pandas" in content
        assert "import numpy" in content
        assert "class DataProcessor" in content
        assert "def __init__" in content
        assert "def load_data" in content
        assert "def process_data" in content
        assert "def save_results" in content
        assert "if __name__" in content
        # Should have some type hints
        assert "->" in content or ": str" in content or ": Path" in content