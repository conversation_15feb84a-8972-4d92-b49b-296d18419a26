"""
Integration tests for the full Codegen workflow with real sandbox.
Tests the complete LangGraph execution from Codegen class through sandbox tools.
"""

import pytest
import os
from agents.heal_agent.heal_agent import Codegen


class TestCodegenSandboxIntegration:
    """Integration test for full Codegen workflow with real sandbox."""
    
    @pytest.fixture(scope="class")
    def codegen_instance(self):
        """Create a Codegen instance with real sandbox."""
        # This will create a real sandbox via the Codegen class
        codegen = Codegen()
        
        print(f"Created Codegen with sandbox: {codegen.sandbox.id}")
        
        # Yield for tests
        yield codegen
        
        # Cleanup: The sandbox should be cleaned up when Codegen is destroyed
        # or we can explicitly clean it up
        try:
            from daytona import Daytona
            Daytona.remove(codegen.sandbox)
            print(f"Cleaned up sandbox: {codegen.sandbox.id}")
        except Exception as e:
            print(f"Warning: Failed to cleanup sandbox {codegen.sandbox.id}: {e}")
    
    @pytest.mark.asyncio
    async def test_simple_command_through_langgraph(self, codegen_instance):
        """Test running a simple command through the full LangGraph."""
        # Execute a query that should use sandbox_run_command
        result = await codegen_instance.run("Run the command: echo 'Hello from LangGraph'")
        
        # Assert
        assert "Hello from LangGraph" in result
    
    @pytest.mark.asyncio
    async def test_file_operations_through_langgraph(self, codegen_instance):
        """Test file operations through the full LangGraph."""
        # Create a file and read it back
        result = await codegen_instance.run(
            "Create a file called 'test.txt' with content 'Integration test content', "
            "then read it back to verify it was created correctly."
        )
        
        # Assert
        assert "Integration test content" in result or "test.txt" in result
    
    @pytest.mark.asyncio
    async def test_directory_listing_through_langgraph(self, codegen_instance):
        """Test directory listing through the full LangGraph."""
        # List current directory
        result = await codegen_instance.run("List the contents of the current directory")
        
        # Assert - should contain typical directory listing elements
        assert any(indicator in result.lower() for indicator in ["total", "drwx", ".", "directory", "files"])
    
    @pytest.mark.asyncio
    async def test_python_execution_through_langgraph(self, codegen_instance):
        """Test Python code execution through the full LangGraph."""
        # Run Python code
        result = await codegen_instance.run(
            "Execute this Python code: print('Python works in integration test')"
        )
        
        # Assert
        assert "Python works in integration test" in result
    
    @pytest.mark.asyncio
    async def test_git_operations_through_langgraph(self, codegen_instance):
        """Test git operations through the full LangGraph."""
        # Check git version
        result = await codegen_instance.run("Check the git version")
        
        # Assert
        assert "git version" in result.lower() or "git" in result.lower()
    
    @pytest.mark.asyncio
    async def test_complex_workflow_through_langgraph(self, codegen_instance):
        """Test a complex multi-step workflow through LangGraph."""
        # Complex workflow: create file, modify it, search for content
        result = await codegen_instance.run(
            "Do the following steps: "
            "1. Create a file called 'workflow_test.txt' with content 'Step 1 complete' "
            "2. Append 'Step 2 complete' to the file "
            "3. Search for the word 'complete' in the file "
            "4. Show the final file contents"
        )
        
        # Assert
        assert ("Step 1 complete" in result and "Step 2 complete" in result) or "complete" in result
    
    @pytest.mark.asyncio
    async def test_error_handling_through_langgraph(self, codegen_instance):
        """Test error handling through the full LangGraph."""
        # Try to read a non-existent file
        result = await codegen_instance.run("Read the contents of a file named 'definitely_does_not_exist.txt'")
        
        # Assert - should handle the error gracefully
        assert any(error_indicator in result.lower() for error_indicator in [
            "no such file", "cannot access", "not found", "does not exist", "error"
        ])
    
    @pytest.mark.asyncio
    async def test_environment_variables_through_langgraph(self, codegen_instance):
        """Test that environment variables are accessible through LangGraph."""
        # Check environment variables
        result = await codegen_instance.run("Check if the GITHUB_TOKEN environment variable is set (show first 10 characters)")
        
        # Assert - should show some token characters or confirm it exists
        assert len([char for char in result if char.isalnum()]) >= 5  # Should have some alphanumeric content
    
    @pytest.mark.asyncio
    async def test_sandbox_isolation_through_langgraph(self, codegen_instance):
        """Test that the sandbox provides proper isolation."""
        # Create a file in one query
        await codegen_instance.run("Create a file called 'isolation_test.txt' with content 'isolated'")
        
        # Verify it exists in another query (same sandbox session)
        result = await codegen_instance.run("Check if 'isolation_test.txt' exists and show its content")
        
        # Assert
        assert "isolated" in result or "isolation_test.txt" in result