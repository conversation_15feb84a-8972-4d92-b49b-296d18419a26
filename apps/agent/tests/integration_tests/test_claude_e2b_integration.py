"""Integration tests for Claude E2B sandbox - requires real E2B account and API key."""

import pytest
import os
import asyncio
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Skip all tests if E2B_API_KEY is not available
pytestmark = [
    pytest.mark.anyio,
    pytest.mark.skipif(
        not os.getenv("E2B_API_KEY"),
        reason="E2B_API_KEY not set - skipping integration tests"
    )
]

# Set default repo ID for tests that don't specify one
if not os.getenv("DEFAULT_REPO_ID"):
    os.environ["DEFAULT_REPO_ID"] = "*********"

from agents.claude_e2b import (
    create_sandbox,
    cleanup_sandbox,
    sandbox_context,
    run_command_in_sandbox,
    run_claude_in_sandbox,
    ClaudeSession
)
from db import db_manager


class TestE2BSandboxIntegration:
    """Integration tests for E2B sandbox functionality."""
    
    async def test_create_and_cleanup_sandbox(self):
        """Test basic sandbox creation and cleanup."""
        # Initialize database connection for this test
        try:
            await db_manager.connect()
        except Exception as e:
            pytest.skip(f"Database connection failed: {e}")
        
        # This tests the full lifecycle
        sandbox = await create_sandbox()
        
        try:
            # Verify sandbox is created
            assert sandbox is not None
            # E2B sandbox should have commands attribute for running commands
            assert hasattr(sandbox, 'commands')
            assert hasattr(sandbox, 'sandbox_id')
            
            # Test basic command execution
            result = await run_command_in_sandbox(sandbox, "echo 'Hello E2B'")
            assert result["exit_code"] == 0
            assert "Hello E2B" in result["stdout"]
            
        finally:
            # Always cleanup
            await cleanup_sandbox(sandbox)
    
    async def test_sandbox_context_manager(self):
        """Test the context manager for automatic cleanup."""
        sandbox_id = None
        
        async with sandbox_context() as sandbox:
            sandbox_id = sandbox.sandbox_id
            
            # Test that we can run commands
            result = await run_command_in_sandbox(sandbox, "pwd")
            assert result["exit_code"] == 0
            assert result["stdout"].strip()  # Should have some output
            
            # Test workspace creation
            result = await run_command_in_sandbox(
                sandbox, 
                "ls -la $HOME/workspace || echo 'workspace not found'"
            )
            assert result["exit_code"] == 0
        
        # After context, sandbox should be cleaned up
        # We can't test this directly, but the context manager should have called cleanup
        assert sandbox_id is not None
    
    async def test_claude_code_availability(self):
        """Test that Claude Code CLI is available in the sandbox."""
        async with sandbox_context() as sandbox:
            # Check Claude Code version
            result = await run_command_in_sandbox(sandbox, "claude --version")
            assert result["exit_code"] == 0
            assert "claude" in result["stdout"].lower() or "anthropic" in result["stdout"].lower()
            
            # Check that npm installed it globally
            result = await run_command_in_sandbox(sandbox, "which claude")
            assert result["exit_code"] == 0
            assert result["stdout"].strip()  # Should have a path
    
    async def test_workspace_setup(self):
        """Test that workspace is properly set up."""
        async with sandbox_context() as sandbox:
            # Check workspace directory exists
            result = await run_command_in_sandbox(sandbox, "ls -la $HOME/workspace")
            assert result["exit_code"] == 0
            
            # Check workspace path environment variable
            result = await run_command_in_sandbox(sandbox, "echo $WORKSPACE_PATH")
            assert result["exit_code"] == 0
            assert "/home/<USER>/workspace" in result["stdout"]
    
    async def test_environment_variables(self):
        """Test that required environment variables are set."""
        async with sandbox_context() as sandbox:
            # Check ANTHROPIC_API_KEY is set (but don't print it)
            result = await run_command_in_sandbox(
                sandbox, 
                "[ -n \"$ANTHROPIC_API_KEY\" ] && echo 'ANTHROPIC_API_KEY is set' || echo 'ANTHROPIC_API_KEY is not set'"
            )
            assert result["exit_code"] == 0
            assert "ANTHROPIC_API_KEY is set" in result["stdout"]
            
            # If GitHub token is available, check it too
            if os.getenv("GH_TOKEN") or os.getenv("GITHUB_TOKEN"):
                result = await run_command_in_sandbox(
                    sandbox,
                    "[ -n \"$GH_TOKEN\" ] && echo 'GH_TOKEN is set' || echo 'GH_TOKEN is not set'"
                )
                assert result["exit_code"] == 0
                assert "GH_TOKEN is set" in result["stdout"]
    
    async def test_multiple_commands(self):
        """Test running multiple commands in sequence."""
        async with sandbox_context() as sandbox:
            commands = [
                ("mkdir -p /tmp/test", "Create test directory"),
                ("echo 'test content' > /tmp/test/file.txt", "Create test file"),
                ("cat /tmp/test/file.txt", "Read test file"),
                ("rm -rf /tmp/test", "Cleanup test directory")
            ]
            
            for cmd, description in commands:
                result = await run_command_in_sandbox(sandbox, cmd)
                if "cat" in cmd:
                    # For the read command, check content
                    assert result["exit_code"] == 0
                    assert "test content" in result["stdout"]
                else:
                    # For other commands, just check they succeeded
                    assert result["exit_code"] == 0, f"Failed: {description}"
    
    async def test_git_configuration(self):
        """Test that git is properly configured if GitHub token is available."""
        if not (os.getenv("GH_TOKEN") or os.getenv("GITHUB_TOKEN")):
            pytest.skip("No GitHub token available")
        
        async with sandbox_context() as sandbox:
            # Check git user configuration
            result = await run_command_in_sandbox(sandbox, "git config --global user.name")
            assert result["exit_code"] == 0
            assert "Backspace Agent" in result["stdout"]
            
            result = await run_command_in_sandbox(sandbox, "git config --global user.email")
            assert result["exit_code"] == 0
            assert "<EMAIL>" in result["stdout"]
    
    async def test_custom_environment_variables(self):
        """Test creating sandbox with custom environment variables."""
        custom_envs = {
            "TEST_VAR": "test_value",
            "DEBUG_MODE": "true"
        }
        
        async with sandbox_context(envs=custom_envs) as sandbox:
            # Check custom environment variables are set
            result = await run_command_in_sandbox(sandbox, "echo $TEST_VAR")
            assert result["exit_code"] == 0
            assert "test_value" in result["stdout"]
            
            result = await run_command_in_sandbox(sandbox, "echo $DEBUG_MODE")
            assert result["exit_code"] == 0
            assert "true" in result["stdout"]
    
    async def test_repository_cloning(self):
        """Test that repository is properly cloned into workspace."""
        # Initialize database connection for this test
        try:
            await db_manager.connect()
        except Exception as e:
            pytest.skip(f"Database connection failed: {e}")
        
        async with sandbox_context() as sandbox:
            # Check that workspace directory contains repository files
            result = await run_command_in_sandbox(sandbox, "cd /home/<USER>/workspace && ls -la")
            assert result["exit_code"] == 0
            
            # Check for .git directory (indicates successful git clone)
            result = await run_command_in_sandbox(sandbox, "cd /home/<USER>/workspace && test -d .git && echo 'Git repository found' || echo 'No git repository'")
            assert result["exit_code"] == 0
            assert "Git repository found" in result["stdout"], f"Repository not cloned properly: {result['stdout']}"
            
            # Check git configuration
            result = await run_command_in_sandbox(sandbox, "cd /home/<USER>/workspace && git config user.name")
            assert result["exit_code"] == 0
            assert "Backspace Agent" in result["stdout"]
            
            result = await run_command_in_sandbox(sandbox, "cd /home/<USER>/workspace && git config user.email")
            assert result["exit_code"] == 0
            assert "<EMAIL>" in result["stdout"]
            
            # Check that we can see some typical repository files (README, package files, etc.)
            # This is a more flexible check since different repos have different structures
            result = await run_command_in_sandbox(sandbox, "cd /home/<USER>/workspace && find . -maxdepth 2 -type f | head -10")
            assert result["exit_code"] == 0
            assert result["stdout"].strip(), "Workspace should contain some files from the cloned repository"
    
    @pytest.mark.slow
    async def test_timeout_handling(self):
        """Test that command timeouts are handled properly."""
        async with sandbox_context() as sandbox:
            # Test a command that should timeout
            with pytest.raises(Exception):  # Should raise timeout exception
                await run_command_in_sandbox(
                    sandbox, 
                    "sleep 10",  # Sleep for 10 seconds
                    timeout=2    # But timeout after 2 seconds
                )


@pytest.mark.slow
class TestClaudeE2BIntegration:
    """Integration tests for Claude Code in E2B sandbox."""
    
    async def test_simple_claude_execution(self):
        """Test running a simple Claude command in the sandbox."""
        # Skip if no Anthropic API key
        if not os.getenv("ANTHROPIC_API_KEY"):
            pytest.skip("ANTHROPIC_API_KEY not set")
        
        # Initialize database connection for this test
        try:
            await db_manager.connect()
        except Exception as e:
            pytest.skip(f"Database connection failed: {e}")
        
        async with sandbox_context() as sandbox:
            # Run a very simple Claude command
            session = await run_claude_in_sandbox(
                sandbox,
                "Just say hello and nothing else",
                claude_options={"max-turns": "1"},
                timeout=60
            )
            
            assert isinstance(session, ClaudeSession)
            assert session.prompt == "Just say hello and nothing else"
            assert len(session.outputs) > 0
            
            # Should have some kind of response
            claude_messages = [o for o in session.outputs if o.type == "claude_message"]
            assert len(claude_messages) > 0
    
    async def test_claude_with_file_operations(self):
        """Test Claude performing file operations in the sandbox."""
        if not os.getenv("ANTHROPIC_API_KEY"):
            pytest.skip("ANTHROPIC_API_KEY not set")
        
        async with sandbox_context() as sandbox:
            session = await run_claude_in_sandbox(
                sandbox,
                "Create a simple Python script that prints 'Hello from E2B' and run it",
                claude_options={"max-turns": "3"},
                timeout=120
            )
            
            assert session.success, f"Session failed: {session.error}"
            
            # Should have tool calls for file operations
            tool_calls = [o for o in session.outputs if o.type == "tool_call"]
            assert len(tool_calls) > 0
            
            # Should have at least a Write tool call
            write_calls = [o for o in tool_calls if o.content.get("name") == "Write"]
            assert len(write_calls) > 0
    
    async def test_claude_session_tracking(self):
        """Test that Claude sessions properly track outputs and metadata."""
        if not os.getenv("ANTHROPIC_API_KEY"):
            pytest.skip("ANTHROPIC_API_KEY not set")
        
        async with sandbox_context() as sandbox:
            session = await run_claude_in_sandbox(
                sandbox,
                "What is 2 + 2?",
                claude_options={"max-turns": "1"},
                timeout=60
            )
            
            # Check session metadata
            assert session.session_id
            assert session.prompt == "What is 2 + 2?"
            assert session.start_time > 0
            assert session.end_time is not None
            assert session.elapsed_time > 0
            
            # Check that we got some outputs
            assert len(session.outputs) > 0
            
            # Should have a final result
            result_outputs = [o for o in session.outputs if o.type == "result"]
            assert len(result_outputs) > 0
    
    async def test_multiple_claude_sessions(self):
        """Test running multiple Claude sessions in the same sandbox."""
        if not os.getenv("ANTHROPIC_API_KEY"):
            pytest.skip("ANTHROPIC_API_KEY not set")
        
        async with sandbox_context() as sandbox:
            # First session
            session1 = await run_claude_in_sandbox(
                sandbox,
                "Create a file called test1.txt with content 'First session'",
                timeout=60
            )
            
            # Second session
            session2 = await run_claude_in_sandbox(
                sandbox,
                "Create a file called test2.txt with content 'Second session'",
                timeout=60
            )
            
            # Both should succeed
            assert session1.success
            assert session2.success
            
            # Verify files were created by checking the sandbox
            result = await run_command_in_sandbox(sandbox, "ls -la test*.txt")
            assert result["exit_code"] == 0
            assert "test1.txt" in result["stdout"]
            assert "test2.txt" in result["stdout"]


if __name__ == "__main__":
    # Allow running this file directly for development/debugging
    import sys
    
    if not os.getenv("E2B_API_KEY"):
        print("❌ E2B_API_KEY not set. Set it to run integration tests.")
        sys.exit(1)
    
    if not os.getenv("ANTHROPIC_API_KEY"):
        print("❌ ANTHROPIC_API_KEY not set. Set it to run Claude integration tests.")
        sys.exit(1)
    
    print("🚀 Running integration tests...")
    
    # Run a simple test
    async def quick_test():
        async with sandbox_context() as sandbox:
            result = await run_command_in_sandbox(sandbox, "echo 'Integration test works!'")
            print(f"✅ Test result: {result['stdout'].strip()}")
    
    asyncio.run(quick_test())
    print("✅ Integration tests setup looks good!")