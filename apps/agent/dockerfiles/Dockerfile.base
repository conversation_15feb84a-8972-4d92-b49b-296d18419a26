# Base image with all tools
# Build for AMD64 platform (required for Daytona)
FROM --platform=linux/amd64 python:3.12-slim

# Install system dependencies
RUN apt-get update && apt-get install -y \
    curl \
    git \
    build-essential \
    unzip \
    wget \
    unzip \
    gnupg \
    software-properties-common \
    && rm -rf /var/lib/apt/lists/*

# Install ripgrep
RUN curl -LO https://github.com/BurntSushi/ripgrep/releases/download/14.1.0/ripgrep_14.1.0-1_amd64.deb \
    && dpkg -i ripgrep_14.1.0-1_amd64.deb \
    && rm ripgrep_14.1.0-1_amd64.deb

# Install Node.js 20 (LTS) and npm
RUN curl -fsSL https://deb.nodesource.com/setup_20.x | bash - \
    && apt-get install -y nodejs \
    && rm -rf /var/lib/apt/lists/*

# Install pnpm
RUN npm install -g pnpm

# Install Python security and quality tools
RUN pip install --no-cache-dir \
    bandit \
    safety \
    flake8 \
    pylint \
    mypy \
    black \
    pytest \
    pytest-cov \
    coverage

# Install Semgrep
RUN pip install --no-cache-dir semgrep

# Install JavaScript/TypeScript tools
RUN npm install -g \
    eslint \
    prettier \
    jest \
    mocha \
    @typescript-eslint/parser \
    @typescript-eslint/eslint-plugin

# Install Snyk CLI
RUN npm install -g snyk

# Install SonarScanner
RUN wget https://binaries.sonarsource.com/Distribution/sonar-scanner-cli/sonar-scanner-cli-4.8.0.2856-linux.zip \
    && unzip sonar-scanner-cli-4.8.0.2856-linux.zip \
    && mv sonar-scanner-4.8.0.2856-linux /opt/sonar-scanner \
    && rm sonar-scanner-cli-4.8.0.2856-linux.zip \
    && ln -s /opt/sonar-scanner/bin/sonar-scanner /usr/local/bin/sonar-scanner

# Install uv globally
RUN curl -LsSf https://astral.sh/uv/install.sh | sh \
    && cp /root/.local/bin/uv /usr/local/bin/uv || cp /root/.cargo/bin/uv /usr/local/bin/uv

# Install GitHub CLI and sudo
RUN curl -fsSL https://cli.github.com/packages/githubcli-archive-keyring.gpg | dd of=/usr/share/keyrings/githubcli-archive-keyring.gpg \
    && chmod go+r /usr/share/keyrings/githubcli-archive-keyring.gpg \
    && echo "deb [arch=$(dpkg --print-architecture) signed-by=/usr/share/keyrings/githubcli-archive-keyring.gpg] https://cli.github.com/packages stable main" | tee /etc/apt/sources.list.d/github-cli.list > /dev/null \
    && apt-get update \
    && apt-get install -y gh sudo \
    && rm -rf /var/lib/apt/lists/*

# Install Claude Code globally for all users
RUN npm install -g @anthropic-ai/claude-code \
    && ln -s /usr/lib/node_modules/@anthropic-ai/claude-code/cli.js /usr/local/bin/claude-code

# Create non-root user to avoid --dangerously-skip-permissions
RUN useradd -m -s /bin/bash daytona \
    && echo 'daytona ALL=(ALL) NOPASSWD:ALL' > /etc/sudoers.d/daytona \
    && chmod 0440 /etc/sudoers.d/daytona

# Switch to non-root user
USER daytona
WORKDIR /home/<USER>