# Base image with all tools
FROM e2bdev/code-interpreter:latest 

# Install system dependencies
RUN apt-get update && apt-get install -y \
    curl \
    git \
    build-essential \
    unzip \
    wget \
    gnupg \
    software-properties-common \
    ripgrep \
    && rm -rf /var/lib/apt/lists/*

# Install Node.js 20 (LTS) and npm
RUN curl -fsSL https://deb.nodesource.com/setup_20.x | bash - \
    && apt-get install -y nodejs \
    && rm -rf /var/lib/apt/lists/*

# Install pnpm
RUN npm install -g pnpm

# Install Python security and quality tools
RUN pip install --no-cache-dir \
    bandit \
    safety \
    flake8 \
    pylint \
    mypy \
    black \
    pytest \
    pytest-cov \
    coverage

# Install Semgrep
RUN pip install --no-cache-dir semgrep

RUN pip install claude-code-sdk

# Install JavaScript/TypeScript tools
RUN npm install -g \
    eslint \
    prettier \
    jest \
    mocha \
    @typescript-eslint/parser \
    @typescript-eslint/eslint-plugin

# Install Claude Code CLI
RUN npm install -g @anthropic-ai/claude-code

# Install Snyk CLI
RUN npm install -g snyk

# Install SonarScanner
RUN wget https://binaries.sonarsource.com/Distribution/sonar-scanner-cli/sonar-scanner-cli-4.8.0.2856-linux.zip \
    && unzip sonar-scanner-cli-4.8.0.2856-linux.zip \
    && mv sonar-scanner-4.8.0.2856-linux /opt/sonar-scanner \
    && rm sonar-scanner-cli-4.8.0.2856-linux.zip \
    && ln -s /opt/sonar-scanner/bin/sonar-scanner /usr/local/bin/sonar-scanner

# Install uv
RUN curl -LsSf https://astral.sh/uv/install.sh | sh
ENV PATH="/root/.cargo/bin:$PATH"

# Install GitHub CLI
RUN curl -fsSL https://cli.github.com/packages/githubcli-archive-keyring.gpg | dd of=/usr/share/keyrings/githubcli-archive-keyring.gpg \
    && chmod go+r /usr/share/keyrings/githubcli-archive-keyring.gpg \
    && echo "deb [arch=$(dpkg --print-architecture) signed-by=/usr/share/keyrings/githubcli-archive-keyring.gpg] https://cli.github.com/packages stable main" | tee /etc/apt/sources.list.d/github-cli.list > /dev/null \
    && apt-get update \
    && apt-get install -y gh \
    && rm -rf /var/lib/apt/lists/*

WORKDIR /workspace