# This is a config for E2B sandbox template.
# You can use template ID (vcomjjr43nxwhfxodbqm) to create a sandbox:

# Python SDK
# from e2b import Sandbox, AsyncSandbox
# sandbox = Sandbox("vcomjjr43nxwhfxodbqm") # Sync sandbox
# sandbox = await AsyncSandbox.create("vcomjjr43nxwhfxodbqm") # Async sandbox

# JS SDK
# import { Sandbox } from 'e2b'
# const sandbox = await Sandbox.create('vcomjjr43nxwhfxodbqm')

team_id = "4e681f7b-f364-44d2-819d-b4f494ba5893"
start_cmd = "/root/.jupyter/start-up.sh"
dockerfile = "e2b.Dockerfile"
template_id = "vcomjjr43nxwhfxodbqm"
