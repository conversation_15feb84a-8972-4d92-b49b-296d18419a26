#!/usr/bin/env python3
"""
AI Repository Analyzer - Analyze GitHub repositories using OpenAI.

This script fetches a GitHub repository's content and analyzes it using OpenAI's API
to provide comprehensive insights about the codebase.

Usage:
    python ai-repo-analyzer.py https://github.com/username/repo

Requirements:
    - GITHUB_TOKEN environment variable set
    - OPENAI_API_KEY environment variable set
"""

import os
import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add deep-scan to path
sys.path.insert(0, 'deep-scan')

from deep_scan.repo import <PERSON>o

def analyze_repo_with_ai(repo_url: str, provider: str = "openai"):
    """Analyze a GitHub repository using AI."""
    
    print("🤖 AI Repository Analysis")
    print("=" * 60)
    
    # Check required environment variables
    github_token = os.getenv("GITHUB_TOKEN")
    
    if not github_token:
        print("❌ Error: GITHUB_TOKEN environment variable not set!")
        print("Please set your GitHub Personal Access Token:")
        print("export GITHUB_TOKEN='your_token_here'")
        return False
    
    # Check AI provider API keys
    if provider.lower() == "anthropic":
        anthropic_api_key = os.getenv("ANTHROPIC_API_KEY")
        if not anthropic_api_key:
            print("❌ Error: ANTHROPIC_API_KEY environment variable not set!")
            print("Please set your Anthropic API key:")
            print("export ANTHROPIC_API_KEY='your_key_here'")
            return False
        print(f"🔑 GitHub token found: ...{github_token[-8:]}")
        print(f"🔑 Anthropic API key found: ...{anthropic_api_key[-8:]}")
        print(f"🧠 Using provider: Claude (Anthropic)")
    else:
        openai_api_key = os.getenv("OPENAI_API_KEY")
        if not openai_api_key:
            print("❌ Error: OPENAI_API_KEY environment variable not set!")
            print("Please set your OpenAI API key:")
            print("export OPENAI_API_KEY='your_key_here'")
            return False
        print(f"🔑 GitHub token found: ...{github_token[-8:]}")
        print(f"🔑 OpenAI API key found: ...{openai_api_key[-8:]}")
        print(f"🧠 Using provider: GPT-4o-mini (OpenAI)")
    
    try:
        print(f"📂 Fetching repository: {repo_url}")
        repo = Repo.from_github_token(repo_url)
        
        print(f"✅ Repository loaded: {repo.full_name}")
        print(f"📝 Description: {repo.description or 'No description'}")
        print(f"🏷️  Language: {repo.language or 'Multiple/Unknown'}")
        print(f"⭐ Stars: {repo.stars:,}")
        
        print(f"\n🔍 Fetching repository content...")
        
        # Get repository content (this will fetch all readable files)
        repo_content = repo.get_repository_content()
        
        print(f"📊 Content Summary:")
        print(f"  • Total files: {repo_content['summary']['total_files']}")
        print(f"  • Total size: {repo_content['summary']['total_size']:,} bytes")
        print(f"  • File types: {', '.join([f'{ext}({count})' for ext, count in repo_content['summary']['file_types'].items()])}")
        
        print(f"\n🤖 Analyzing with AI...")
        
        # Analyze with AI using specified provider
        analysis = repo.analyze_with_ai(provider=provider)
        
        print(f"\n" + "="*60)
        print("🎯 AI ANALYSIS RESULTS")
        print("="*60)
        print(analysis)
        print("="*60)
        
        # Save analysis to file
        output_file = f"analysis_{repo.name}_{repo.owner}.md"
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(f"# AI Analysis: {repo.full_name}\n\n")
            f.write(f"**Repository**: {repo.html_url}\n")
            f.write(f"**Description**: {repo.description or 'No description'}\n")
            f.write(f"**Language**: {repo.language or 'Multiple/Unknown'}\n")
            f.write(f"**Stars**: {repo.stars:,}\n")
            from datetime import datetime
            f.write(f"**Analysis Date**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            f.write("## Analysis Results\n\n")
            f.write(analysis)
        
        print(f"\n💾 Analysis saved to: {output_file}")
        return True
        
    except Exception as e:
        print(f"❌ Error analyzing repository: {e}")
        return False

def main():
    """Main function."""
    
    # Get repo URL and provider from command line or prompt
    repo_url = None
    provider = "openai"  # default
    
    if len(sys.argv) > 1:
        repo_url = sys.argv[1]
        if len(sys.argv) > 2:
            provider = sys.argv[2].lower()
    else:
        repo_url = input("Enter GitHub repository URL: ").strip()
        
        if not repo_url:
            print("❌ No repository URL provided!")
            print("\nExample usage:")
            print("  python ai-repo-analyzer.py https://github.com/username/repo")
            print("  python ai-repo-analyzer.py https://github.com/username/repo anthropic")
            print("  python ai-repo-analyzer.py https://github.com/username/repo openai")
            return
        
        provider_choice = input("Choose AI provider (openai/anthropic) [openai]: ").strip().lower()
        if provider_choice in ["anthropic", "claude"]:
            provider = "anthropic"
    
    # Validate URL
    if "github.com" not in repo_url:
        print("❌ Invalid GitHub URL!")
        return
    
    # Validate provider
    if provider not in ["openai", "anthropic"]:
        print("❌ Invalid provider! Use 'openai' or 'anthropic'")
        return
    
    # Run analysis
    success = analyze_repo_with_ai(repo_url, provider)
    
    if success:
        print(f"\n✅ AI analysis completed successfully!")
    else:
        print(f"\n❌ AI analysis failed!")

if __name__ == "__main__":
    main()