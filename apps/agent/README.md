# Backspace MonoRepo

## Structure

```
agent/
├── db.py                    # Database connection manager
├── webapp.py               # FastAPI webhook handler
├── agents/                 # AI agent implementations
│   ├── base.py            # Abstract base classes
│   ├── llm_config.py      # LLM configuration utilities
│   ├── scanner/           # Security & quality scanner
│   │   ├── agent.py
│   │   ├── graph.py
│   │   ├── prompts.py
│   │   └── states.py
│   ├── coder/             # AI coding assistant
│   │   ├── agent.py
│   │   ├── graph.py
│   │   ├── prompts.py
│   │   └── states.py
│   └── tools/             # Agent tools and utilities
└── utils/                 # Utility modules
    ├── github_utils.py    # GitHub API utilities
    ├── docker_builder.py  # Docker image operations
    ├── sandbox.py         # Sandboxed execution
    └── github_auth.py     # GitHub authentication
```

#### 🔍 Scanner Agent

A comprehensive codebase analysis tool that performs:

- Security vulnerability detection
- Code quality assessment
- Test coverage analysis
- Dependency security checks
- Performance issue identification
- Automatic GitHub issue creation for findings

#### 💻 Coder Agent

An AI coding assistant that provides:

- Intelligent code analysis and modification
- Pattern recognition and search
- Code structure analysis
- File I/O operations
- Sandboxed code execution

## 🛠️ Setup & Installation

### Prerequisites

- Python 3.8+
- Docker
- Supabase account
- GitHub App credentials

### Environment Variables

```bash
# Supabase Configuration
SUPABASE_URI=your_supabase_url
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key

# GitHub App Configuration
GITHUB_APP_ID=your_github_app_id
GITHUB_PRIVATE_KEY=your_github_private_key
GITHUB_WEBHOOK_SECRET=your_webhook_secret

# AI Model Configuration (optional)
ANTHROPIC_API_KEY=your_anthropic_key
OPENAI_API_KEY=your_openai_key
```
