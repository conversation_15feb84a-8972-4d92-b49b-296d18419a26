#!/usr/bin/env python3
"""
Direct test to verify Claude E2B tracing is working with LangSmith.
This directly uses E2B AsyncSandbox and run_claude_in_sandbox to test tracing.
"""

import asyncio
import logging
import os
import sys
from pathlib import Path

# Load environment variables from .env file
from dotenv import load_dotenv
load_dotenv(Path(__file__).parent.parent.parent / ".env")

# Add the src directory to Python path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from agents.claude_e2b.claude import run_claude_in_sandbox
from e2b import AsyncSandbox

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(levelname)s: %(message)s'
)
logger = logging.getLogger(__name__)

async def test_direct_claude_tracing():
    """Test Claude E2B with Lang<PERSON>mith tracing enabled using direct E2B sandbox."""
    
    # Check environment variables (using correct <PERSON><PERSON><PERSON> names)
    required_env_vars = [
        "LANGCHAIN_API_KEY",
        "LANGCHAIN_TRACING_V2",
        "LANGCHAIN_PROJECT",
        "E2B_API_KEY",
        "ANTHROPIC_API_KEY"
    ]
    
    missing_vars = [var for var in required_env_vars if not os.getenv(var)]
    if missing_vars:
        logger.error(f"❌ Missing environment variables: {missing_vars}")
        return False
    
    logger.info("✅ All required environment variables found")
    logger.info(f"🔍 LangSmith Project: {os.getenv('LANGCHAIN_PROJECT')}")
    
    sandbox = None
    try:
        # Create E2B sandbox directly
        logger.info("🚀 Creating E2B sandbox directly...")
        sandbox = await AsyncSandbox.create(
            template="vcomjjr43nxwhfxodbqm",  # Claude Code template
            timeout=300,
            envs={
                "ANTHROPIC_API_KEY": os.getenv("ANTHROPIC_API_KEY"),
                "WORKSPACE_PATH": "/home/<USER>/workspace"
            }
        )
        logger.info(f"✅ Created sandbox: {sandbox.sandbox_id}")
        
        # Simple test prompt
        test_prompt = """
        Please do a quick test:
        1. Show the current directory with 'ls -la'
        2. Create a simple file called 'test.txt' with content 'Hello from Claude tracing test'
        3. Read the file back to verify it was created
        
        Keep this simple and quick for testing LangSmith tracing.
        """
        
        logger.info("🎯 Running Claude with tracing enabled...")
        logger.info("📊 This should create LangSmith traces showing:")
        logger.info("   - Top-level: Claude CLI execution")
        logger.info("   - Child traces: System Init, Claude Messages, Tool Calls, Tool Results")
        logger.info("   - Hierarchical structure with tool results nested under tool calls")
        
        # Run Claude with tracing enabled
        session = await run_claude_in_sandbox(
            sandbox=sandbox,
            prompt=test_prompt,
            claude_options={"max-turns": "3"},  # Limit for quick test
            enable_tracing=True,  # This should enable LangSmith tracing
            timeout=120
        )
        
        logger.info("✅ Claude execution completed!")
        logger.info(f"📊 Session ID: {session.session_id}")
        logger.info(f"📝 Total outputs: {len(session.outputs)}")
        logger.info(f"💰 Total cost: ${session.total_cost_usd:.4f}")
        
        # Show some sample outputs
        if session.outputs:
            logger.info("📋 Sample outputs:")
            for i, output in enumerate(session.outputs[:3]):  # Show first 3
                logger.info(f"   {i+1}. {output.type}: {str(output.content)[:100]}...")
        
        # Show LangSmith information
        project_name = os.getenv('LANGCHAIN_PROJECT')
        logger.info("🔗 Check your LangSmith traces at:")
        logger.info(f"   https://smith.langchain.com/projects/{project_name}")
        logger.info("🎯 Look for traces with:")
        logger.info("   - Top-level: Claude CLI execution")
        logger.info("   - Child traces: System Init, Claude Messages, Tool Calls, Tool Results")
        logger.info("   - Tool results nested under their corresponding tool calls")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Test failed with exception: {e}")
        import traceback
        logger.error(f"📋 Full traceback:\n{traceback.format_exc()}")
        return False
    
    finally:
        # Clean up sandbox
        if sandbox:
            try:
                logger.info("🧹 Cleaning up sandbox...")
                await sandbox.kill()
                logger.info("✅ Sandbox cleaned up")
            except Exception as e:
                logger.warning(f"⚠️ Failed to clean up sandbox: {e}")

async def main():
    """Main test function."""
    logger.info("🧪 Testing Direct Claude E2B LangSmith tracing...")
    logger.info("=" * 60)
    
    success = await test_direct_claude_tracing()
    
    logger.info("=" * 60)
    if success:
        logger.info("✅ Test PASSED - Claude E2B tracing is working!")
        logger.info("🎉 Your LangSmith integration is functioning correctly")
        logger.info("💡 This proves claude_scanner tracing works automatically")
        logger.info("🔄 claude_scanner uses the same run_claude_in_sandbox function")
    else:
        logger.info("❌ Test FAILED - check the errors above")
    
    return success

if __name__ == "__main__":
    # Run the test
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
