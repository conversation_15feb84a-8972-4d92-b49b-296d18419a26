export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  public: {
    Tables: {
      incidents: {
        Row: {
          branch: string | null
          created_at: string
          description: string | null
          id: number
          service: string | null
          severity: string | null
          url: string | null
        }
        Insert: {
          branch?: string | null
          created_at?: string
          description?: string | null
          id?: number
          service?: string | null
          severity?: string | null
          url?: string | null
        }
        Update: {
          branch?: string | null
          created_at?: string
          description?: string | null
          id?: number
          service?: string | null
          severity?: string | null
          url?: string | null
        }
        Relationships: []
      }
      integrations: {
        Row: {
          created_at: string
          data: Json | null
          id: number
          name: string | null
          org_id: number | null
        }
        Insert: {
          created_at?: string
          data?: Json | null
          id?: number
          name?: string | null
          org_id?: number | null
        }
        Update: {
          created_at?: string
          data?: Json | null
          id?: number
          name?: string | null
          org_id?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "integrations_org_id_fkey"
            columns: ["org_id"]
            isOneToOne: false
            referencedRelation: "organizations"
            referencedColumns: ["id"]
          },
        ]
      }
      issues: {
        Row: {
          created_at: string
          dependencies_ids: number[] | null
          description: string | null
          hash: string | null
          id: number
          metadata: Json | null
          name: string | null
          pr_link: string | null
          scan_id: number | null
          status: string | null
        }
        Insert: {
          created_at?: string
          dependencies_ids?: number[] | null
          description?: string | null
          hash?: string | null
          id?: number
          metadata?: Json | null
          name?: string | null
          pr_link?: string | null
          scan_id?: number | null
          status?: string | null
        }
        Update: {
          created_at?: string
          dependencies_ids?: number[] | null
          description?: string | null
          hash?: string | null
          id?: number
          metadata?: Json | null
          name?: string | null
          pr_link?: string | null
          scan_id?: number | null
          status?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "issues_scan_id_fkey"
            columns: ["scan_id"]
            isOneToOne: false
            referencedRelation: "scans"
            referencedColumns: ["id"]
          },
        ]
      }
      organization_invitations: {
        Row: {
          accepted_at: string | null
          created_at: string
          email: string
          expires_at: string | null
          id: number
          invited_by: string | null
          org_id: number
          status: string
          token: string
        }
        Insert: {
          accepted_at?: string | null
          created_at?: string
          email: string
          expires_at?: string | null
          id?: never
          invited_by?: string | null
          org_id: number
          status?: string
          token?: string
        }
        Update: {
          accepted_at?: string | null
          created_at?: string
          email?: string
          expires_at?: string | null
          id?: never
          invited_by?: string | null
          org_id?: number
          status?: string
          token?: string
        }
        Relationships: [
          {
            foreignKeyName: "organization_invitations_org_id_fkey"
            columns: ["org_id"]
            isOneToOne: false
            referencedRelation: "organizations"
            referencedColumns: ["id"]
          },
        ]
      }
      organization_users: {
        Row: {
          created_at: string
          id: number
          invited_by: string | null
          is_admin: boolean | null
          org_id: number
          status: string
          user_id: string
        }
        Insert: {
          created_at?: string
          id?: number
          invited_by?: string | null
          is_admin?: boolean | null
          org_id: number
          status?: string
          user_id: string
        }
        Update: {
          created_at?: string
          id?: number
          invited_by?: string | null
          is_admin?: boolean | null
          org_id?: number
          status?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "organization_members_org_id_fkey"
            columns: ["org_id"]
            isOneToOne: false
            referencedRelation: "organizations"
            referencedColumns: ["id"]
          },
        ]
      }
      organizations: {
        Row: {
          created_at: string
          created_by: string | null
          id: number
          name: string | null
          slug: string | null
        }
        Insert: {
          created_at?: string
          created_by?: string | null
          id?: number
          name?: string | null
          slug?: string | null
        }
        Update: {
          created_at?: string
          created_by?: string | null
          id?: number
          name?: string | null
          slug?: string | null
        }
        Relationships: []
      }
      repositories: {
        Row: {
          created_at: string
          daytona_image: string | null
          id: number
          integration_id: number | null
          name: string | null
          org_id: number | null
          pushed_at: string | null
          repo_created_at: string | null
          url: string
        }
        Insert: {
          created_at?: string
          daytona_image?: string | null
          id?: number
          integration_id?: number | null
          name?: string | null
          org_id?: number | null
          pushed_at?: string | null
          repo_created_at?: string | null
          url: string
        }
        Update: {
          created_at?: string
          daytona_image?: string | null
          id?: number
          integration_id?: number | null
          name?: string | null
          org_id?: number | null
          pushed_at?: string | null
          repo_created_at?: string | null
          url?: string
        }
        Relationships: [
          {
            foreignKeyName: "repositories_integration_id_fkey"
            columns: ["integration_id"]
            isOneToOne: false
            referencedRelation: "integrations"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "Repository_org_id_fkey"
            columns: ["org_id"]
            isOneToOne: false
            referencedRelation: "organizations"
            referencedColumns: ["id"]
          },
        ]
      }
      scans: {
        Row: {
          branch: string | null
          created_at: string
          duration: string | null
          id: number
          metrics: Json | null
          repo_id: number | null
          status: string | null
          summary: string | null
          token_cost: string | null
          trigger: string | null
        }
        Insert: {
          branch?: string | null
          created_at?: string
          duration?: string | null
          id?: number
          metrics?: Json | null
          repo_id?: number | null
          status?: string | null
          summary?: string | null
          token_cost?: string | null
          trigger?: string | null
        }
        Update: {
          branch?: string | null
          created_at?: string
          duration?: string | null
          id?: number
          metrics?: Json | null
          repo_id?: number | null
          status?: string | null
          summary?: string | null
          token_cost?: string | null
          trigger?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "scans_repo_id_fkey"
            columns: ["repo_id"]
            isOneToOne: false
            referencedRelation: "repositories"
            referencedColumns: ["id"]
          },
        ]
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DefaultSchema = Database[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof Database },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  public: {
    Enums: {},
  },
} as const
