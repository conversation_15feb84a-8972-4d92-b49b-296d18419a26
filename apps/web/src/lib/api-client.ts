/**
 * API client for communicating with FastAPI backend
 */
// Removed useAuth import - now using direct Supabase client calls
import { createClient } from '@/lib/supabase/client';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';

export interface ResearchRequest {
  query: string;
  max_searches?: number;
}

export interface ResearchSession {
  id: string;
  user_id: string;
  query: string;
  status:
    | 'pending'
    | 'planning'
    | 'researching'
    | 'writing'
    | 'completed'
    | 'failed';
  max_searches: number;
  search_count: number;
  created_at: string;
  updated_at: string;
}

export interface ResearchEvent {
  session_id: string;
  event_type: string;
  content: string;
  metadata?: Record<string, any>;
  timestamp: string;
}

export interface CodegenRequest {
  prompt: string;
  context?: string;
  file_path?: string;
}

export interface CodegenResponse {
  result: string;
  session_id: string;
  execution_time: number;
}

class ApiClient {
  private baseUrl: string;

  constructor(baseUrl: string = API_BASE_URL) {
    this.baseUrl = baseUrl;
  }

  private async fetchWithAuth(
    endpoint: string,
    options: RequestInit = {},
    getToken?: () => Promise<string | null>
  ): Promise<Response> {
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
      ...((options.headers as Record<string, string>) || {})
    };

    // Add auth token if getToken function is provided
    if (getToken) {
      const token = await getToken();
      if (token) {
        headers.Authorization = `Bearer ${token}`;
      }
    }

    const response = await fetch(`${this.baseUrl}${endpoint}`, {
      ...options,
      headers
    });

    if (!response.ok) {
      const errorData = await response
        .json()
        .catch(() => ({ detail: 'Unknown error' }));
      throw new Error(errorData.detail || `HTTP ${response.status}`);
    }

    return response;
  }

  // Research endpoints
  async createResearchSession(
    request: ResearchRequest,
    getToken: () => Promise<string | null>
  ): Promise<{ session: ResearchSession; message: string }> {
    const response = await this.fetchWithAuth(
      '/api/research',
      {
        method: 'POST',
        body: JSON.stringify(request)
      },
      getToken
    );
    return response.json();
  }

  async getResearchSession(
    sessionId: string,
    getToken: () => Promise<string | null>
  ): Promise<{
    session: ResearchSession;
    events: ResearchEvent[];
    event_count: number;
  }> {
    const response = await this.fetchWithAuth(
      `/api/research/${sessionId}`,
      {},
      getToken
    );
    return response.json();
  }

  async getResearchHistory(
    page: number = 1,
    pageSize: number = 20,
    getToken: () => Promise<string | null>
  ): Promise<{
    sessions: ResearchSession[];
    total: number;
    page: number;
    page_size: number;
  }> {
    const response = await this.fetchWithAuth(
      `/api/research?page=${page}&page_size=${pageSize}`,
      {},
      getToken
    );
    return response.json();
  }

  // Code generation endpoints
  async generateCode(
    request: CodegenRequest,
    getToken: () => Promise<string | null>
  ): Promise<CodegenResponse> {
    const response = await this.fetchWithAuth(
      '/api/codegen',
      {
        method: 'POST',
        body: JSON.stringify(request)
      },
      getToken
    );
    return response.json();
  }

  // User endpoints
  async getUserProfile(getToken: () => Promise<string | null>): Promise<{
    user_id: string;
    email?: string;
    username?: string;
    first_name?: string;
    last_name?: string;
  }> {
    const response = await this.fetchWithAuth(
      '/api/user/profile',
      {},
      getToken
    );
    return response.json();
  }

  // Server-Sent Events for research streaming
  createResearchEventSource(sessionId: string, token: string): EventSource {
    const url = new URL(`${this.baseUrl}/api/research/${sessionId}/stream`);

    // Note: EventSource doesn't support custom headers, so we need to pass token as query param
    // This is less secure but necessary for EventSource. In production, consider WebSockets instead.
    const eventSource = new EventSource(url.toString());

    return eventSource;
  }
}

// Hook for using API client with Supabase auth
export function useApiClient() {
  const client = new ApiClient();

  const getToken = async (): Promise<string | null> => {
    const supabase = createClient();
    const {
      data: { session }
    } = await supabase.auth.getSession();
    return session?.access_token || null;
  };

  return {
    client,
    getToken,

    // Convenience methods that automatically include auth
    createResearchSession: (request: ResearchRequest) =>
      client.createResearchSession(request, getToken),

    getResearchSession: (sessionId: string) =>
      client.getResearchSession(sessionId, getToken),

    getResearchHistory: (page?: number, pageSize?: number) =>
      client.getResearchHistory(page, pageSize, getToken),

    generateCode: (request: CodegenRequest) =>
      client.generateCode(request, getToken),

    getUserProfile: () => client.getUserProfile(getToken),

    createResearchEventSource: async (sessionId: string) => {
      const token = await getToken();
      if (!token) throw new Error('No auth token available');
      return client.createResearchEventSource(sessionId, token);
    }
  };
}

// Default export for non-hook usage
export const apiClient = new ApiClient();
