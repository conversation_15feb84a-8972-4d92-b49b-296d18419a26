import React, { useMemo } from 'react';
import ReactMarkdown from 'react-markdown';
import { cn } from '@/lib/utils';

interface MarkdownProps {
  content: string;
  className?: string;
}

export function Markdown({ content, className }: MarkdownProps) {
  const processedContent = useMemo(() => {
    // Convert \n to actual newlines for proper markdown processing
    return content.replace(/\\n/g, '\n');
  }, [content]);

  const renderedMarkdown = useMemo(() => {
    return (
      <div
        className={cn(
          'prose prose-sm dark:prose-invert max-w-none text-sm',
          // Custom styling for markdown elements
          '[&_h1]:mt-6 [&_h1]:mb-4 [&_h1]:text-xl [&_h1]:font-medium first:[&_h1]:mt-0',
          '[&_h2]:mt-5 [&_h2]:mb-3 [&_h2]:text-lg [&_h2]:font-medium first:[&_h2]:mt-0',
          '[&_h3]:mt-4 [&_h3]:mb-2 [&_h3]:text-base [&_h3]:font-medium first:[&_h3]:mt-0',
          '[&_p]:mb-3 [&_p]:leading-relaxed first:[&_p]:mt-0',
          '[&_ul]:mb-3 [&_ul]:ml-4',
          '[&_ol]:mb-3 [&_ol]:ml-4',
          '[&_li]:mb-1',
          '[&_blockquote]:my-4 [&_blockquote]:border-l-4 [&_blockquote]:border-gray-300 [&_blockquote]:pl-4 [&_blockquote]:italic',
          '[&_code]:rounded [&_code]:bg-gray-100 [&_code]:px-1.5 [&_code]:py-0.5 [&_code]:font-mono [&_code]:text-sm [&_code]:dark:bg-gray-800',
          '[&_pre]:my-4 [&_pre]:overflow-x-auto [&_pre]:rounded-md [&_pre]:bg-gray-100 [&_pre]:p-4 [&_pre]:dark:bg-gray-800',
          '[&_pre_code]:bg-transparent [&_pre_code]:p-0',
          '[&_strong]:font-medium',
          '[&_em]:italic',
          '[&_a]:text-blue-600 [&_a]:underline [&_a]:hover:text-blue-800 [&_a]:dark:text-blue-400 [&_a]:dark:hover:text-blue-300',
          '[&_table]:my-4 [&_table]:w-full [&_table]:border-collapse',
          '[&_th]:border [&_th]:border-gray-300 [&_th]:bg-gray-50 [&_th]:px-3 [&_th]:py-2 [&_th]:text-left [&_th]:font-medium [&_th]:dark:border-gray-600 [&_th]:dark:bg-gray-800',
          '[&_td]:border [&_td]:border-gray-300 [&_td]:px-3 [&_td]:py-2 [&_td]:dark:border-gray-600',
          '[&_hr]:my-6 [&_hr]:border-gray-300 [&_hr]:dark:border-gray-600',
          className
        )}
      >
        <ReactMarkdown
          components={{
            h1: ({ children }) => (
              <h1 className='mt-6 mb-4 text-xl font-medium text-gray-900 first:mt-0 dark:text-gray-100'>
                {children}
              </h1>
            ),
            h2: ({ children }) => (
              <h2 className='mt-5 mb-3 text-lg font-medium text-gray-900 first:mt-0 dark:text-gray-100'>
                {children}
              </h2>
            ),
            h3: ({ children }) => (
              <h3 className='mt-4 mb-2 text-base font-medium text-gray-900 first:mt-0 dark:text-gray-100'>
                {children}
              </h3>
            ),
            p: ({ children }) => (
              <p className='mb-3 text-sm leading-relaxed text-gray-700 first:mt-0 dark:text-gray-300'>
                {children}
              </p>
            ),
            code: ({ children, ...props }) => {
              // Check if this is inline code or block code
              const isInline =
                typeof props.node?.position?.start?.line === 'undefined' ||
                props.node?.position?.start?.line ===
                  props.node?.position?.end?.line;

              if (isInline) {
                return (
                  <code className='rounded bg-gray-100 px-1.5 py-0.5 font-mono text-sm text-gray-800 dark:bg-gray-800 dark:text-gray-200'>
                    {children}
                  </code>
                );
              }

              return (
                <code className='my-4 block overflow-x-auto rounded-md bg-gray-100 p-4 font-mono text-sm text-gray-800 dark:bg-gray-800 dark:text-gray-200'>
                  {children}
                </code>
              );
            },
            pre: ({ children }) => (
              <pre className='my-4 overflow-x-auto rounded-md bg-gray-100 p-4 text-sm dark:bg-gray-800'>
                {children}
              </pre>
            ),
            blockquote: ({ children }) => (
              <blockquote className='my-4 border-l-4 border-gray-300 pl-4 text-sm text-gray-600 italic dark:border-gray-600 dark:text-gray-400'>
                {children}
              </blockquote>
            ),
            ul: ({ children }) => (
              <ul className='mb-3 ml-4 list-disc text-sm text-gray-700 dark:text-gray-300'>
                {children}
              </ul>
            ),
            ol: ({ children }) => (
              <ol className='mb-3 ml-4 list-decimal text-sm text-gray-700 dark:text-gray-300'>
                {children}
              </ol>
            ),
            li: ({ children }) => <li className='mb-1 text-sm'>{children}</li>,
            strong: ({ children }) => (
              <strong className='font-medium text-gray-900 dark:text-gray-100'>
                {children}
              </strong>
            ),
            a: ({ children, href, ...props }) => (
              <a
                href={href}
                className='text-blue-600 underline hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300'
                {...props}
              >
                {children}
              </a>
            )
          }}
        >
          {processedContent}
        </ReactMarkdown>
      </div>
    );
  }, [processedContent, className]);

  return renderedMarkdown;
}
