# FilterPopover Component

A reusable filter popover component that provides a consistent filtering interface across the application.

## Usage

```tsx
import {
  FilterPopover,
  CommandGroup,
  CommandItem,
  CommandSeparator
} from '@/components/ui/filter-popover';

function MyFilterComponent() {
  const [selectedValues, setSelectedValues] = useState<string[]>([]);

  const trigger = (
    <Button variant='outline' size='sm' className='h-7 text-xs'>
      <IconFilter className='mr-1 h-3 w-3' />
      Filter Label
      {selectedValues.length > 0 && (
        <>
          <div className='bg-border mx-1 h-3 w-px' />
          <Badge variant='secondary' className='px-1 py-0 text-xs'>
            {selectedValues.length}
          </Badge>
        </>
      )}
    </Button>
  );

  return (
    <FilterPopover trigger={trigger} searchPlaceholder='Search options...'>
      <CommandGroup>
        {options.map((option) => (
          <CommandItem
            key={option}
            onSelect={() => toggleSelection(option)}
            className='text-xs'
          >
            <div
              className={cn(
                'border-primary mr-2 flex h-3 w-3 items-center justify-center rounded-sm border',
                selectedValues.includes(option)
                  ? 'bg-primary text-primary-foreground'
                  : 'opacity-50 [&_svg]:invisible'
              )}
            >
              <IconCheck className='h-2 w-2' />
            </div>
            <span>{option}</span>
          </CommandItem>
        ))}
      </CommandGroup>
      {selectedValues.length > 0 && (
        <>
          <CommandSeparator />
          <CommandGroup>
            <CommandItem
              onSelect={() => setSelectedValues([])}
              className='justify-center text-center text-xs'
            >
              Clear filters
            </CommandItem>
          </CommandGroup>
        </>
      )}
    </FilterPopover>
  );
}
```

## Props

### FilterPopover

| Prop                | Type                           | Default               | Description                                      |
| ------------------- | ------------------------------ | --------------------- | ------------------------------------------------ |
| `trigger`           | `React.ReactNode`              | Required              | The element that triggers the popover            |
| `children`          | `React.ReactNode`              | Required              | The content inside the popover                   |
| `searchPlaceholder` | `string`                       | `"Search..."`         | Placeholder text for the search input            |
| `emptyMessage`      | `string`                       | `"No results found."` | Message shown when no results are found          |
| `contentWidth`      | `string`                       | `"w-48"`              | Tailwind CSS width class for the popover content |
| `align`             | `'start' \| 'center' \| 'end'` | `'start'`             | Alignment of the popover relative to trigger     |

## Exported Components

The following components are re-exported for convenience:

- `CommandGroup` - For grouping related items
- `CommandItem` - For individual selectable items
- `CommandSeparator` - For separating groups of items

## Examples in the Codebase

- **Overview Page**: Type and column filters
- **Scans Page**: Status, trigger, and column filters
- **Issues Page**: Status, priority, trigger, and column filters
- **Incidents Page**: Status, severity, and column filters
- **Members Page**: Role, status, and column filters

## Design Patterns

1. **Consistent Trigger Design**: Use outline buttons with filter icon and count badge
2. **Selection State**: Use checkboxes with visual feedback for selected state
3. **Clear Option**: Include a "Clear filters" option when items are selected
4. **Search**: Enable search for longer lists of options
5. **Grouping**: Use CommandGroup to organize related options
