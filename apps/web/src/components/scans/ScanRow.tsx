import React from 'react';
import { TableCell, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { cn } from '@/lib/utils';
import type { Scan, VisibleColumns } from '@/constants/scans';

interface ScanRowProps {
  scan: Scan;
  visibleColumns: VisibleColumns;
  isHovered: boolean;
  onMouseEnter: () => void;
  onMouseLeave: () => void;
  onClick: () => void;
  getScanDescription: (scan: Scan) => string;
}

const getStatusBadge = (status: string) => {
  const badges = {
    completed: (
      <Badge className='bg-green-100 px-2 py-0 text-sm text-green-800 dark:bg-green-900/30 dark:text-green-300'>
        Completed
      </Badge>
    ),
    running: (
      <Badge className='bg-blue-100 px-2 py-0 text-sm text-blue-800 dark:bg-blue-900/30 dark:text-blue-300'>
        Running
      </Badge>
    )
  };
  return badges[status as keyof typeof badges] || badges.completed;
};

const getTriggerBadge = (trigger: string) => {
  const badges = {
    manual: (
      <Badge className='bg-blue-100 text-sm text-blue-800 dark:bg-blue-900/30 dark:text-blue-300'>
        Manual
      </Badge>
    ),
    scheduled: (
      <Badge className='bg-purple-100 px-2 py-0 text-sm text-purple-800 dark:bg-purple-900/30 dark:text-purple-300'>
        Scheduled
      </Badge>
    )
  };
  return badges[trigger as keyof typeof badges] || badges.manual;
};

const formatDurationInMinutes = (duration: string) => {
  const seconds = parseInt(duration.replace('s', ''));
  const minutes = Math.round((seconds / 60) * 10) / 10;
  return `${minutes}m`;
};

export function ScanRow({
  scan,
  visibleColumns,
  isHovered,
  onMouseEnter,
  onMouseLeave,
  onClick,
  getScanDescription
}: ScanRowProps) {
  return (
    <TableRow
      className={cn(
        'cursor-pointer transition-colors',
        isHovered && 'bg-muted/50'
      )}
      onMouseEnter={onMouseEnter}
      onMouseLeave={onMouseLeave}
      onClick={onClick}
    >
      {visibleColumns.trigger && (
        <TableCell className='py-2'>
          {getTriggerBadge(scan.triggerReason)}
        </TableCell>
      )}
      {visibleColumns.status && (
        <TableCell className='py-2'>{getStatusBadge(scan.status)}</TableCell>
      )}
      {visibleColumns.description && (
        <TableCell className='py-2'>
          <div className='text-muted-foreground text-sm'>
            {getScanDescription(scan)}
          </div>
        </TableCell>
      )}
      {visibleColumns.issues && (
        <TableCell className='py-2'>
          <div className='text-muted-foreground text-sm'>
            {scan.issues.length}
          </div>
        </TableCell>
      )}
      {visibleColumns.duration && (
        <TableCell className='py-2'>
          <div className='text-muted-foreground text-sm'>
            {formatDurationInMinutes(scan.duration)}
          </div>
        </TableCell>
      )}
      {visibleColumns.cost && (
        <TableCell className='py-2'>
          <div className='text-muted-foreground text-sm'>
            ${scan.tokenCost.toFixed(3)}
          </div>
        </TableCell>
      )}
      {visibleColumns.creator && (
        <TableCell className='py-2'>
          {scan.creator ? (
            <div className='flex items-center gap-2'>
              <Avatar className='h-5 w-5'>
                <AvatarImage src={scan.creator.avatar} />
                <AvatarFallback className='text-sm'>
                  {scan.creator.initials}
                </AvatarFallback>
              </Avatar>
              <span className='text-sm'>{scan.creator.name}</span>
            </div>
          ) : (
            <div className='flex items-center gap-2'>
              <Avatar className='h-5 w-5'>
                <AvatarImage
                  src='/assets/backspace.svg'
                  className='bg-black p-1'
                />
                <AvatarFallback className='text-sm'>B</AvatarFallback>
              </Avatar>
              <span className='text-sm'>Backspace</span>
            </div>
          )}
        </TableCell>
      )}
      {visibleColumns.timestamp && (
        <TableCell className='text-muted-foreground py-2 text-sm'>
          {scan.timestamp}
        </TableCell>
      )}
    </TableRow>
  );
}
