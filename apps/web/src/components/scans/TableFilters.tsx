import React from 'react';
import { Input } from '@/components/ui/input';
import {
  FilterPopover,
  createFilterTrigger,
  FilterItem,
  FilterActions,
  CommandGroup
} from '@/components/ui/filter-popover';
import { IconColumns3 } from '@tabler/icons-react';
import type { VisibleColumns, Column<PERSON>ey } from '@/constants/scans';

interface TableFiltersProps {
  searchTerm: string;
  onSearchChange: (value: string) => void;
  statusFilter: string[];
  onStatusFilterChange: (value: string[]) => void;
  triggerFilter: string[];
  onTriggerFilterChange: (value: string[]) => void;
  visibleColumns: VisibleColumns;
  onVisibleColumnsChange: (columns: VisibleColumns) => void;
  children?: React.ReactNode;
}

const STATUS_OPTIONS = ['completed', 'running', 'failed'];
const TRIGGER_OPTIONS = ['manual', 'scheduled', 'push', 'pr'];

const COLUMN_LABELS: Record<ColumnKey, string> = {
  timestamp: 'Timestamp',
  trigger: 'Trigger',
  status: 'Status',
  description: 'Description',
  issues: 'Issues Found',
  duration: 'Duration',
  cost: 'Token Cost',
  creator: 'Creator'
};

function StatusFilterPopover({
  label,
  value,
  onChange,
  options
}: {
  label: string;
  value: string[];
  onChange: (value: string[]) => void;
  options: string[];
}) {
  const toggleFilter = (option: string) => {
    onChange(
      value.includes(option)
        ? value.filter((v) => v !== option)
        : [...value, option]
    );
  };

  const selectAll = () => {
    onChange(options);
  };

  const clearAll = () => {
    onChange([]);
  };

  const trigger = createFilterTrigger({
    label
  });

  return (
    <FilterPopover
      trigger={trigger}
      searchPlaceholder={`Search ${label.toLowerCase()}...`}
    >
      <CommandGroup>
        {options.map((option) => (
          <FilterItem
            key={option}
            label={option}
            isSelected={value.includes(option)}
            onToggle={() => toggleFilter(option)}
          />
        ))}
      </CommandGroup>
      <FilterActions
        onSelectAll={selectAll}
        onClearAll={clearAll}
        hasSelections={value.length > 0}
      />
    </FilterPopover>
  );
}

function ColumnsFilterPopover({
  visibleColumns,
  onVisibleColumnsChange
}: {
  visibleColumns: VisibleColumns;
  onVisibleColumnsChange: (columns: VisibleColumns) => void;
}) {
  const toggleColumn = (key: keyof VisibleColumns) => {
    onVisibleColumnsChange({
      ...visibleColumns,
      [key]: !visibleColumns[key]
    });
  };

  const selectAll = () => {
    const allVisible = Object.keys(visibleColumns).reduce(
      (acc, key) => ({ ...acc, [key]: true }),
      {} as VisibleColumns
    );
    onVisibleColumnsChange(allVisible);
  };

  const clearAll = () => {
    const allHidden = Object.keys(visibleColumns).reduce(
      (acc, key) => ({ ...acc, [key]: false }),
      {} as VisibleColumns
    );
    onVisibleColumnsChange(allHidden);
  };

  const trigger = createFilterTrigger({
    label: 'Columns',
    icon: IconColumns3
  });

  return (
    <FilterPopover trigger={trigger} searchPlaceholder='Search columns...'>
      <CommandGroup>
        {Object.entries(visibleColumns).map(([key, visible]) => (
          <FilterItem
            key={key}
            label={COLUMN_LABELS[key as ColumnKey]}
            isSelected={visible}
            onToggle={() => toggleColumn(key as keyof VisibleColumns)}
          />
        ))}
      </CommandGroup>
      <FilterActions
        onSelectAll={selectAll}
        onClearAll={clearAll}
        hasSelections={Object.values(visibleColumns).some(Boolean)}
      />
    </FilterPopover>
  );
}

export function TableFilters({
  searchTerm,
  onSearchChange,
  statusFilter,
  onStatusFilterChange,
  triggerFilter,
  onTriggerFilterChange,
  visibleColumns,
  onVisibleColumnsChange,
  children
}: TableFiltersProps) {
  return (
    <div className='flex items-center justify-between gap-3'>
      <div className='flex items-center gap-3'>
        <div className='flex items-center gap-2'>
          <StatusFilterPopover
            label='Status'
            value={statusFilter}
            onChange={onStatusFilterChange}
            options={STATUS_OPTIONS}
          />

          <StatusFilterPopover
            label='Trigger'
            value={triggerFilter}
            onChange={onTriggerFilterChange}
            options={TRIGGER_OPTIONS}
          />

          <ColumnsFilterPopover
            visibleColumns={visibleColumns}
            onVisibleColumnsChange={onVisibleColumnsChange}
          />
        </div>

        <Input
          placeholder='Search scans...'
          value={searchTerm}
          onChange={(e) => onSearchChange(e.target.value)}
          className='h-7 w-64 border-0 text-xs shadow-none'
        />
      </div>

      {children && <div className='flex items-center gap-2'>{children}</div>}
    </div>
  );
}
