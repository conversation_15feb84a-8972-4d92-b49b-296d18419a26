import React from 'react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';
import { IconX } from '@tabler/icons-react';
import { SignalHigh, SignalLow, SignalMedium } from 'lucide-react';
import { cn } from '@/lib/utils';
import { generateAgentTickets } from '@/utils/mockData';
import { getMetricSummaryText } from '@/utils/scanUtils';
import { METRICS_CONFIG } from '@/constants/scans';
import type { Scan, Issue } from '@/constants/scans';

interface ScanDetailsModalProps {
  scan: Scan;
  isVisible: boolean;
  onClose: () => void;
}

const getStatusBadge = (status: Scan['status']) => {
  const badges = {
    completed: (
      <Badge className='bg-green-100 text-sm text-green-800 dark:bg-green-900/30 dark:text-green-300'>
        Completed
      </Badge>
    ),
    running: (
      <Badge className='bg-blue-100 text-sm text-blue-800 dark:bg-blue-900/30 dark:text-blue-300'>
        Running
      </Badge>
    ),
    failed: (
      <Badge className='bg-red-100 text-sm text-red-800 dark:bg-red-900/30 dark:text-red-300'>
        Failed
      </Badge>
    )
  };
  return badges[status];
};

const getPriorityBadge = (severity: Issue['severity']) => {
  // Map severity to priority format used in issues page
  const priority =
    severity === 'critical' || severity === 'high'
      ? 'high'
      : severity === 'medium'
        ? 'medium'
        : 'low';

  const badges = {
    low: (
      <Badge className='bg-yellow-100 px-2 py-0 text-sm text-green-800 dark:bg-green-900/30 dark:text-green-300'>
        <div className='flex items-center gap-1.5'>
          <SignalLow className='h-3 w-3' />
          <span>Low</span>
        </div>
      </Badge>
    ),
    medium: (
      <Badge className='bg-orange-100 px-2 py-0 text-sm text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300'>
        <div className='flex items-center gap-1.5'>
          <SignalMedium className='h-3 w-3' />
          <span>Medium</span>
        </div>
      </Badge>
    ),
    high: (
      <Badge className='bg-red-100 px-2 py-0 text-sm text-orange-800 dark:bg-orange-900/30 dark:text-orange-300'>
        <div className='flex items-center gap-1.5'>
          <SignalHigh className='h-3 w-3' />
          <span>High</span>
        </div>
      </Badge>
    )
  };
  return badges[priority];
};

const getTriggerBadge = (trigger: Scan['triggerReason']) => {
  const badges = {
    manual: (
      <Badge className='bg-blue-100 text-sm text-blue-800 dark:bg-blue-900/30 dark:text-blue-300'>
        Manual
      </Badge>
    ),
    scheduled: (
      <Badge className='bg-purple-100 px-2 py-0 text-sm text-purple-800 dark:bg-purple-900/30 dark:text-purple-300'>
        Scheduled
      </Badge>
    ),
    push: (
      <Badge className='bg-orange-100 px-2 py-0 text-sm text-orange-800 dark:bg-orange-900/30 dark:text-orange-300'>
        Push
      </Badge>
    ),
    pr: (
      <Badge className='bg-teal-100 px-2 py-0 text-sm text-teal-800 dark:bg-teal-900/30 dark:text-teal-300'>
        Pull Request
      </Badge>
    )
  };
  return badges[trigger];
};

const formatDurationInMinutes = (duration: string) => {
  const seconds = parseInt(duration.replace('s', ''));
  const minutes = Math.round((seconds / 60) * 10) / 10;
  return `${minutes}m`;
};

export function ScanDetailsModal({
  scan,
  isVisible,
  onClose
}: ScanDetailsModalProps) {
  const scrollToSection = (sectionId: string) => {
    document.getElementById(sectionId)?.scrollIntoView({
      behavior: 'smooth',
      block: 'start'
    });
  };

  return (
    <div className='fixed inset-0 z-50 flex'>
      {/* Backdrop */}
      <div
        className={cn(
          'absolute inset-0 bg-black/50 transition-opacity duration-300',
          isVisible ? 'opacity-100' : 'opacity-0'
        )}
        onClick={onClose}
      />

      {/* Modal Content */}
      <div
        className={cn(
          'bg-background ml-auto h-full w-full max-w-5xl shadow-xl transition-transform duration-300 ease-in-out',
          isVisible ? 'translate-x-0' : 'translate-x-full'
        )}
      >
        <div className='flex h-full'>
          {/* Main Content */}
          <div className='flex-1 overflow-y-auto'>
            <div className='mx-auto px-8 py-8'>
              {/* Header with close button */}
              <div className='mb-8 flex items-start justify-between'>
                <div className='flex-1'>
                  <div className='mb-4 flex items-center gap-3'>
                    <h3 className='text-xl font-semibold'>{scan.name}</h3>
                  </div>
                </div>
                <Button
                  variant='ghost'
                  size='sm'
                  onClick={onClose}
                  className='shrink-0'
                >
                  <IconX className='h-4 w-4' />
                </Button>
              </div>

              <div className='space-y-8'>
                {/* Overview Section */}
                <section id='scan-overview'>
                  <div>
                    <div className='space-y-4 text-sm leading-relaxed'>
                      <p>
                        Our comprehensive analysis reveals a codebase with{' '}
                        <strong>significant potential for enhancement</strong>{' '}
                        across multiple quality dimensions. While the foundation
                        is solid, strategic improvements in{' '}
                        {
                          Object.entries(scan.metrics).filter(
                            ([, value]) => value < 70
                          ).length
                        }{' '}
                        key areas could dramatically improve maintainability,
                        reduce technical debt, and accelerate development
                        velocity.
                      </p>
                      <p>
                        This assessment identifies {scan.issues.length} specific
                        opportunities ranging from quick wins that can be
                        implemented in days to strategic architectural
                        improvements that will future-proof the application.
                      </p>
                    </div>
                  </div>
                </section>

                {/* Metric Analysis Sections */}
                <div className='space-y-8'>
                  {Object.entries(METRICS_CONFIG).map(([key, config]) => {
                    const value =
                      scan.metrics[key as keyof typeof scan.metrics];
                    const metricTickets = generateAgentTickets(scan).filter(
                      (ticket) => ticket.metric === key
                    );

                    // Convert percentage (0-100) to rating (1-10)
                    const rating = Math.max(
                      1,
                      Math.min(10, Math.round(value / 10))
                    );

                    return (
                      <section key={key} id={`analysis-${key}`}>
                        {/* Metric Header */}
                        <div className='mb-6'>
                          <div className='mb-4 flex items-center justify-between'>
                            <div className='flex items-center gap-2'>
                              <config.icon
                                className='h-5 w-5'
                                style={{ color: config.color }}
                              />
                              <h3 className='text-md font-medium'>
                                {config.label}
                              </h3>
                            </div>
                            <div className='flex items-center gap-2 font-medium'>
                              {rating}/10
                            </div>
                          </div>

                          <div className='text-sm leading-relaxed'>
                            <p>
                              {getMetricSummaryText(
                                key,
                                value,
                                metricTickets.length
                              )}
                            </p>
                          </div>
                        </div>

                        {/* Tickets Table */}
                        <div className='space-y-4'>
                          {metricTickets.length > 0 ? (
                            <Table>
                              <TableHeader>
                                <TableRow>
                                  <TableHead className='text-sm'>
                                    Status
                                  </TableHead>
                                  <TableHead className='text-sm'>
                                    Priority
                                  </TableHead>
                                  <TableHead className='text-sm'>
                                    Issue
                                  </TableHead>
                                </TableRow>
                              </TableHeader>
                              <TableBody>
                                {scan.issues
                                  .filter((issue) => issue.type === key)
                                  .map((issue) => (
                                    <TableRow
                                      key={issue.id}
                                      className='text-sm'
                                    >
                                      <TableCell className='py-3'>
                                        {getStatusBadge('completed')}
                                      </TableCell>
                                      <TableCell className='py-3'>
                                        {getPriorityBadge(issue.severity)}
                                      </TableCell>
                                      <TableCell className='py-3'>
                                        <div className='text-sm font-medium'>
                                          {issue.title}
                                        </div>
                                      </TableCell>
                                    </TableRow>
                                  ))}
                              </TableBody>
                            </Table>
                          ) : (
                            <div className='py-8 text-center'>
                              <div className='text-muted-foreground text-sm'>
                                No issues found for this metric. Current score
                                of {Math.round(value)}% indicates excellent
                                performance.
                              </div>
                            </div>
                          )}
                        </div>
                      </section>
                    );
                  })}
                </div>
              </div>
            </div>
          </div>

          {/* Sidebar */}
          <div className='bg-muted/30 w-80 flex-shrink-0 border-l'>
            <div className='sticky top-0 p-6'>
              {/* Navigation */}
              <nav className='mb-8 text-sm'>
                <button
                  onClick={() => scrollToSection('scan-overview')}
                  className='text-muted-foreground hover:text-foreground block w-full rounded-md px-3 py-2 text-left transition-colors'
                >
                  Overview
                </button>
                {Object.entries(METRICS_CONFIG).map(([key, config]) => (
                  <button
                    key={key}
                    onClick={() => scrollToSection(`analysis-${key}`)}
                    className='text-muted-foreground hover:text-foreground block w-full rounded-md px-3 py-2 text-left transition-colors'
                  >
                    {config.label}
                  </button>
                ))}
              </nav>

              {/* Scan Details */}
              <div className='border-t pt-6'>
                <div className='text-muted-foreground space-y-6 text-sm'>
                  <div>
                    <span className='font-medium'>Status:</span>
                    <div className='mt-1'>{getStatusBadge(scan.status)}</div>
                  </div>
                  <div>
                    <span className='font-medium'>Trigger:</span>
                    <div className='mt-1'>
                      {getTriggerBadge(scan.triggerReason)}
                    </div>
                  </div>
                  <div>
                    <span className='font-medium'>Issues Found:</span>
                    <div className='mt-1 font-mono text-sm'>
                      {scan.issues.length}
                    </div>
                  </div>
                  <div>
                    <span className='font-medium'>Duration:</span>
                    <div className='mt-1'>
                      {formatDurationInMinutes(scan.duration)}
                    </div>
                  </div>
                  <div>
                    <span className='font-medium'>Token Cost:</span>
                    <div className='mt-1 font-mono text-sm'>
                      ${scan.tokenCost.toFixed(3)}
                    </div>
                  </div>
                  <div>
                    <span className='font-medium'>Started:</span>
                    <div className='mt-1'>{scan.timestamp}</div>
                  </div>
                  <div>
                    <span className='font-medium'>Scan ID:</span>
                    <div className='mt-1 font-mono text-xs'>{scan.id}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
