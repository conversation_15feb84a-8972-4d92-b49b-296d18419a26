import React, { useState } from 'react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Markdown } from '@/components/ui/markdown';
import {
  IconX,
  IconExternalLink,
  IconCheck,
  IconAlertTriangle,
  IconMessageCircle,
  IconTool,
  IconShield,
  IconUser
} from '@tabler/icons-react';
import { SignalHigh, SignalLow, SignalMedium } from 'lucide-react';
import { cn } from '@/lib/utils';
import { format, formatDistanceToNow } from 'date-fns';

interface Incident {
  id: string;
  title: string;
  description: string;
  severity: 'high' | 'medium' | 'low';
  status: 'running' | 'completed';
  timestamp: string;
  resolvedAt?: string;
  service: string;
  environment: 'production' | 'staging';
}

interface IncidentStep {
  id: string;
  type:
    | 'detection'
    | 'investigation'
    | 'mitigation'
    | 'resolution'
    | 'postmortem';
  title: string;
  description: string;
  status: 'running' | 'completed';
  timestamp: string;
  duration?: number; // duration in minutes
}

interface IncidentDetailsModalProps {
  incident: Incident;
  isVisible: boolean;
  onClose: () => void;
}

const generateMockIncidentSteps = (incident: Incident): IncidentStep[] => {
  const baseSteps: Omit<IncidentStep, 'id' | 'timestamp'>[] = [
    {
      type: 'detection',
      title: 'Incident Detected',
      description: `Incident detected in ${incident.service} service. Monitoring alerts triggered due to ${incident.severity} severity issue.`,
      status: 'completed',
      duration: 1
    },
    {
      type: 'investigation',
      title: 'Initial Investigation',
      description:
        'On-call agent assigned. Investigating root cause and impact assessment.',
      status: 'completed',
      duration: 15
    }
  ];

  if (incident.status === 'completed') {
    baseSteps.push(
      {
        type: 'mitigation',
        title: 'Mitigation Applied',
        description:
          'Temporary fix applied to reduce customer impact. Service functionality partially restored.',
        status: 'completed',
        duration: 30
      },
      {
        type: 'resolution',
        title: 'Issue Resolved',
        description:
          'Root cause identified and permanent fix deployed. Full service functionality restored.',
        status: 'completed',
        duration: 45
      },
      {
        type: 'postmortem',
        title: 'Post-incident Review',
        description:
          'Post-incident review scheduled. Documentation and lessons learned to be captured.',
        status: 'completed'
      }
    );
  } else {
    baseSteps.push({
      type: 'mitigation',
      title: 'Mitigation in Progress',
      description:
        'Working on temporary fix to reduce customer impact. Monitoring service metrics closely.',
      status: 'running'
    });
  }

  return baseSteps.map((step, index) => ({
    ...step,
    id: `step-${index}`,
    timestamp: new Date(
      new Date(incident.timestamp).getTime() + index * 15 * 60 * 1000
    ).toISOString()
  }));
};

const getStepIcon = (type: IncidentStep['type']) => {
  const icons = {
    detection: IconAlertTriangle,
    investigation: IconMessageCircle,
    mitigation: IconTool,
    resolution: IconShield,
    postmortem: IconUser
  };
  return icons[type];
};

const formatDuration = (minutes: number) => {
  if (minutes < 1) {
    return '< 1 minute';
  } else if (minutes < 60) {
    return `${minutes} minute${minutes === 1 ? '' : 's'}`;
  } else {
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;
    if (remainingMinutes === 0) {
      return `${hours} hour${hours === 1 ? '' : 's'}`;
    }
    return `${hours}h ${remainingMinutes}m`;
  }
};

function getStatusBadge(status: Incident['status']) {
  const badges = {
    running: (
      <Badge className='bg-blue-100 text-sm text-blue-800 dark:bg-blue-900/30 dark:text-blue-300'>
        Running
      </Badge>
    ),
    completed: (
      <Badge className='bg-green-100 text-sm text-green-800 dark:bg-green-900/30 dark:text-green-300'>
        Completed
      </Badge>
    )
  };
  return badges[status];
}

function getSeverityBadge(severity: Incident['severity']) {
  const badges = {
    low: (
      <Badge className='bg-yellow-100 px-2 py-0 text-sm text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300'>
        <div className='flex items-center gap-1.5'>
          <SignalLow className='h-3 w-3' />
          <span>Low</span>
        </div>
      </Badge>
    ),
    medium: (
      <Badge className='bg-orange-100 px-2 py-0 text-sm text-orange-800 dark:bg-orange-900/30 dark:text-orange-300'>
        <div className='flex items-center gap-1.5'>
          <SignalMedium className='h-3 w-3' />
          <span>Medium</span>
        </div>
      </Badge>
    ),
    high: (
      <Badge className='bg-red-100 px-2 py-0 text-sm text-red-800 dark:bg-red-900/30 dark:text-red-300'>
        <div className='flex items-center gap-1.5'>
          <SignalHigh className='h-3 w-3' />
          <span>High</span>
        </div>
      </Badge>
    )
  };
  return badges[severity];
}

export function IncidentDetailsModal({
  incident,
  isVisible,
  onClose
}: IncidentDetailsModalProps) {
  const [incidentSteps] = useState(() => generateMockIncidentSteps(incident));

  const resolutionTime = incident.resolvedAt
    ? Math.round(
        (new Date(incident.resolvedAt).getTime() -
          new Date(incident.timestamp).getTime()) /
          (1000 * 60)
      )
    : 0;

  return (
    <div className='fixed inset-0 z-50 flex'>
      {/* Backdrop */}
      <div
        className={cn(
          'absolute inset-0 bg-black/50 transition-opacity duration-300',
          isVisible ? 'opacity-100' : 'opacity-0'
        )}
        onClick={onClose}
      />

      {/* Modal Content */}
      <div
        className={cn(
          'bg-background ml-auto h-full w-full max-w-5xl shadow-xl transition-transform duration-300 ease-in-out',
          isVisible ? 'translate-x-0' : 'translate-x-full'
        )}
      >
        <div className='flex h-full'>
          {/* Main Content */}
          <div className='flex-1 overflow-y-auto'>
            <div className='mx-auto px-8 py-8'>
              {/* Header with close button */}
              <div className='mb-8 flex items-start justify-between'>
                <div className='flex-1'>
                  <div className='mb-4 flex items-center gap-3'>
                    <h3 className='text-xl font-semibold'>{incident.title}</h3>
                  </div>
                </div>
                <Button
                  variant='ghost'
                  size='sm'
                  onClick={onClose}
                  className='shrink-0'
                >
                  <IconX className='h-4 w-4' />
                </Button>
              </div>

              <div className='space-y-8'>
                {/* Incident Summary */}
                <section id='incident-summary'>
                  <div className='mb-6'>
                    <div className='space-y-4 text-sm leading-relaxed'>
                      <Markdown content={incident.description} />
                    </div>
                  </div>
                </section>

                {/* Incident Timeline */}
                <section id='incident-timeline' className='border-t pt-6'>
                  <div className='mb-6'>
                    <div className='mb-4 flex items-center gap-3'>
                      <h3 className='text-md font-medium'>Incident Timeline</h3>
                    </div>
                  </div>

                  {/* Timeline */}
                  <div className='space-y-0'>
                    {incidentSteps.map((step, index) => {
                      const StepIcon = getStepIcon(step.type);
                      return (
                        <div key={step.id} className='flex gap-4'>
                          <div className='flex flex-col items-center'>
                            {step.status === 'completed' ? (
                              <div className='relative flex h-8 w-8 items-center justify-center rounded-full border-2 border-green-200 bg-green-50'>
                                <IconCheck className='h-4 w-4 text-green-600' />
                              </div>
                            ) : step.status === 'running' ? (
                              <div className='h-8 w-8 animate-spin rounded-full border-2 border-blue-200 border-t-blue-600' />
                            ) : (
                              <div className='relative flex h-8 w-8 items-center justify-center rounded-full border-2 border-gray-200 bg-gray-50'>
                                <StepIcon className='h-4 w-4 text-gray-400' />
                              </div>
                            )}
                            {index < incidentSteps.length - 1 && (
                              <div className='min-h-[4rem] w-0.5 flex-1 bg-gray-200' />
                            )}
                          </div>

                          <div className='flex-1 pb-8'>
                            <div className='mb-2 flex items-center gap-2'>
                              <h4 className='text-sm font-medium'>
                                {step.title}
                              </h4>
                            </div>

                            <p className='mb-3 text-sm text-gray-600'>
                              {step.description}
                            </p>

                            <div className='flex items-center gap-4 text-xs text-gray-500'>
                              <span>
                                {format(
                                  new Date(step.timestamp),
                                  'MMM d, HH:mm'
                                )}
                              </span>
                              {step.duration && (
                                <span>
                                  Duration: {formatDuration(step.duration)}
                                </span>
                              )}
                            </div>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </section>
              </div>
            </div>
          </div>

          {/* Sidebar */}
          <div className='bg-muted/30 w-64 flex-shrink-0 border-l'>
            <div className='sticky top-0 p-6'>
              <div>
                <div className='text-muted-foreground space-y-6 text-sm'>
                  <div>
                    <span className='font-medium'>Status:</span>
                    <div className='mt-1'>
                      {getStatusBadge(incident.status)}
                    </div>
                  </div>
                  <div>
                    <span className='font-medium'>Severity:</span>
                    <div className='mt-1'>
                      {getSeverityBadge(incident.severity)}
                    </div>
                  </div>
                  <div>
                    <span className='font-medium'>Service:</span>
                    <div className='mt-1 font-mono text-sm'>
                      {incident.service}
                    </div>
                  </div>
                  <div>
                    <span className='font-medium'>Environment:</span>
                    <div className='mt-1'>
                      <Badge variant='outline' className='text-xs'>
                        {incident.environment}
                      </Badge>
                    </div>
                  </div>
                  <div>
                    <span className='font-medium'>Started:</span>
                    <div className='mt-1'>
                      {format(
                        new Date(incident.timestamp),
                        "MMM d, yyyy 'at' h:mm a"
                      )}
                    </div>
                  </div>
                  {incident.resolvedAt && (
                    <div>
                      <span className='font-medium'>Resolved:</span>
                      <div className='mt-1'>
                        {format(
                          new Date(incident.resolvedAt),
                          "MMM d, yyyy 'at' h:mm a"
                        )}
                      </div>
                    </div>
                  )}
                  {incident.resolvedAt && (
                    <div>
                      <span className='font-medium'>Resolution Time:</span>
                      <div className='mt-1'>
                        {formatDuration(resolutionTime)}
                      </div>
                    </div>
                  )}
                  <div>
                    <span className='font-medium'>Last Updated:</span>
                    <div className='mt-1'>
                      {formatDistanceToNow(
                        new Date(incident.resolvedAt || incident.timestamp),
                        { addSuffix: true }
                      )}
                    </div>
                  </div>
                  <div>
                    <span className='font-medium'>Incident ID:</span>
                    <div className='mt-1 font-mono text-xs'>{incident.id}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
