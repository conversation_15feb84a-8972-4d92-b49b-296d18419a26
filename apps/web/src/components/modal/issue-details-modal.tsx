import React, { useState } from 'react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Markdown } from '@/components/ui/markdown';
import {
  IconX,
  IconGitBranch,
  IconExternalLink,
  IconCheck,
  IconCode,
  IconMessageCircle,
  IconGitCommit,
  IconBug,
  IconShield,
  IconClock,
  IconGitMerge,
  IconPlayerPlay
} from '@tabler/icons-react';
import { SignalHigh, SignalLow, SignalMedium } from 'lucide-react';
import { cn } from '@/lib/utils';
import { format, formatDistanceToNow } from 'date-fns';

interface Issue {
  id: string;
  name: string;
  description: string;
  status: 'queued' | 'running' | 'completed' | 'merged';
  priority: 'low' | 'medium' | 'high';
  trigger: 'scan' | 'incident';
  triggerSource: string;
  githubPrLink?: string;
  createdAt: string;
  updatedAt: string;
  estimatedHours?: number;
  tokenCost: number;
}

interface AgentStep {
  id: string;
  type: 'analysis' | 'code_generation' | 'testing' | 'review' | 'deployment';
  title: string;
  description: string;
  status: 'running' | 'completed';
  timestamp: string;
  duration?: number; // duration in minutes
  artifacts?: {
    type: 'code' | 'test' | 'documentation' | 'commit';
    name: string;
    url?: string;
  }[];
}

interface IssueDetailsModalProps {
  issue: Issue;
  isVisible: boolean;
  onClose: () => void;
}

const generateMockAgentSteps = (issue: Issue): AgentStep[] => {
  const baseSteps: Omit<AgentStep, 'id' | 'timestamp'>[] = [
    {
      type: 'analysis',
      title: 'Issue Analysis',
      description: `Analyzed the reported issue: "${issue.name}". Identified root cause in authentication flow.`,
      status: 'completed',
      duration: 2,
      artifacts: [{ type: 'documentation', name: 'Analysis Report', url: '#' }]
    },
    {
      type: 'code_generation',
      title: 'Solution Implementation',
      description:
        'Generated fix for authentication timeout issue. Updated session management logic.',
      status: 'completed',
      duration: 13,
      artifacts: [
        { type: 'code', name: 'auth-session.ts', url: '#' },
        { type: 'code', name: 'middleware.ts', url: '#' }
      ]
    },
    {
      type: 'testing',
      title: 'Test Generation',
      description:
        'Created comprehensive test suite to verify the fix and prevent regression.',
      status: 'completed',
      duration: 28,
      artifacts: [{ type: 'test', name: 'auth-session.test.ts', url: '#' }]
    },
    {
      type: 'review',
      title: 'Code Review',
      description:
        'Automated code review completed. No issues found. Ready for deployment.',
      status: 'completed',
      duration: 43,
      artifacts: [{ type: 'documentation', name: 'Review Summary', url: '#' }]
    }
  ];

  if (issue.githubPrLink) {
    baseSteps.push({
      type: 'deployment',
      title: 'GitHub PR Creation',
      description:
        'Created pull request with the proposed changes. Awaiting review.',
      status: 'completed',
      artifacts: [
        { type: 'commit', name: `Fix: ${issue.name}`, url: issue.githubPrLink }
      ]
    });
  }



  
  return baseSteps.map((step, index) => ({
    ...step,
    id: `step-${index}`,
    timestamp: new Date(
      new Date(issue.createdAt).getTime() + index * 15 * 60 * 1000
    ).toISOString()
  }));
};

const getStepIcon = (type: AgentStep['type']) => {
  const icons = {
    analysis: IconBug,
    code_generation: IconCode,
    testing: IconShield,
    review: IconMessageCircle,
    deployment: IconGitBranch
  };
  return icons[type];
};

const formatDuration = (minutes: number) => {
  if (minutes < 1) {
    return '< 1 minute';
  } else if (minutes < 60) {
    return `${minutes} minute${minutes === 1 ? '' : 's'}`;
  } else {
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;
    if (remainingMinutes === 0) {
      return `${hours} hour${hours === 1 ? '' : 's'}`;
    }
    return `${hours}h ${remainingMinutes}m`;
  }
};

function getStatusBadge(status: Issue['status']) {
  const badges = {
    queued: (
      <Badge className='bg-gray-100 text-sm text-gray-800 dark:bg-gray-900/30 dark:text-gray-300'>
        <IconClock className='mr-1 h-3 w-3' />
        Queued
      </Badge>
    ),
    running: (
      <Badge className='bg-blue-100 text-sm text-blue-800 dark:bg-blue-900/30 dark:text-blue-300'>
        <IconPlayerPlay className='mr-1 h-3 w-3' />
        Running
      </Badge>
    ),
    completed: (
      <Badge className='bg-green-100 text-sm text-green-800 dark:bg-green-900/30 dark:text-green-300'>
        <IconCheck className='mr-1 h-3 w-3' />
        Completed
      </Badge>
    ),
    merged: (
      <Badge className='bg-purple-100 text-sm text-purple-800 dark:bg-purple-900/30 dark:text-purple-300'>
        <IconGitMerge className='mr-1 h-3 w-3' />
        Merged
      </Badge>
    )
  };
  return badges[status];
}

function getPriorityBadge(priority: Issue['priority']) {
  const badges = {
    low: (
      <Badge className='bg-yellow-100 px-2 py-0 text-sm text-yellow-800 dark:bg-green-900/30 dark:text-green-300'>
        <div className='flex items-center gap-1.5'>
          <SignalLow className='h-3 w-3' />
          <span>Low</span>
        </div>
      </Badge>
    ),
    medium: (
      <Badge className='bg-orange-100 px-2 py-0 text-sm text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300'>
        <div className='flex items-center gap-1.5'>
          <SignalMedium className='h-3 w-3' />
          <span>Medium</span>
        </div>
      </Badge>
    ),
    high: (
      <Badge className='bg-red-100 px-2 py-0 text-sm text-orange-800 dark:bg-orange-900/30 dark:text-orange-300'>
        <div className='flex items-center gap-1.5'>
          <SignalHigh className='h-3 w-3' />
          <span>High</span>
        </div>
      </Badge>
    )
  };
  return badges[priority];
}

export function IssueDetailsModal({
  issue,
  isVisible,
  onClose
}: IssueDetailsModalProps) {
  const [agentSteps] = useState(() => generateMockAgentSteps(issue));

  return (
    <div className='fixed inset-0 z-50 flex'>
      {/* Backdrop */}
      <div
        className={cn(
          'absolute inset-0 bg-black/50 transition-opacity duration-300',
          isVisible ? 'opacity-100' : 'opacity-0'
        )}
        onClick={onClose}
      />

      {/* Modal Content */}
      <div
        className={cn(
          'bg-background ml-auto h-full w-full max-w-5xl shadow-xl transition-transform duration-300 ease-in-out',
          isVisible ? 'translate-x-0' : 'translate-x-full'
        )}
      >
        <div className='flex h-full'>
          {/* Main Content */}
          <div className='flex-1 overflow-y-auto'>
            <div className='mx-auto px-8 py-8'>
              {/* Header with close button */}
              <div className='mb-8 flex items-start justify-between'>
                <div className='flex-1'>
                  <div className='mb-4 flex items-center gap-3'>
                    <h3 className='text-xl font-semibold'>{issue.name}</h3>
                  </div>
                </div>
                <Button
                  variant='ghost'
                  size='sm'
                  onClick={onClose}
                  className='shrink-0'
                >
                  <IconX className='h-4 w-4' />
                </Button>
              </div>

              <div className='space-y-8'>
                {/* Issue Summary */}
                <section id='issue-summary'>
                  <div className='mb-6'>
                    <div className='space-y-4 text-sm leading-relaxed'>
                      <Markdown content={issue.description} />
                    </div>
                  </div>
                </section>

                {/* Agent Activity */}
                <section id='agent-activity' className='border-t pt-6'>
                  <div className='mb-6'>
                    <div className='mb-4 flex items-center gap-3'>
                      <h3 className='text-md font-medium'>Activity</h3>
                    </div>
                  </div>

                  {/* Activity Timeline */}
                  <div className='space-y-0'>
                    {agentSteps.map((step, index) => {
                      const StepIcon = getStepIcon(step.type);
                      return (
                        <div key={step.id} className='flex gap-4'>
                          <div className='flex flex-col items-center'>
                            {step.status === 'completed' ? (
                              <div className='relative flex h-8 w-8 items-center justify-center rounded-full border-2'>
                                <IconCheck className='h-4 w-4 text-green-600' />
                              </div>
                            ) : step.status === 'running' ? (
                              <div className='h-8 w-8 animate-spin rounded-full border-2 border-t-transparent' />
                            ) : (
                              <div className='relative flex h-8 w-8 items-center justify-center rounded-full border-2'>
                                <StepIcon className='h-4 w-4 text-gray-400' />
                              </div>
                            )}
                            {index < agentSteps.length - 1 && (
                              <div className='min-h-[4rem] w-0.5 flex-1 bg-gray-200' />
                            )}
                          </div>

                          <div className='flex-1 pb-8'>
                            <div className='mb-2 flex items-center gap-2'>
                              <h4 className='text-sm font-medium'>
                                {step.title}
                              </h4>
                            </div>

                            <p className='mb-3 text-sm text-gray-600'>
                              {step.description}
                            </p>

                            <div className='flex items-center gap-4 text-xs text-gray-500'>
                              {step.duration && (
                                <span>{formatDuration(step.duration)}</span>
                              )}

                              {step.artifacts && step.artifacts.length > 0 && (
                                <div className='flex items-center gap-2'>
                                  {step.artifacts.map((artifact, i) => (
                                    <a
                                      key={i}
                                      href={artifact.url || '#'}
                                      className='inline-flex items-center gap-1 rounded bg-gray-100 px-2 py-1 text-xs text-gray-700 hover:bg-gray-200'
                                    >
                                      {artifact.type === 'code' && (
                                        <IconCode className='h-3 w-3' />
                                      )}
                                      {artifact.type === 'commit' && (
                                        <IconGitCommit className='h-3 w-3' />
                                      )}
                                      {artifact.type === 'test' && (
                                        <IconShield className='h-3 w-3' />
                                      )}
                                      {artifact.type === 'documentation' && (
                                        <IconMessageCircle className='h-3 w-3' />
                                      )}
                                      {artifact.name}
                                    </a>
                                  ))}
                                </div>
                              )}
                            </div>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </section>
              </div>
            </div>
          </div>

          {/* Sidebar */}
          <div className='bg-muted/30 w-64 flex-shrink-0 border-l'>
            <div className='sticky top-0 p-6'>
              <div>
                <div className='text-muted-foreground space-y-6 text-sm'>
                  <div>
                    <span className='font-medium'>Status:</span>
                    <div className='mt-1'>{getStatusBadge(issue.status)}</div>
                  </div>
                  <div>
                    <span className='font-medium'>Priority:</span>
                    <div className='mt-1'>
                      {getPriorityBadge(issue.priority)}
                    </div>
                  </div>
                  <div>
                    <span className='font-medium'>Trigger:</span>
                    <div className='mt-1'>{issue.triggerSource}</div>
                  </div>
                  <div>
                    <span className='font-medium'>Created:</span>
                    <div className='mt-1'>
                      {format(
                        new Date(issue.createdAt),
                        "MMM d, yyyy 'at' h:mm a"
                      )}
                    </div>
                  </div>
                  {issue.estimatedHours && (
                    <div>
                      <span className='font-medium'>Estimated Time:</span>
                      <div className='mt-1'>{issue.estimatedHours}h</div>
                    </div>
                  )}
                  <div>
                    <span className='font-medium'>Last Updated:</span>
                    <div className='mt-1'>
                      {formatDistanceToNow(new Date(issue.updatedAt), {
                        addSuffix: true
                      })}
                    </div>
                  </div>
                  {issue.githubPrLink && (
                    <div>
                      <span className='font-medium'>GitHub PR:</span>
                      <div className='mt-1'>
                        <a
                          href={issue.githubPrLink}
                          target='_blank'
                          rel='noopener noreferrer'
                          className='inline-flex items-center gap-1 text-blue-600 hover:text-blue-800'
                        >
                          #{issue.githubPrLink.split('/').pop()}
                          <IconExternalLink className='h-3 w-3' />
                        </a>
                      </div>
                    </div>
                  )}
                  <div>
                    <span className='font-medium'>Issue ID:</span>
                    <div className='mt-1 font-mono text-sm'>{issue.id}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
