'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Modal } from '@/components/ui/modal';
import { Loader2, Check, X } from 'lucide-react';

interface CreateOrgModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: (orgName: string, orgSlug: string) => void;
  loading?: boolean;
}

// Mock function to check slug availability
const checkSlugAvailability = async (slug: string): Promise<boolean> => {
  // Simulate API call delay
  await new Promise((resolve) => setTimeout(resolve, 500));

  // Mock some taken slugs
  const takenSlugs = ['acme', 'beta', 'gamma', 'test', 'admin', 'api'];
  return !takenSlugs.includes(slug.toLowerCase());
};

// Function to generate slug from organization name
const generateSlug = (name: string): string => {
  return name
    .toLowerCase()
    .trim()
    .replace(/[^a-z0-9\s-]/g, '') // Remove special characters
    .replace(/\s+/g, '-') // Replace spaces with hyphens
    .replace(/-+/g, '-') // Replace multiple hyphens with single hyphen
    .replace(/^-|-$/g, ''); // Remove leading/trailing hyphens
};

export const CreateOrgModal: React.FC<CreateOrgModalProps> = ({
  isOpen,
  onClose,
  onConfirm,
  loading = false
}) => {
  const [orgName, setOrgName] = useState('');
  const [orgSlug, setOrgSlug] = useState('');
  const [isCheckingSlug, setIsCheckingSlug] = useState(false);
  const [slugAvailable, setSlugAvailable] = useState<boolean | null>(null);
  const [slugTouched, setSlugTouched] = useState(false);

  // Auto-generate slug when org name changes
  useEffect(() => {
    if (orgName && !slugTouched) {
      const generatedSlug = generateSlug(orgName);
      setOrgSlug(generatedSlug);
    }
  }, [orgName, slugTouched]);

  // Check slug availability when slug changes
  useEffect(() => {
    if (orgSlug && orgSlug.length >= 2) {
      setIsCheckingSlug(true);
      setSlugAvailable(null);

      const timeoutId = setTimeout(async () => {
        try {
          const available = await checkSlugAvailability(orgSlug);
          setSlugAvailable(available);
        } catch (error) {
          console.error('Error checking slug availability:', error);
          setSlugAvailable(null);
        } finally {
          setIsCheckingSlug(false);
        }
      }, 300); // Debounce API calls

      return () => clearTimeout(timeoutId);
    } else {
      setSlugAvailable(null);
      setIsCheckingSlug(false);
    }
  }, [orgSlug]);

  const handleSlugChange = (value: string) => {
    setSlugTouched(true);
    const cleanSlug = generateSlug(value);
    setOrgSlug(cleanSlug);
  };

  const handleSubmit = () => {
    if (orgName.trim() && orgSlug.trim() && slugAvailable) {
      onConfirm(orgName.trim(), orgSlug.trim());
    }
  };

  const handleClose = () => {
    setOrgName('');
    setOrgSlug('');
    setSlugTouched(false);
    setSlugAvailable(null);
    setIsCheckingSlug(false);
    onClose();
  };

  const isFormValid =
    orgName.trim() && orgSlug.trim() && slugAvailable === true;

  return (
    <Modal
      title='Create Organization'
      description='Create a new organization to collaborate with your team.'
      isOpen={isOpen}
      onClose={handleClose}
    >
      <div className='space-y-4 py-4'>
        <div className='space-y-2'>
          <Label htmlFor='org-name'>Organization Name</Label>
          <Input
            id='org-name'
            placeholder='Enter organization name'
            value={orgName}
            onChange={(e) => setOrgName(e.target.value)}
            disabled={loading}
          />
        </div>

        <div className='space-y-2'>
          <Label htmlFor='org-slug'>Organization Slug</Label>
          <div className='relative'>
            <Input
              id='org-slug'
              placeholder='organization-slug'
              value={orgSlug}
              onChange={(e) => handleSlugChange(e.target.value)}
              disabled={loading}
              className='pr-8'
            />
            <div className='absolute top-1/2 right-2 -translate-y-1/2'>
              {isCheckingSlug && (
                <Loader2 className='text-muted-foreground h-4 w-4 animate-spin' />
              )}
              {!isCheckingSlug && slugAvailable === true && (
                <Check className='h-4 w-4 text-green-500' />
              )}
              {!isCheckingSlug && slugAvailable === false && (
                <X className='h-4 w-4 text-red-500' />
              )}
            </div>
          </div>
          {orgSlug && (
            <p className='text-muted-foreground text-sm'>
              Your organization will be available at:{' '}
              <code className='bg-muted rounded px-1 py-0.5 text-xs'>
                /{orgSlug}
              </code>
            </p>
          )}
          {slugAvailable === false && (
            <p className='text-sm text-red-500'>
              This slug is already taken. Please choose a different one.
            </p>
          )}
        </div>
      </div>

      <div className='flex justify-end space-x-2 pt-4'>
        <Button variant='outline' onClick={handleClose} disabled={loading}>
          Cancel
        </Button>
        <Button onClick={handleSubmit} disabled={loading || !isFormValid}>
          {loading && <Loader2 className='mr-2 h-4 w-4 animate-spin' />}
          Create Organization
        </Button>
      </div>
    </Modal>
  );
};
