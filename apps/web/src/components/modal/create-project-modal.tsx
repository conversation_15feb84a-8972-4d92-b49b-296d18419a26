'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Modal } from '@/components/ui/modal';
import { Loader2, Check, X } from 'lucide-react';

interface CreateProjectModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: (projectName: string, projectSlug: string) => void;
  loading?: boolean;
  orgSlug: string;
}

// Mock function to check if project slug is available within the organization
const checkProjectSlugAvailability = async (
  orgSlug: string,
  projectSlug: string
): Promise<boolean> => {
  // Simulate API call delay
  await new Promise((resolve) => setTimeout(resolve, 500));

  // Mock some taken project slugs per organization
  const takenProjectSlugs: Record<string, string[]> = {
    'backspace-inc': ['website-redesign', 'mobile-app', 'api-development'],
    'beta-corp': ['dashboard', 'analytics', 'reporting']
  };

  const orgProjects = takenProjectSlugs[orgSlug] || [];
  return !orgProjects.includes(projectSlug.toLowerCase());
};

// Function to generate slug from project name
const generateSlug = (name: string): string => {
  return name
    .toLowerCase()
    .trim()
    .replace(/[^a-z0-9\s-]/g, '') // Remove special characters
    .replace(/\s+/g, '-') // Replace spaces with hyphens
    .replace(/-+/g, '-') // Replace multiple hyphens with single hyphen
    .replace(/^-|-$/g, ''); // Remove leading/trailing hyphens
};

export const CreateProjectModal: React.FC<CreateProjectModalProps> = ({
  isOpen,
  onClose,
  onConfirm,
  loading = false,
  orgSlug
}) => {
  const [projectName, setProjectName] = useState('');
  const [projectSlug, setProjectSlug] = useState('');
  const [isCheckingSlug, setIsCheckingSlug] = useState(false);
  const [slugAvailable, setSlugAvailable] = useState<boolean | null>(null);
  const [slugTouched, setSlugTouched] = useState(false);

  // Auto-generate slug when project name changes
  useEffect(() => {
    if (projectName && !slugTouched) {
      const generatedSlug = generateSlug(projectName);
      setProjectSlug(generatedSlug);
    }
  }, [projectName, slugTouched]);

  // Check slug availability when slug changes
  useEffect(() => {
    if (projectSlug && projectSlug.length >= 2) {
      setIsCheckingSlug(true);
      setSlugAvailable(null);

      const timeoutId = setTimeout(async () => {
        try {
          const available = await checkProjectSlugAvailability(
            orgSlug,
            projectSlug
          );
          setSlugAvailable(available);
        } catch (error) {
          console.error('Error checking project slug availability:', error);
          setSlugAvailable(null);
        } finally {
          setIsCheckingSlug(false);
        }
      }, 300); // Debounce API calls

      return () => clearTimeout(timeoutId);
    } else {
      setSlugAvailable(null);
      setIsCheckingSlug(false);
    }
  }, [projectSlug, orgSlug]);

  const handleSlugChange = (value: string) => {
    setSlugTouched(true);
    const cleanSlug = generateSlug(value);
    setProjectSlug(cleanSlug);
  };

  const handleSubmit = () => {
    if (projectName.trim() && projectSlug.trim() && slugAvailable) {
      onConfirm(projectName.trim(), projectSlug.trim());
    }
  };

  const handleClose = () => {
    setProjectName('');
    setProjectSlug('');
    setSlugTouched(false);
    setSlugAvailable(null);
    setIsCheckingSlug(false);
    onClose();
  };

  const isFormValid =
    projectName.trim() && projectSlug.trim() && slugAvailable === true;

  return (
    <Modal
      title='Create Project'
      description={`Create a new project in your organization.`}
      isOpen={isOpen}
      onClose={handleClose}
    >
      <div className='space-y-4 py-4'>
        <div className='space-y-2'>
          <Label htmlFor='project-name'>Project Name</Label>
          <Input
            id='project-name'
            placeholder='Enter project name'
            value={projectName}
            onChange={(e) => setProjectName(e.target.value)}
            disabled={loading}
          />
        </div>

        <div className='space-y-2'>
          <Label htmlFor='project-slug'>Project Slug</Label>
          <div className='relative'>
            <Input
              id='project-slug'
              placeholder='project-slug'
              value={projectSlug}
              onChange={(e) => handleSlugChange(e.target.value)}
              disabled={loading}
              className='pr-8'
            />
            <div className='absolute top-1/2 right-2 -translate-y-1/2'>
              {isCheckingSlug && (
                <Loader2 className='text-muted-foreground h-4 w-4 animate-spin' />
              )}
              {!isCheckingSlug && slugAvailable === true && (
                <Check className='h-4 w-4 text-green-500' />
              )}
              {!isCheckingSlug && slugAvailable === false && (
                <X className='h-4 w-4 text-red-500' />
              )}
            </div>
          </div>
          {projectSlug && (
            <p className='text-muted-foreground text-sm'>
              Your project will be available at:{' '}
              <code className='bg-muted rounded px-1 py-0.5 text-xs'>
                /{orgSlug}/p/{projectSlug}
              </code>
            </p>
          )}
          {slugAvailable === false && (
            <p className='text-sm text-red-500'>
              This project slug is already taken in this organization. Please
              choose a different one.
            </p>
          )}
        </div>
      </div>

      <div className='flex justify-end space-x-2 pt-4'>
        <Button variant='outline' onClick={handleClose} disabled={loading}>
          Cancel
        </Button>
        <Button onClick={handleSubmit} disabled={loading || !isFormValid}>
          {loading && <Loader2 className='mr-2 h-4 w-4 animate-spin' />}
          Create Project
        </Button>
      </div>
    </Modal>
  );
};
