'use client';

import {
  Side<PERSON>,
  SidebarContent,
  SidebarGroup,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarTrigger,
  useSidebar
} from '@/components/ui/sidebar';
import {
  FilterPopover,
  FilterItem,
  CommandGroup
} from '@/components/ui/filter-popover';
import { BackspaceIcon } from '@/components/ui/backspace-icon';
import {
  FolderOpen,
  Monitor,
  Settings,
  BarChart3,
  AlertCircle,
  Kanban,
  Home,
  FolderGit2,
  ChevronsUpDown,
  Inbox
} from 'lucide-react';
import Link from 'next/link';
import { usePathname, useRouter } from 'next/navigation';
import * as React from 'react';

interface Org {
  id: string;
  name: string;
  slug: string;
}

interface Repo {
  id: number;
  name: string | null;
  url: string;
  org_id: number | null;
}

interface AppSidebarProps {
  orgs: Org[];
  repos: Repo[];
}

// Utility function to extract the short repo name (part after the "/")
function getShortRepoName(fullRepoName: string | null): string {
  if (!fullRepoName) return '';
  const parts = fullRepoName.split('/');
  return parts.length > 1 ? parts[parts.length - 1] : fullRepoName;
}

// Organization selector component
function OrgSelector({
  currentOrg,
  orgs,
  handleSwitchOrg
}: {
  currentOrg: any;
  orgs: any[];
  handleSwitchOrg: (orgId: string) => void;
}) {
  const handleOrgSelect = (orgId: string) => {
    handleSwitchOrg(orgId);
  };

  const orgTrigger = (
    <SidebarMenuButton className='cursor-pointer justify-between hover:bg-black/5 focus-visible:ring-transparent data-[state=open]:bg-black/10 hover:dark:bg-white/5 data-[state=open]:dark:bg-white/10'>
      <div className='flex items-center gap-2'>
        <BackspaceIcon className='size-4' />
        <span className='truncate font-medium'>{currentOrg.name}</span>
      </div>
      <ChevronsUpDown className='size-4 opacity-50' />
    </SidebarMenuButton>
  );

  return (
    <FilterPopover
      trigger={orgTrigger}
      searchPlaceholder='Search orgs...'
      contentWidth='w-56'
      align='start'
    >
      <CommandGroup>
        {orgs.map((org) => (
          <FilterItem
            key={org.id}
            label={org.name}
            isSelected={org.id === currentOrg.id}
            onToggle={() => handleOrgSelect(org.id)}
          />
        ))}
      </CommandGroup>
    </FilterPopover>
  );
}

// Repository selector component
function RepoSelector({
  currentRepo,
  repos,
  handleSwitchRepo
}: {
  currentRepo: any;
  repos: any[];
  handleSwitchRepo: (repoId: number) => void;
}) {
  const handleRepoSelect = (repoId: number) => {
    handleSwitchRepo(repoId);
  };

  const repoTrigger = (
    <SidebarMenuButton className='cursor-pointer justify-between hover:bg-black/5 focus-visible:ring-transparent data-[state=open]:bg-black/10 hover:dark:bg-white/5 data-[state=open]:dark:bg-white/10'>
      <div className='flex items-center gap-2'>
        <FolderGit2 className='size-4' />
        <span className='truncate font-medium'>
          {getShortRepoName(currentRepo.name)}
        </span>
      </div>
      <ChevronsUpDown className='size-4 opacity-50' />
    </SidebarMenuButton>
  );

  return (
    <FilterPopover
      trigger={repoTrigger}
      searchPlaceholder='Search repos...'
      contentWidth='w-56'
      align='start'
    >
      <CommandGroup>
        {repos.map((repo) => (
          <FilterItem
            key={repo.id}
            label={getShortRepoName(repo.name)}
            isSelected={repo.id === currentRepo.id}
            onToggle={() => handleRepoSelect(repo.id)}
          />
        ))}
      </CommandGroup>
    </FilterPopover>
  );
}

export default function AppSidebar({ orgs, repos }: AppSidebarProps) {
  const pathname = usePathname();
  const router = useRouter();
  const { state } = useSidebar();

  // Handle null/undefined arrays
  const safeOrgs = orgs || [];
  const safeRepos = repos || [];

  // Extract current org and repo from pathname
  const pathSegments = pathname.split('/').filter(Boolean);
  const currentOrgSlug = pathSegments[0] || safeOrgs[0]?.slug || '';
  const currentRepoName =
    pathSegments[1] === 'repo' && pathSegments[2]
      ? pathSegments[2]
      : getShortRepoName(safeRepos[0]?.name) || '';

  // Find current org and repo
  const currentOrg =
    safeOrgs.find((org) => org.slug === currentOrgSlug) || safeOrgs[0];
  const currentRepo =
    safeRepos.find((repo) => getShortRepoName(repo.name) === currentRepoName) ||
    safeRepos[0];

  // Handle cases where no org or repo is found
  if (!currentOrg) {
    return null; // or some loading/error state
  }

  // Navigation items
  const mainNavItems = [
    { title: 'Inbox', url: `/${currentOrgSlug}/inbox`, icon: Inbox },
    {
      title: 'Repositories',
      url: `/${currentOrgSlug}/repositories`,
      icon: FolderOpen
    },
    { title: 'Monitor', url: `/${currentOrgSlug}/monitor`, icon: Monitor },
    {
      title: 'Settings',
      url: `/${currentOrgSlug}/settings/members`,
      icon: Settings
    }
  ];

  const repoNavItems = currentRepo
    ? [
        {
          title: 'Overview',
          url: `/${currentOrgSlug}/repo/${getShortRepoName(currentRepo.name)}/overview`,
          icon: Home
        },
        {
          title: 'Scans',
          url: `/${currentOrgSlug}/repo/${getShortRepoName(currentRepo.name)}/scans`,
          icon: BarChart3
        },
        {
          title: 'Incidents',
          url: `/${currentOrgSlug}/repo/${getShortRepoName(currentRepo.name)}/incidents`,
          icon: AlertCircle
        },
        {
          title: 'Issues',
          url: `/${currentOrgSlug}/repo/${getShortRepoName(currentRepo.name)}/issues`,
          icon: Kanban
        }
      ]
    : [];

  // Simplified handlers
  const handleSwitchOrg = (orgId: string) => {
    const org = safeOrgs.find((o) => o.id === orgId);
    if (org) {
      router.push(
        `/${org.slug}/repo/${getShortRepoName(currentRepo?.name)}/overview`
      );
    }
  };

  const handleSwitchRepo = (repoId: number) => {
    const repo = safeRepos.find((p) => p.id === repoId);
    if (repo) {
      router.push(
        `/${currentOrgSlug}/repo/${getShortRepoName(repo.name)}/overview`
      );
    }
  };

  const isActivePath = (item: { title: string; url: string }) => {
    if (item.title === 'Settings') return pathname.includes('/settings');
    if (item.title === 'Scans') return pathname.includes('/scans');
    return pathname === item.url;
  };

  return (
    <Sidebar collapsible='icon' className='border-sidebar-accent border-r'>
      <SidebarHeader>
        <SidebarMenu>
          <SidebarMenuItem>
            <div className='flex items-center gap-2'>
              {state === 'expanded' && (
                <OrgSelector
                  currentOrg={currentOrg}
                  orgs={safeOrgs}
                  handleSwitchOrg={handleSwitchOrg}
                />
              )}
              <SidebarTrigger className='size-8 p-0' />
            </div>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarHeader>

      <SidebarContent className='overflow-x-hidden'>
        {/* Main Navigation */}
        <SidebarGroup>
          <SidebarMenu className='space-y-0'>
            {mainNavItems.map((item) => (
              <SidebarMenuItem key={item.title}>
                <SidebarMenuButton
                  asChild
                  tooltip={item.title}
                  isActive={isActivePath(item)}
                  className='hover:bg-black/5 data-[active=true]:bg-black/10 hover:dark:bg-white/5 data-[active=true]:dark:bg-white/10'
                >
                  <Link
                    href={item.url}
                    className='hover:[&>span]:opacity-100 data-[active=true]:[&>span]:opacity-100 hover:[&>svg]:opacity-100 data-[active=true]:[&>svg]:opacity-100'
                  >
                    <item.icon className='size-4 opacity-50 transition-opacity duration-100' />
                    <span className='font-medium opacity-50 transition-opacity duration-100'>
                      {item.title}
                    </span>
                  </Link>
                </SidebarMenuButton>
              </SidebarMenuItem>
            ))}
          </SidebarMenu>
        </SidebarGroup>

        {/* Repository Section */}
        {currentRepo && (
          <SidebarGroup>
            <SidebarGroupLabel>Repository</SidebarGroupLabel>

            {/* Repository Selector */}
            <SidebarMenu className='space-y-0'>
              <SidebarMenuItem>
                <RepoSelector
                  currentRepo={currentRepo}
                  repos={safeRepos}
                  handleSwitchRepo={handleSwitchRepo}
                />
              </SidebarMenuItem>
            </SidebarMenu>

            {/* Repository Navigation Items */}
            <SidebarMenu className='mt-2'>
              {repoNavItems.map((item) => (
                <SidebarMenuItem key={item.title}>
                  <SidebarMenuButton
                    asChild
                    tooltip={item.title}
                    isActive={isActivePath(item)}
                    className='hover:bg-black/5 data-[active=true]:bg-black/10 hover:dark:bg-white/5 data-[active=true]:dark:bg-white/10'
                  >
                    <Link
                      href={item.url}
                      className='hover:[&>span]:opacity-100 data-[active=true]:[&>span]:opacity-100 hover:[&>svg]:opacity-100 data-[active=true]:[&>svg]:opacity-100'
                    >
                      <item.icon className='size-4 opacity-50 transition-opacity duration-200' />
                      <span className='font-medium opacity-50 transition-opacity duration-200'>
                        {item.title}
                      </span>
                    </Link>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              ))}
            </SidebarMenu>
          </SidebarGroup>
        )}
      </SidebarContent>
    </Sidebar>
  );
}
