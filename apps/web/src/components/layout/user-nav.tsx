'use client';
import { useEffect, useState } from 'react';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuSub,
  DropdownMenuSubContent,
  DropdownMenuSubTrigger,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';
import { UserAvatarProfile } from '@/components/user-avatar-profile';
import { Icons } from '@/components/icons';
import { createClient } from '@/lib/supabase/client';
import { useRouter, useParams } from 'next/navigation';
import { useTheme } from 'next-themes';
import { Check } from 'lucide-react';
import type { User } from '@supabase/supabase-js';

export function UserNav() {
  const [user, setUser] = useState<User | null>(null);
  const router = useRouter();
  const params = useParams();
  const { theme, setTheme } = useTheme();

  // Get the current org slug from URL parameters
  const orgSlug = params['org-slug'] as string;

  useEffect(() => {
    const supabase = createClient();

    // Get initial session
    supabase.auth.getSession().then(({ data: { session } }) => {
      setUser(session?.user ?? null);
    });

    // Listen for auth changes
    const {
      data: { subscription }
    } = supabase.auth.onAuthStateChange((_event, session) => {
      setUser(session?.user ?? null);
    });

    return () => subscription.unsubscribe();
  }, []);

  const handleSignOut = async () => {
    const supabase = createClient();
    await supabase.auth.signOut();
    router.push('/auth/login');
  };

  if (user) {
    return (
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant='ghost'
            className='hover:bg-transparent focus-visible:ring-transparent'
          >
            <UserAvatarProfile user={user} className='size-6' />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent
          className='w-56'
          align='end'
          sideOffset={10}
          forceMount
        >
          <DropdownMenuLabel className='font-normal'>
            <div className='flex flex-col space-y-1'>
              <p className='text-sm leading-none font-medium'>
                {user.user_metadata?.full_name || user.email?.split('@')[0]}
              </p>
              <p className='text-muted-foreground text-xs leading-none'>
                {user.email}
              </p>
            </div>
          </DropdownMenuLabel>
          <DropdownMenuSeparator />
          <DropdownMenuGroup>
            <DropdownMenuItem
              className='cursor-pointer gap-2'
              onClick={() => router.push(`/${orgSlug}/settings/members`)}
            >
              <Icons.settings className='h-4 w-4' />
              Settings
              <Icons.chevronRight className='ml-auto h-4 w-4' />
            </DropdownMenuItem>
            <DropdownMenuSub>
              <DropdownMenuSubTrigger className='gap-2'>
                <Icons.laptop className='h-4 w-4' />
                Appearance
              </DropdownMenuSubTrigger>
              <DropdownMenuSubContent>
                <DropdownMenuItem
                  className='cursor-pointer gap-2'
                  onClick={() => setTheme('light')}
                >
                  <Icons.sun className='h-4 w-4' />
                  Light
                  {theme === 'light' && <Check className='ml-auto h-4 w-4' />}
                </DropdownMenuItem>
                <DropdownMenuItem
                  className='cursor-pointer gap-2'
                  onClick={() => setTheme('dark')}
                >
                  <Icons.moon className='h-4 w-4' />
                  Dark
                  {theme === 'dark' && <Check className='ml-auto h-4 w-4' />}
                </DropdownMenuItem>
                <DropdownMenuItem
                  className='cursor-pointer gap-2'
                  onClick={() => setTheme('system')}
                >
                  <Icons.laptop className='h-4 w-4' />
                  System
                  {theme === 'system' && <Check className='ml-auto h-4 w-4' />}
                </DropdownMenuItem>
              </DropdownMenuSubContent>
            </DropdownMenuSub>
          </DropdownMenuGroup>
          <DropdownMenuSeparator />
          <DropdownMenuItem
            className='cursor-pointer gap-2'
            onClick={handleSignOut}
          >
            <Icons.login className='h-4 w-4' />
            Sign out
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    );
  }
}
