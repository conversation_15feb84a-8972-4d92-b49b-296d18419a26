import {
  IconShield,
  IconBulb,
  IconCube,
  IconBolt,
  IconCpu
} from '@tabler/icons-react';

// Metrics configuration matching analytics-timeline
export const METRICS_CONFIG = {
  testCoverage: {
    label: 'Test Coverage',
    color: 'hsl(142 76% 36%)',
    description: 'How protected is the codebase against regressions?',
    icon: IconShield
  },
  clarity: {
    label: 'Clarity',
    color: 'hsl(221 83% 53%)',
    description:
      'How fast can contributors understand and navigate the codebase?',
    icon: IconBulb
  },
  modularity: {
    label: 'Modularity',
    color: 'hsl(271 81% 56%)',
    description: 'How reduced is the risk of unintended consequences?',
    icon: IconCube
  },
  security: {
    label: 'Security',
    color: 'hsl(12 76% 61%)',
    description: 'How secure is your codebase against vulnerabilities?',
    icon: IconShield
  },
  performance: {
    label: 'Performance',
    color: 'hsl(173 58% 39%)',
    description: 'How closely are performance best practices being followed?',
    icon: IconBolt
  },
  faultResilience: {
    label: 'Fault Resilience',
    color: 'hsl(43 74% 49%)',
    description: 'How resilient is production to errors and outages?',
    icon: IconCpu
  },
  agentReadiness: {
    label: 'Agent Readiness',
    color: 'hsl(262 83% 58%)',
    description: 'How well can agents operate in your codebase?',
    icon: IconCpu
  }
} as const;

export interface User {
  name: string;
  avatar?: string;
  initials: string;
}

export interface Issue {
  id: string;
  type: keyof typeof METRICS_CONFIG;
  severity: 'critical' | 'high' | 'medium' | 'low';
  title: string;
  description: string;
  file: string;
  line?: number;
  recommendation: string;
  effort: 'low' | 'medium' | 'high';
  impact: number;
  codeSnippet?: string;
  relatedFiles?: string[];
}

export interface Scan {
  id: string;
  name: string;
  description: string;
  timestamp: string;
  timestampMs: number;
  duration: string;
  status: 'completed' | 'running' | 'failed';
  overallScore: number;
  previousScore?: number;
  metrics: Record<keyof typeof METRICS_CONFIG, number>;
  issues: Issue[];
  filesAnalyzed: number;
  linesOfCode: number;
  tokenCost: number;
  promptTokens: number;
  completionTokens: number;
  totalTokens: number;
  creator?: User;
  triggerReason: 'manual' | 'scheduled' | 'push' | 'pr';
  configuration: {
    scanType: string;
    includeTests: boolean;
    includeDependencies: boolean;
    maxFileSize: string;
    excludePatterns: string[];
  };
  executionDetails: {
    startTime: string;
    endTime: string;
    executionEnvironment: string;
    agentVersion: string;
    rulesVersion: string;
  };
}

export interface AgentTicket {
  id: string;
  title: string;
  description: string;
  metric: keyof typeof METRICS_CONFIG;
  priority: 'critical' | 'high' | 'medium' | 'low';
  estimatedEffort: string;
  dependencies: string[];
  status: 'queued' | 'assigned' | 'in_progress' | 'completed' | 'failed';
  assignedAgent?: string;
  files: string[];
}

export type ColumnKey =
  | 'status'
  | 'trigger'
  | 'description'
  | 'issues'
  | 'duration'
  | 'cost'
  | 'creator'
  | 'timestamp';

export type VisibleColumns = Record<ColumnKey, boolean>;

// Users pool
export const USERS = [
  {
    name: 'Sarah Chen',
    initials: 'SC',
    avatar: 'https://api.slingacademy.com/public/sample-users/1.png'
  },
  {
    name: 'Alex Rodriguez',
    initials: 'AR',
    avatar: 'https://api.slingacademy.com/public/sample-users/2.png'
  },
  {
    name: 'Jordan Kim',
    initials: 'JK',
    avatar: 'https://api.slingacademy.com/public/sample-users/3.png'
  },
  {
    name: 'Maya Patel',
    initials: 'MP',
    avatar: 'https://api.slingacademy.com/public/sample-users/4.png'
  },
  {
    name: 'Chris Taylor',
    initials: 'CT',
    avatar: 'https://api.slingacademy.com/public/sample-users/5.png'
  }
];

export const SCAN_TYPES = [
  'Security Vulnerability Scan',
  'Code Quality Analysis',
  'Performance Review',
  'Test Coverage Analysis',
  'Dependency Audit',
  'Architecture Review',
  'Documentation Check',
  'Best Practices Audit'
];

export const TRIGGER_REASONS: Array<'manual' | 'scheduled' | 'push' | 'pr'> = [
  'manual',
  'scheduled',
  'push',
  'pr'
];
