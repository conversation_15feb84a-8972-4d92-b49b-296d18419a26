import { METRICS_CONFIG, type Scan } from '@/constants/scans';

export function getScanDescription(scan: Scan): string {
  const criticalIssues = scan.issues.filter(
    (i) => i.severity === 'critical'
  ).length;
  const highIssues = scan.issues.filter((i) => i.severity === 'high').length;
  const lowMetrics = Object.entries(scan.metrics).filter(
    ([, value]) => value < 70
  );

  if (criticalIssues > 0) {
    return `${criticalIssues} critical security/performance issues found`;
  } else if (highIssues > 0) {
    return `${highIssues} high-priority issues affecting code quality`;
  } else if (lowMetrics.length > 0) {
    const metricNames = lowMetrics
      .map(([key]) => METRICS_CONFIG[key as keyof typeof METRICS_CONFIG].label)
      .slice(0, 2)
      .join(', ');
    return `Low scores in ${metricNames}${lowMetrics.length > 2 ? ` +${lowMetrics.length - 2} more` : ''}`;
  } else if (scan.issues.length > 0) {
    return `${scan.issues.length} minor issues, overall good code quality`;
  } else {
    return 'No issues found, excellent code quality';
  }
}

export function getMetricSummaryText(
  key: string,
  value: number,
  totalCount: number
): string {
  const metricTexts = {
    testCoverage:
      totalCount === 0
        ? `Test coverage analysis shows strong fundamentals at ${Math.round(value)}%. While this represents solid protection against regressions, there are strategic opportunities to reach industry-leading coverage levels and enhance testing practices.`
        : `Analysis of your test coverage reveals ${totalCount > 0 ? 'opportunities for improvement' : 'excellent practices'} in protecting your codebase against regressions. Current coverage at ${Math.round(value)}% provides a good foundation with ${totalCount} areas identified for enhancement.`,

    clarity:
      totalCount === 0
        ? `Code clarity assessment shows excellent practices at ${Math.round(value)}%. The codebase demonstrates strong readability and maintainability. Focus areas include advanced documentation patterns and consistency optimizations for team scalability.`
        : `Code clarity assessment shows ${totalCount} opportunities to enhance contributor understanding and codebase navigability. Current clarity score of ${Math.round(value)}% indicates ${value > 80 ? 'strong' : value > 65 ? 'good' : 'developing'} practices with clear improvement paths.`,

    modularity:
      totalCount === 0
        ? `Modularity analysis reveals excellent architectural design at ${Math.round(value)}%. The codebase demonstrates strong separation of concerns and minimal coupling. Opportunities exist for advanced patterns and future-proofing strategies.`
        : `Modularity analysis reveals ${totalCount} instances of coupling patterns that could be optimized to reduce the risk of unintended consequences. Current modularity score of ${Math.round(value)}% shows ${value > 80 ? 'strong' : value > 65 ? 'moderate' : 'developing'} architectural practices.`,

    security:
      totalCount === 0
        ? `Security analysis demonstrates robust protection at ${Math.round(value)}%. The application follows strong security practices with comprehensive protection measures. Opportunities include advanced threat modeling and security automation enhancements.`
        : `Security analysis identified ${totalCount} areas requiring attention to strengthen your application's security posture. Current security score of ${Math.round(value)}% indicates ${value > 85 ? 'strong' : value > 70 ? 'good' : 'developing'} protection with clear improvement opportunities.`,

    performance:
      totalCount === 0
        ? `Performance analysis shows excellent optimization at ${Math.round(value)}%. The application demonstrates strong performance characteristics with efficient resource utilization. Focus areas include advanced optimization techniques and scalability enhancements.`
        : `Performance analysis shows ${totalCount} optimization opportunities to enhance application speed and efficiency. Current performance score of ${Math.round(value)}% reflects ${value > 85 ? 'excellent' : value > 70 ? 'good' : 'developing'} optimization practices.`,

    faultResilience:
      totalCount === 0
        ? `Fault resilience assessment demonstrates robust error handling at ${Math.round(value)}%. The system shows strong recovery capabilities and graceful degradation. Enhancement opportunities include advanced resilience patterns and chaos engineering practices.`
        : `Fault resilience assessment reveals ${totalCount} opportunities to strengthen your production error handling and recovery capabilities. Current resilience score of ${Math.round(value)}% indicates ${value > 85 ? 'excellent' : value > 70 ? 'good' : 'developing'} fault tolerance.`,

    agentReadiness:
      totalCount === 0
        ? `Agent readiness analysis shows excellent AI integration potential at ${Math.round(value)}%. The codebase is well-structured for AI tooling and automation. Opportunities include advanced AI patterns and next-generation development workflows.`
        : `Agent readiness analysis shows ${totalCount} opportunities to optimize your codebase for AI tooling and automation. Current readiness score of ${Math.round(value)}% reflects ${value > 80 ? 'excellent' : value > 65 ? 'good' : 'developing'} AI integration potential.`
  };

  return metricTexts[key as keyof typeof metricTexts] || '';
}
