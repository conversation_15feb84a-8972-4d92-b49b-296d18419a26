import { format } from 'date-fns';
import {
  METRICS_CONFIG,
  SCAN_TYPES,
  TRIGGER_REASONS,
  USERS,
  type Scan,
  type Issue,
  type AgentTicket
} from '@/constants/scans';

const ISSUE_TEMPLATES = {
  testCoverage: [
    {
      title: 'Missing unit tests for core functions',
      description: 'Critical business logic lacks proper test coverage',
      recommendation: 'Add comprehensive unit tests for all public methods',
      codeSnippet:
        'export function processPayment(amount: number) {\n  // Missing tests\n  return amount * 1.05;\n}'
    },
    {
      title: 'Integration tests missing for API endpoints',
      description: 'API endpoints lack integration test coverage',
      recommendation: 'Implement end-to-end API testing',
      codeSnippet:
        'app.post("/api/payment", async (req, res) => {\n  // No integration tests\n});'
    },
    {
      title: 'Edge case scenarios not tested',
      description: 'Error handling and edge cases lack test coverage',
      recommendation: 'Add tests for error conditions and edge cases',
      codeSnippet:
        'if (user.balance < amount) {\n  // Edge case not tested\n  throw new Error();\n}'
    }
  ],
  security: [
    {
      title: 'Potential SQL injection vulnerability',
      description: 'User input not properly sanitized in database queries',
      recommendation: 'Use parameterized queries to prevent SQL injection',
      codeSnippet:
        "const query = `SELECT * FROM users WHERE email = '${email}'`;\n// Vulnerable to SQL injection"
    },
    {
      title: 'Missing input validation',
      description: 'API endpoints lack proper input validation',
      recommendation: 'Implement comprehensive input validation',
      codeSnippet:
        'app.post("/api/user", (req, res) => {\n  // No input validation\n  const user = req.body;\n});'
    },
    {
      title: 'Insecure session management',
      description: 'Session tokens lack proper security measures',
      recommendation: 'Implement secure session management',
      codeSnippet:
        'const token = Math.random().toString();\n// Insecure token generation'
    }
  ],
  performance: [
    {
      title: 'Inefficient database query pattern',
      description: 'N+1 query pattern detected causing performance issues',
      recommendation: 'Implement query batching to reduce database calls',
      codeSnippet:
        'users.forEach(user => {\n  // N+1 query\n  const profile = await getUserProfile(user.id);\n});'
    },
    {
      title: 'Large bundle size detected',
      description: 'JavaScript bundles are larger than recommended',
      recommendation: 'Implement code splitting and lazy loading',
      codeSnippet:
        'import * as LargeLibrary from "massive-library";\n// Bundle bloat'
    },
    {
      title: 'Unoptimized image loading',
      description: 'Images loaded without optimization',
      recommendation: 'Implement image optimization and lazy loading',
      codeSnippet: '<img src="large-image.jpg" />\n// No optimization'
    }
  ],
  clarity: [
    {
      title: 'Complex function with high cognitive complexity',
      description: 'Function has too many nested conditions',
      recommendation: 'Break down into smaller, focused functions',
      codeSnippet:
        'function processData(data: any[]) {\n  // 50+ lines of nested conditions\n  if (data && data.length > 0) {\n    // ...'
    },
    {
      title: 'Unclear variable naming',
      description: 'Variables and functions have unclear names',
      recommendation: 'Use descriptive, intention-revealing names',
      codeSnippet:
        'const d = new Date();\nconst x = calc(d);\n// Unclear naming'
    },
    {
      title: 'Missing documentation',
      description: 'Complex business logic lacks documentation',
      recommendation: 'Add comprehensive code documentation',
      codeSnippet:
        'function calculateTax(amount, rate) {\n  // No documentation\n  return amount * rate * 0.87;\n}'
    }
  ],
  modularity: [
    {
      title: 'Tight coupling between modules',
      description: 'High interdependency makes changes risky',
      recommendation: 'Implement dependency injection pattern',
      codeSnippet:
        'import { PaymentGateway } from "../payment";\n// Direct dependency'
    },
    {
      title: 'Circular dependencies detected',
      description: 'Modules have circular import dependencies',
      recommendation: 'Refactor to eliminate circular dependencies',
      codeSnippet:
        '// moduleA.ts imports moduleB.ts\n// moduleB.ts imports moduleA.ts'
    },
    {
      title: 'God object anti-pattern',
      description: 'Single class/module doing too many things',
      recommendation: 'Split responsibilities into focused modules',
      codeSnippet:
        'class ApplicationManager {\n  // 500+ lines handling everything\n}'
    }
  ],
  faultResilience: [
    {
      title: 'Missing error handling for API calls',
      description: 'Network calls lack proper error handling',
      recommendation: 'Implement circuit breaker and retry logic',
      codeSnippet: 'const response = await fetch(apiUrl);\n// No error handling'
    },
    {
      title: 'No graceful degradation',
      description: 'Application fails completely on errors',
      recommendation: 'Implement graceful degradation strategies',
      codeSnippet:
        'if (apiData) {\n  render(apiData);\n} else {\n  // No fallback\n}'
    },
    {
      title: 'Missing monitoring and alerting',
      description: 'Insufficient error monitoring in production',
      recommendation: 'Add comprehensive error tracking',
      codeSnippet:
        'try {\n  riskyOperation();\n} catch (e) {\n  // Silent failure\n}'
    }
  ],
  agentReadiness: [
    {
      title: 'Insufficient code documentation',
      description: 'Complex algorithms lack documentation',
      recommendation: 'Add comprehensive JSDoc comments',
      codeSnippet:
        'function calculateRoute(points) {\n  // No documentation\n  return points.sort();\n}'
    },
    {
      title: 'Missing type annotations',
      description: 'Functions lack proper type definitions',
      recommendation: 'Add comprehensive TypeScript types',
      codeSnippet:
        'function processData(data) {\n  // No type annotations\n  return data.map(item => item.value);\n}'
    },
    {
      title: 'Complex interfaces for AI navigation',
      description: 'API interfaces are too complex for agent understanding',
      recommendation: 'Simplify and document API interfaces',
      codeSnippet:
        'interface ComplexConfig {\n  // 50+ properties without docs\n}'
    }
  ]
};

function generateMetrics(): Record<keyof typeof METRICS_CONFIG, number> {
  return {
    testCoverage: 60 + Math.random() * 30,
    clarity: 55 + Math.random() * 35,
    modularity: 50 + Math.random() * 40,
    security: 70 + Math.random() * 25,
    performance: 60 + Math.random() * 30,
    faultResilience: 55 + Math.random() * 35,
    agentReadiness: 45 + Math.random() * 45
  };
}

function generateIssues(
  metrics: Record<keyof typeof METRICS_CONFIG, number>,
  scanIndex: number
): Issue[] {
  const issues: Issue[] = [];
  let issueId = 0;

  Object.entries(metrics).forEach(([metricKey, value]) => {
    const issueCount =
      value < 70
        ? Math.floor(Math.random() * 3) + 2
        : Math.floor(Math.random() * 2) + 1;

    for (let j = 0; j < issueCount; j++) {
      const severity =
        value <= 50
          ? 'critical'
          : value <= 65
            ? 'high'
            : value <= 80
              ? 'medium'
              : 'low';

      const templates =
        ISSUE_TEMPLATES[metricKey as keyof typeof ISSUE_TEMPLATES];
      const template = templates[j % templates.length];

      issues.push({
        id: `${scanIndex}-${issueId++}`,
        type: metricKey as keyof typeof METRICS_CONFIG,
        severity: severity as 'critical' | 'high' | 'medium' | 'low',
        title: template.title,
        description: template.description,
        file: `src/components/${metricKey}-${j + 1}.tsx`,
        line: Math.floor(Math.random() * 200) + 1,
        recommendation: template.recommendation,
        effort: ['low', 'medium', 'high'][Math.floor(Math.random() * 3)] as
          | 'low'
          | 'medium'
          | 'high',
        impact:
          Math.round((100 - value) * Math.random()) +
          Math.floor(Math.random() * 20),
        codeSnippet: template.codeSnippet,
        relatedFiles: [
          `src/tests/${metricKey}-${j + 1}Test.ts`,
          `src/types/${metricKey}Types.ts`
        ]
      });
    }
  });

  return issues;
}

function generateScanDetails(timestamp: Date, scanIndex: number) {
  const filesAnalyzed = Math.floor(Math.random() * 500) + 100;
  const linesOfCode = filesAnalyzed * (Math.floor(Math.random() * 50) + 25);
  const promptTokens = Math.floor(linesOfCode * 0.8);
  const completionTokens = Math.floor(promptTokens * 0.3);
  const totalTokens = promptTokens + completionTokens;
  const tokenCost = promptTokens * 0.001 + completionTokens * 0.002;
  const duration = `${Math.floor(Math.random() * 180) + 30}s`;
  const durationSeconds = parseInt(duration.replace('s', ''));
  const timestampMs = timestamp.getTime();
  const startTime = new Date(timestampMs);
  const endTime = new Date(timestampMs + durationSeconds * 1000);

  return {
    filesAnalyzed,
    linesOfCode,
    tokenCost,
    promptTokens,
    completionTokens,
    totalTokens,
    duration,
    startTime: format(startTime, 'yyyy-MM-dd HH:mm:ss'),
    endTime: format(endTime, 'yyyy-MM-dd HH:mm:ss')
  };
}

export function generateMockScans(): Scan[] {
  const baseTime = Date.now();
  const scans: Scan[] = [];

  for (let i = 0; i < 50; i++) {
    const timestamp = new Date(baseTime - i * 6 * 60 * 60 * 1000);
    const timestampMs = timestamp.getTime();
    const scanType = SCAN_TYPES[i % SCAN_TYPES.length];
    const triggerReason = TRIGGER_REASONS[i % TRIGGER_REASONS.length];

    const metrics = generateMetrics();
    const overallScore = Math.round(
      Object.values(metrics).reduce((sum, val) => sum + val, 0) / 7
    );

    const issues = generateIssues(metrics, i);
    const details = generateScanDetails(timestamp, i);

    scans.push({
      id: `scan-${i + 1}`,
      name: scanType,
      description: `Comprehensive ${scanType.toLowerCase()} analyzing ${details.filesAnalyzed} files with ${details.linesOfCode.toLocaleString()} lines of code. This automated assessment evaluates code quality, identifies improvement opportunities, and provides actionable recommendations for enhanced maintainability and performance.`,
      timestamp: format(timestamp, 'MMM d, HH:mm'),
      timestampMs,
      duration: details.duration,
      status: i === 1 ? 'running' : i === 7 ? 'failed' : 'completed',
      overallScore,
      previousScore: i > 0 ? scans[i - 1]?.overallScore : undefined,
      metrics,
      issues,
      filesAnalyzed: details.filesAnalyzed,
      linesOfCode: details.linesOfCode,
      tokenCost: details.tokenCost,
      promptTokens: details.promptTokens,
      completionTokens: details.completionTokens,
      totalTokens: details.totalTokens,
      creator: triggerReason === 'manual' ? USERS[i % USERS.length] : undefined,
      triggerReason,
      configuration: {
        scanType: scanType,
        includeTests: Math.random() > 0.5,
        includeDependencies: triggerReason !== 'pr',
        maxFileSize: '1MB',
        excludePatterns: ['node_modules/**', '*.test.ts', 'dist/**']
      },
      executionDetails: {
        startTime: details.startTime,
        endTime: details.endTime,
        executionEnvironment: 'cloud-runner-v2.1',
        agentVersion: '1.4.2',
        rulesVersion: '2024.12.1'
      }
    });
  }

  return scans.sort((a, b) => b.timestampMs - a.timestampMs);
}

export function generateAgentTickets(scan: Scan): AgentTicket[] {
  const tickets: AgentTicket[] = [];
  let ticketId = 0;

  Object.entries(METRICS_CONFIG).forEach(([metricKey, metricConfig]) => {
    const metricIssues = scan.issues.filter(
      (issue) => issue.type === metricKey
    );
    const metricValue = scan.metrics[metricKey as keyof typeof METRICS_CONFIG];

    // Create enhancement tickets for metrics without critical issues
    if (metricIssues.length === 0 && metricValue < 90) {
      tickets.push({
        id: `ticket-${ticketId++}`,
        title: `${metricConfig.label} Enhancement Opportunities`,
        description: `Proactive improvements to enhance ${metricConfig.label.toLowerCase()} from ${Math.round(metricValue)}% to industry-leading standards`,
        metric: metricKey as keyof typeof METRICS_CONFIG,
        priority: metricValue < 70 ? 'medium' : 'low',
        estimatedEffort: metricValue < 70 ? '2-3 days' : '1-2 days',
        dependencies: [],
        status: 'queued',
        assignedAgent: `${metricConfig.label} Enhancement Agent`,
        files: [
          `src/components/${metricKey}.tsx`,
          `src/tests/${metricKey}Test.ts`
        ]
      });
      return;
    }

    // Group issues by severity
    const criticalIssues = metricIssues.filter(
      (i) => i.severity === 'critical'
    );
    const highIssues = metricIssues.filter((i) => i.severity === 'high');
    const mediumLowIssues = metricIssues.filter((i) =>
      ['medium', 'low'].includes(i.severity)
    );

    // Create tickets for each severity group
    if (criticalIssues.length > 0) {
      criticalIssues.forEach((issue) => {
        tickets.push({
          id: `ticket-${ticketId++}`,
          title: `[URGENT] ${issue.title}`,
          description: issue.description,
          metric: metricKey as keyof typeof METRICS_CONFIG,
          priority: 'critical',
          estimatedEffort:
            issue.effort === 'low'
              ? '2-4 hours'
              : issue.effort === 'medium'
                ? '1-2 days'
                : '3-5 days',
          dependencies: issue.relatedFiles || [],
          status: 'queued',
          assignedAgent: `${metricConfig.label} Specialist Agent`,
          files: [issue.file, ...(issue.relatedFiles || [])]
        });
      });
    }

    if (highIssues.length > 0) {
      tickets.push({
        id: `ticket-${ticketId++}`,
        title: `${metricConfig.label} High Priority Improvements`,
        description: `Address ${highIssues.length} high-priority ${metricConfig.label.toLowerCase()} issues across the codebase`,
        metric: metricKey as keyof typeof METRICS_CONFIG,
        priority: 'high',
        estimatedEffort: `${Math.ceil(highIssues.length * 0.5)}-${Math.ceil(highIssues.length * 1)} days`,
        dependencies: Array.from(
          new Set(highIssues.flatMap((i) => i.relatedFiles || []))
        ),
        status: 'queued',
        assignedAgent: `${metricConfig.label} Enhancement Agent`,
        files: Array.from(new Set(highIssues.map((i) => i.file)))
      });
    }

    if (mediumLowIssues.length > 0) {
      tickets.push({
        id: `ticket-${ticketId++}`,
        title: `${metricConfig.label} Code Quality Cleanup`,
        description: `Comprehensive cleanup addressing ${mediumLowIssues.length} code quality improvements`,
        metric: metricKey as keyof typeof METRICS_CONFIG,
        priority: 'medium',
        estimatedEffort: `${Math.ceil(mediumLowIssues.length * 0.25)}-${Math.ceil(mediumLowIssues.length * 0.5)} days`,
        dependencies: [],
        status: 'queued',
        files: Array.from(new Set(mediumLowIssues.map((i) => i.file)))
      });
    }
  });

  return tickets.sort((a, b) => {
    const priorityOrder = { critical: 4, high: 3, medium: 2, low: 1 };
    return priorityOrder[b.priority] - priorityOrder[a.priority];
  });
}
