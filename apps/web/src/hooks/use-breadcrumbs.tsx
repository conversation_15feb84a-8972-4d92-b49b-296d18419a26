'use client';

import { usePathname } from 'next/navigation';
import { useMemo } from 'react';

type BreadcrumbItem = {
  title: string;
  link: string;
};

// This allows to add custom title as well
const routeMapping: Record<string, BreadcrumbItem[]> = {
  '/dashboard': [{ title: 'Dashboard', link: '/dashboard' }],
  '/dashboard/employee': [
    { title: 'Dashboard', link: '/dashboard' },
    { title: 'Employee', link: '/dashboard/employee' }
  ],
  '/dashboard/product': [
    { title: 'Dashboard', link: '/dashboard' },
    { title: 'Product', link: '/dashboard/product' }
  ]
  // Add more custom mappings as needed
};

export function useBreadcrumbs() {
  const pathname = usePathname();

  const breadcrumbs = useMemo(() => {
    // Check if we have a custom mapping for this exact path
    if (routeMapping[pathname]) {
      return routeMapping[pathname];
    }

    // If no exact match, fall back to generating breadcrumbs from the path
    const segments = pathname.split('/').filter(Bo<PERSON>an);

    // Skip the first segment if it appears to be an org slug
    // We assume the first segment is an org slug if the path has more than one segment
    // and doesn't start with known non-org routes like 'auth', 'dashboard', etc.
    const knownNonOrgRoutes = ['auth', 'dashboard'];
    let filteredSegments = segments;

    // Skip org slug if present
    if (segments.length > 1 && !knownNonOrgRoutes.includes(segments[0])) {
      filteredSegments = segments.slice(1);
    }

    // Skip "repo" segment if it appears (used for repository routing)
    if (filteredSegments.length > 0 && filteredSegments[0] === 'repo') {
      filteredSegments = filteredSegments.slice(1);
    }

    return filteredSegments.map((segment, index) => {
      // Calculate the actual path including skipped segments
      const segmentIndex = segments.indexOf(segment);
      const path = `/${segments.slice(0, segmentIndex + 1).join('/')}`;
      return {
        title: segment,
        link: path
      };
    });
  }, [pathname]);

  return breadcrumbs;
}
