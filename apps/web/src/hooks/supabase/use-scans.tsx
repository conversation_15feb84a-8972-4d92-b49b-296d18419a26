'use client';
import { createClient } from '@/lib/supabase/client';
import { useQuery } from '@tanstack/react-query';
import { Tables } from '@/lib/supabase/schema';
import { type User, USERS } from '@/constants/scans';

// Frontend Scan interface that adapts the Supabase data
export interface Scan {
  id: string;
  name: string;
  description: string;
  timestamp: string;
  timestampMs: number;
  duration: string;
  status: 'completed' | 'running' | 'failed';
  overallScore: number;
  previousScore?: number;
  metrics: {
    testCoverage: number;
    clarity: number;
    modularity: number;
    security: number;
    performance: number;
    faultResilience: number;
    agentReadiness: number;
  };
  issues: any[];
  filesAnalyzed: number;
  linesOfCode: number;
  tokenCost: number;
  promptTokens: number;
  completionTokens: number;
  totalTokens: number;
  creator?: User;
  triggerReason: 'manual' | 'scheduled' | 'push' | 'pr';
  configuration: {
    scanType: string;
    includeTests: boolean;
    includeDependencies: boolean;
    maxFileSize: string;
    excludePatterns: string[];
  };
  executionDetails: {
    startTime: string;
    endTime: string;
    executionEnvironment: string;
    agentVersion: string;
    rulesVersion: string;
  };
}

// Transform Supabase scan to frontend format
function transformScan(supabaseScan: Tables<'scans'>): Scan {
  // Safely parse metrics from Json type
  let metrics = {
    testCoverage: 0,
    clarity: 0,
    modularity: 0,
    security: 0,
    performance: 0,
    faultResilience: 0,
    agentReadiness: 0
  };

  if (supabaseScan.metrics && typeof supabaseScan.metrics === 'object') {
    const metricsData = supabaseScan.metrics as any;
    metrics = {
      testCoverage: metricsData.test_coverage || 0,
      clarity: metricsData.clarity || 0,
      modularity: metricsData.modularity || 0,
      security: metricsData.security || 0,
      performance: metricsData.performance || 0,
      faultResilience: metricsData.fault_resilience || 0,
      agentReadiness: metricsData.agent_readiness || 0
    };
  }

  const overallScore = Math.round(
    Object.values(metrics).reduce((sum, val) => sum + val, 0) / 7
  );

  // Get random user for creator (fallback since we don't have user ID in schema)
  const creator = USERS[Math.floor(Math.random() * USERS.length)];

  // Parse token cost
  const tokenCost = parseFloat(
    (supabaseScan.token_cost || '$0').replace('$', '')
  );

  // Mock some values that aren't in the data
  const totalTokens = Math.floor(tokenCost * 1000);
  const promptTokens = Math.floor(totalTokens * 0.7);
  const completionTokens = totalTokens - promptTokens;
  const filesAnalyzed = Math.floor(Math.random() * 100) + 50;
  const linesOfCode = filesAnalyzed * 100;

  return {
    id: supabaseScan.id.toString(),
    name: `${(supabaseScan.trigger || 'Unknown').charAt(0).toUpperCase() + (supabaseScan.trigger || 'Unknown').slice(1)} Scan`,
    description: supabaseScan.summary || 'No summary available',
    timestamp: new Date(supabaseScan.created_at).toLocaleString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }),
    timestampMs: new Date(supabaseScan.created_at).getTime(),
    duration: supabaseScan.duration || '0s',
    status: (supabaseScan.status || 'completed') as
      | 'completed'
      | 'running'
      | 'failed',
    overallScore,
    metrics,
    issues: [], // No issues data in provided structure
    filesAnalyzed,
    linesOfCode,
    tokenCost,
    promptTokens,
    completionTokens,
    totalTokens,
    creator,
    triggerReason:
      supabaseScan.trigger === 'cli'
        ? 'manual'
        : (supabaseScan.trigger as 'manual' | 'scheduled' | 'push' | 'pr'),
    configuration: {
      scanType: 'Full Analysis',
      includeTests: true,
      includeDependencies: supabaseScan.trigger !== 'pr',
      maxFileSize: '1MB',
      excludePatterns: ['node_modules/**', '*.test.ts', 'dist/**']
    },
    executionDetails: {
      startTime: supabaseScan.created_at,
      endTime: supabaseScan.created_at, // Same for now since we don't have end time
      executionEnvironment: 'cloud-runner-v2.1',
      agentVersion: '1.4.2',
      rulesVersion: '2024.12.1'
    }
  };
}

// Hook for fetching scans by organization
export function useScans(orgSlug: string) {
  return useQuery({
    queryKey: ['scans', orgSlug],
    queryFn: async (): Promise<Scan[]> => {
      const supabase = createClient();
      const { data } = await supabase.auth.getSession();
      if (!data.session?.user) {
        return [];
      }

      // Get organization first
      const { data: orgData, error: orgError } = await supabase
        .from('organizations')
        .select('id')
        .eq('slug', orgSlug)
        .single();

      if (orgError || !orgData) {
        throw new Error('Organization not found');
      }

      // Get repositories for this organization
      const { data: repositories, error: repoError } = await supabase
        .from('repositories')
        .select('id');

      if (repoError) {
        throw new Error('Failed to load repositories');
      }

      if (!repositories || repositories.length === 0) {
        return [];
      }

      // Get all scans for these repositories
      const repoIds = repositories.map((repo) => repo.id);
      const { data: scansData, error: scansError } = await supabase
        .from('scans')
        .select('*')
        .in('repo_id', repoIds)
        .order('created_at', { ascending: false });

      if (scansError) {
        throw new Error('Failed to load scans');
      }

      return (scansData || []).map(transformScan);
    },
    enabled: !!orgSlug,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000 // 10 minutes
  });
}

// Hook for fetching scans by specific repository
export function useRepositoryScans(orgSlug: string, repositorySlug: string) {
  return useQuery({
    queryKey: ['scans', orgSlug, repositorySlug],
    queryFn: async (): Promise<Scan[]> => {
      const supabase = createClient();
      const { data } = await supabase.auth.getSession();
      if (!data.session?.user) {
        return [];
      }

      // First, find the repository by name
      const { data: repoData, error: repoError } = await supabase
        .from('repositories')
        .select('id, name')
        .eq('name', repositorySlug)
        .single();

      if (repoError || !repoData) {
        // If repository not found, try to get all scans and filter later
        console.warn(
          'Repository not found, falling back to all scans:',
          repoError
        );
        const { data: allScansData, error: scansError } = await supabase
          .from('scans')
          .select('*')
          .order('created_at', { ascending: false });

        if (scansError || !allScansData) {
          throw new Error('Failed to load scans');
        }

        return allScansData.map(transformScan);
      }

      // Get scans for this specific repository
      const { data: scansData, error: scansError } = await supabase
        .from('scans')
        .select('*')
        .eq('repo_id', repoData.id)
        .order('created_at', { ascending: false });

      if (scansError) {
        throw new Error('Failed to load scans for repository');
      }

      return (scansData || []).map(transformScan);
    },
    enabled: !!orgSlug && !!repositorySlug,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000 // 10 minutes
  });
}

// Helper to get scan description
export function getScanDescription(scan: Scan): string {
  return scan.description || 'Analysis completed';
}
