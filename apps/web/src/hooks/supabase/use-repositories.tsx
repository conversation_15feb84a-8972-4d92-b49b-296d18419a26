'use client';
import { createClient } from '@/lib/supabase/client';
import { useQuery } from '@tanstack/react-query';
import { Tables } from '@/lib/supabase/schema';

// Repository type from schema with scan count added
export type RepositoryWithScans = Tables<'repositories'> & {
  scanCount: number;
};

// Hook for fetching repositories by organization
export function useRepositories(orgSlug: string) {
  return useQuery({
    queryKey: ['repositories', orgSlug],
    queryFn: async (): Promise<RepositoryWithScans[]> => {
      const supabase = createClient();
      const { data } = await supabase.auth.getSession();
      if (!data.session?.user) {
        return [];
      }

      // Get organization and its repositories in one query using slug
      const { data: orgData, error: orgError } = await supabase
        .from('organizations')
        .select(
          `
          id,
          repositories (*)
        `
        )
        .eq('slug', orgSlug)
        .single();

      if (orgError || !orgData) {
        throw new Error('Organization not found');
      }

      const repositories = orgData.repositories || [];

      // Get scan counts for each repository (as a proxy for activity)
      const repoIds = repositories.map((repo) => repo.id);
      let scanCounts: Record<number, number> = {};

      if (repoIds.length > 0) {
        const { data: scanData, error: scanError } = await supabase
          .from('scans')
          .select('repo_id')
          .in('repo_id', repoIds)
          .gte(
            'created_at',
            new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString()
          ); // Last 30 days

        if (!scanError && scanData) {
          // Count scans per repository
          scanData.forEach((scan) => {
            if (scan.repo_id) {
              scanCounts[scan.repo_id] = (scanCounts[scan.repo_id] || 0) + 1;
            }
          });
        }
      }

      // Add scan counts to repositories
      const repositoriesWithScans: RepositoryWithScans[] = repositories.map(
        (repo) => ({
          ...repo,
          scanCount: scanCounts[repo.id] || 0
        })
      );

      return repositoriesWithScans;
    },
    enabled: !!orgSlug,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000 // 10 minutes
  });
}
