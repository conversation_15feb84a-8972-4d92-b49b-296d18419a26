'use client';
import { createClient } from '@/lib/supabase/client';
import { useQuery } from '@tanstack/react-query';
import { Tables } from '@/lib/supabase/schema';

// Frontend Incident interface that adapts the Supabase data
export interface Incident {
  id: string;
  title: string;
  description: string;
  severity: 'high' | 'medium' | 'low';
  status: 'running' | 'completed';
  timestamp: string;
  resolvedAt?: string;
  service: string;
  environment: 'production' | 'staging';
  branch: string | null;
}

// Transform Supabase incident to frontend format
function transformIncident(supabaseIncident: Tables<'incidents'>): Incident {
  // Generate title from service name or use default
  const serviceName = supabaseIncident.service || 'unknown-service';
  const title = `${serviceName} incident`;

  // Default status to 'running' since the schema doesn't have a status field
  // In a real app, you might determine this based on other fields
  const status = 'running'; // Could be derived from other logic

  return {
    id: supabaseIncident.id.toString(),
    title,
    description: supabaseIncident.description || 'No description available',
    severity: (supabaseIncident.severity || 'medium') as
      | 'high'
      | 'medium'
      | 'low',
    status: status as 'running' | 'completed',
    timestamp: supabaseIncident.created_at,
    resolvedAt: undefined, // Schema doesn't have resolved_at field
    service: supabaseIncident.service || 'unknown-service',
    environment: 'production' as 'production' | 'staging', // Schema doesn't have environment field
    branch: supabaseIncident.branch
  };
}

// Hook for fetching incidents by organization
export function useIncidents(orgSlug: string) {
  return useQuery({
    queryKey: ['incidents', orgSlug],
    queryFn: async (): Promise<Incident[]> => {
      const supabase = createClient();
      const { data } = await supabase.auth.getSession();
      if (!data.session?.user) {
        return [];
      }

      // Fetch all incidents directly
      const { data: incidentsData, error: incidentsError } = await supabase
        .from('incidents')
        .select('*')
        .order('created_at', { ascending: false });

      if (incidentsError) {
        throw new Error('Failed to load incidents');
      }

      return (incidentsData || []).map(transformIncident);
    },
    enabled: !!orgSlug,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000 // 10 minutes
  });
}

// Hook for fetching incidents by specific repository
export function useRepositoryIncidents(
  orgSlug: string,
  repositorySlug: string
) {
  return useQuery({
    queryKey: ['incidents', orgSlug, repositorySlug],
    queryFn: async (): Promise<Incident[]> => {
      const supabase = createClient();
      const { data } = await supabase.auth.getSession();
      if (!data.session?.user) {
        return [];
      }

      // Fetch all incidents directly (no repository filtering in schema)
      const { data: incidentsData, error: incidentsError } = await supabase
        .from('incidents')
        .select('*')
        .order('created_at', { ascending: false });

      if (incidentsError) {
        throw new Error('Failed to load incidents');
      }

      return (incidentsData || []).map(transformIncident);
    },
    enabled: !!orgSlug && !!repositorySlug,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000 // 10 minutes
  });
}

// Helper to get incident description
export function getIncidentDescription(incident: Incident): string {
  return incident.description || 'No description available';
}
