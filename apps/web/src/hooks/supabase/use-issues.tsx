'use client';
import { createClient } from '@/lib/supabase/client';
import { useQuery } from '@tanstack/react-query';
import { Tables } from '@/lib/supabase/schema';

// Issue type from schema with additional computed fields
export type IssueWithMetadata = Tables<'issues'> & {
  priority: 'low' | 'medium' | 'high';
  trigger: 'scan' | 'incident';
  triggerSource: string;
  githubPrLink: string | undefined;
  tokenCost: number;
  repositoryName?: string;
  repositoryUrl?: string;
};

// Helper function to determine priority from issue name/description
function getPriorityFromIssue(
  issue: Tables<'issues'>
): 'low' | 'medium' | 'high' {
  const name = issue.name?.toLowerCase() || '';
  const description = issue.description?.toLowerCase() || '';

  const highPriorityKeywords = [
    'security',
    'vulnerability',
    'critical',
    'fault',
    'crash'
  ];
  const mediumPriorityKeywords = ['performance', 'test', 'coverage', 'slow'];

  if (
    highPriorityKeywords.some(
      (keyword) => name.includes(keyword) || description.includes(keyword)
    )
  ) {
    return 'high';
  }

  if (
    mediumPriorityKeywords.some(
      (keyword) => name.includes(keyword) || description.includes(keyword)
    )
  ) {
    return 'medium';
  }

  return 'low';
}

// Helper function to determine trigger type from issue
function getTriggerFromIssue(issue: Tables<'issues'>): {
  trigger: 'scan' | 'incident';
  triggerSource: string;
} {
  if (issue.scan_id) {
    return { trigger: 'scan', triggerSource: `Scan #${issue.scan_id}` };
  }
  return { trigger: 'scan', triggerSource: 'Unknown' };
}

// Hook for fetching issues by organization
export function useIssues(orgSlug: string) {
  return useQuery({
    queryKey: ['issues', orgSlug],
    queryFn: async (): Promise<IssueWithMetadata[]> => {
      const supabase = createClient();
      const { data } = await supabase.auth.getSession();
      if (!data.session?.user) {
        return [];
      }

      // Get organization first (same pattern as useScans)
      const { data: orgData, error: orgError } = await supabase
        .from('organizations')
        .select('id')
        .eq('slug', orgSlug)
        .single();

      if (orgError || !orgData) {
        throw new Error('Organization not found');
      }

      // Get repositories for this organization
      const { data: repositories, error: repoError } = await supabase
        .from('repositories')
        .select('id, name, url');

      if (repoError) {
        throw new Error('Failed to load repositories');
      }

      if (!repositories || repositories.length === 0) {
        return [];
      }

      // Get all scans for these repositories
      const repoIds = repositories.map((repo) => repo.id);
      const { data: scansData, error: scansError } = await supabase
        .from('scans')
        .select('id, repo_id')
        .in('repo_id', repoIds);

      if (scansError) {
        throw new Error('Failed to load scans');
      }

      if (!scansData || scansData.length === 0) {
        return [];
      }

      // Get all issues for these scans
      const scanIds = scansData.map((scan) => scan.id);
      const { data: issuesData, error: issuesError } = await supabase
        .from('issues')
        .select('*')
        .in('scan_id', scanIds)
        .order('created_at', { ascending: false });

      if (issuesError) {
        throw new Error('Failed to load issues');
      }

      // Transform issues and add repository metadata
      const allIssues: IssueWithMetadata[] = (issuesData || []).map((issue) => {
        const { trigger, triggerSource } = getTriggerFromIssue(issue);
        const priority = getPriorityFromIssue(issue);

        // Find the repository for this issue's scan
        const scan = scansData.find((s) => s.id === issue.scan_id);
        const repository = scan
          ? repositories.find((r) => r.id === scan.repo_id)
          : undefined;

        return {
          ...issue,
          priority,
          trigger,
          triggerSource,
          githubPrLink: issue.pr_link || undefined,
          tokenCost: 0, // This would need to be calculated from scan data
          repositoryName: repository?.name || undefined,
          repositoryUrl: repository?.url || undefined
        };
      });

      return allIssues;
    },
    enabled: !!orgSlug,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000 // 10 minutes
  });
}

// Hook for fetching issues by specific repository
export function useRepositoryIssues(orgSlug: string, repositorySlug: string) {
  return useQuery({
    queryKey: ['issues', orgSlug, repositorySlug],
    queryFn: async (): Promise<IssueWithMetadata[]> => {
      const supabase = createClient();
      const { data } = await supabase.auth.getSession();
      if (!data.session?.user) {
        return [];
      }

      // First, find the repository by name (same pattern as useRepositoryScans)
      const { data: repoData, error: repoError } = await supabase
        .from('repositories')
        .select('id, name, url')
        .eq('name', repositorySlug)
        .single();

      if (repoError || !repoData) {
        // If repository not found, try to get all issues and filter later
        console.warn(
          'Repository not found, falling back to all issues:',
          repoError
        );
        const { data: allIssuesData, error: issuesError } = await supabase
          .from('issues')
          .select('*')
          .order('created_at', { ascending: false });

        if (issuesError || !allIssuesData) {
          throw new Error('Failed to load issues');
        }

        return allIssuesData.map((issue) => {
          const { trigger, triggerSource } = getTriggerFromIssue(issue);
          const priority = getPriorityFromIssue(issue);

          return {
            ...issue,
            priority,
            trigger,
            triggerSource,
            githubPrLink: issue.pr_link || undefined,
            tokenCost: 0,
            repositoryName: undefined,
            repositoryUrl: undefined
          };
        });
      }

      // Get scans for this specific repository, then get issues for those scans
      const { data: scansData, error: scansError } = await supabase
        .from('scans')
        .select('id')
        .eq('repo_id', repoData.id);

      if (scansError) {
        throw new Error('Failed to load scans for repository');
      }

      if (!scansData || scansData.length === 0) {
        return [];
      }

      // Get issues for these scans
      const scanIds = scansData.map((scan) => scan.id);
      const { data: issuesData, error: issuesError } = await supabase
        .from('issues')
        .select('*')
        .in('scan_id', scanIds)
        .order('created_at', { ascending: false });

      if (issuesError) {
        throw new Error('Failed to load issues for repository');
      }

      const allIssues: IssueWithMetadata[] = (issuesData || []).map((issue) => {
        const { trigger, triggerSource } = getTriggerFromIssue(issue);
        const priority = getPriorityFromIssue(issue);

        return {
          ...issue,
          priority,
          trigger,
          triggerSource,
          githubPrLink: issue.pr_link || undefined,
          tokenCost: 0, // This would need to be calculated from scan data
          repositoryName: repoData.name || undefined,
          repositoryUrl: repoData.url || undefined
        };
      });

      return allIssues;
    },
    enabled: !!orgSlug && !!repositorySlug,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000 // 10 minutes
  });
}

// Hook for fetching inbox issues (completed issues across the organization)
export function useInboxIssues(orgSlug: string) {
  return useQuery({
    queryKey: ['inbox', orgSlug],
    queryFn: async (): Promise<IssueWithMetadata[]> => {
      const supabase = createClient();
      const { data } = await supabase.auth.getSession();
      if (!data.session?.user) {
        return [];
      }

      // Get organization first (same pattern as useScans)
      const { data: orgData, error: orgError } = await supabase
        .from('organizations')
        .select('id')
        .eq('slug', orgSlug)
        .single();

      if (orgError || !orgData) {
        throw new Error('Organization not found');
      }

      // Get repositories for this organization
      const { data: repositories, error: repoError } = await supabase
        .from('repositories')
        .select('id, name, url');

      if (repoError) {
        throw new Error('Failed to load repositories');
      }

      if (!repositories || repositories.length === 0) {
        return [];
      }

      // Get all scans for these repositories
      const repoIds = repositories.map((repo) => repo.id);
      const { data: scansData, error: scansError } = await supabase
        .from('scans')
        .select('id, repo_id')
        .in('repo_id', repoIds);

      if (scansError) {
        throw new Error('Failed to load scans');
      }

      if (!scansData || scansData.length === 0) {
        return [];
      }

      // Get completed issues for these scans
      const scanIds = scansData.map((scan) => scan.id);
      const { data: issuesData, error: issuesError } = await supabase
        .from('issues')
        .select('*')
        .in('scan_id', scanIds)
        .eq('status', 'completed')
        .order('created_at', { ascending: false });

      if (issuesError) {
        throw new Error('Failed to load completed issues');
      }

      // Transform issues and add repository metadata
      const completedIssues: IssueWithMetadata[] = (issuesData || []).map(
        (issue) => {
          const { trigger, triggerSource } = getTriggerFromIssue(issue);
          const priority = getPriorityFromIssue(issue);

          // Find the repository for this issue's scan
          const scan = scansData.find((s) => s.id === issue.scan_id);
          const repository = scan
            ? repositories.find((r) => r.id === scan.repo_id)
            : undefined;

          return {
            ...issue,
            priority,
            trigger,
            triggerSource,
            githubPrLink: issue.pr_link || undefined,
            tokenCost: 0, // This would need to be calculated from scan data
            repositoryName: repository?.name || undefined,
            repositoryUrl: repository?.url || undefined
          };
        }
      );

      return completedIssues;
    },
    enabled: !!orgSlug,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000 // 10 minutes
  });
}
