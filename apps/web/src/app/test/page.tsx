'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Github } from 'lucide-react';
import { createClient } from '@/lib/supabase/client';

export default function TestPage() {
  const [orgName, setOrgName] = useState('');
  const [isCreating, setIsCreating] = useState(false);

  const createOrganization = async () => {
    if (!orgName.trim()) return;

    setIsCreating(true);
    try {
      const { data, error } = await createClient()
        .from('organizations')
        .insert([{ name: orgName.trim() }])
        .select()
        .single();

      if (error) {
        console.error('Error creating organization:', error);
        alert('Failed to create organization');
      } else {
        console.log('Created organization:', data);
        alert(`Organization "${data.name}" created with ID: ${data.id}`);
        setOrgName('');
      }
    } catch (error) {
      console.error('Error:', error);
      alert('Failed to create organization');
    } finally {
      setIsCreating(false);
    }
  };

  const handleInstallApp = () => {
    window.open(
      'https://github.com/apps/backspace-agent/installations/new',
      '_blank'
    );
  };

  return (
    <div className='container mx-auto p-8'>
      <div className='mx-auto max-w-md space-y-6'>
        <Card>
          <CardHeader>
            <CardTitle className='text-center text-2xl font-bold'>
              Create Organization
            </CardTitle>
            <CardDescription className='text-center'>
              Test creating a new organization in Supabase
            </CardDescription>
          </CardHeader>
          <CardContent className='space-y-4'>
            <Input
              placeholder='Organization name'
              value={orgName}
              onChange={(e) => setOrgName(e.target.value)}
            />
            <Button
              onClick={createOrganization}
              className='w-full'
              size='lg'
              disabled={!orgName.trim() || isCreating}
            >
              {isCreating ? 'Creating...' : 'Create Organization'}
            </Button>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className='text-center text-2xl font-bold'>
              Install Backspace GitHub App
            </CardTitle>
            <CardDescription className='text-center'>
              Connect your GitHub repositories to Backspace for automated code
              quality and maintenance
            </CardDescription>
          </CardHeader>
          <CardContent className='space-y-4'>
            <Button onClick={handleInstallApp} className='w-full' size='lg'>
              <Github className='mr-2 h-5 w-5' />
              Install on GitHub
            </Button>

            <div className='text-muted-foreground text-center text-sm'>
              <p>
                This will redirect you to GitHub to install the Backspace app on
                your repositories.
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
