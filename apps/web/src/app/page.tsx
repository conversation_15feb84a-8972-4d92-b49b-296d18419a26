import { redirect } from 'next/navigation';
import { createClient } from '@/lib/supabase/server';

export default async function Page() {
  const supabase = await createClient();
  const {
    data: { user }
  } = await supabase.auth.getUser();

  if (!user) {
    return redirect('/auth');
  }

  // Fetch the user's organization membership
  const { data: orgUser, error: orgUserError } = await supabase
    .from('organization_users')
    .select('org_id')
    .eq('user_id', user.id)
    .eq('status', 'active')
    .single();

  if (orgUserError || !orgUser) {
    // If user doesn't belong to any organization or there's an error,
    // redirect to auth page
    return redirect('/auth');
  }

  // Fetch the organization details
  const { data: org, error: orgError } = await supabase
    .from('organizations')
    .select('slug')
    .eq('id', orgUser.org_id)
    .single();

  if (orgError || !org?.slug) {
    // If organization doesn't exist or has no slug, redirect to auth
    return redirect('/auth');
  }

  redirect(`/${org.slug}/inbox`);
}
