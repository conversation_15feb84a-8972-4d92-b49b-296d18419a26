body {
  @apply overscroll-none bg-transparent;
}

:root {
  --font-sans: var(--font-inter);
  --header-height: calc(var(--spacing) * 12 + 1px);
}

.theme-scaled {
  @media (min-width: 1024px) {
    --radius: 0.6rem;
    --text-lg: 1.05rem;
    --text-base: 0.85rem;
    --text-sm: 0.8rem;
    --spacing: 0.222222rem;
  }

  [data-slot='card'] {
    --spacing: 0.16rem;
  }

  [data-slot='select-trigger'],
  [data-slot='toggle-group-item'] {
    --spacing: 0.222222rem;
  }
}

/* Base sleek theme - uses blue for action buttons */
.theme-default,
.theme-default-scaled {
  --primary: oklch(0.556 0.182 264.052);
  /* Blue for action buttons */
  --primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.88 0 0);
  --sidebar-accent-hover: oklch(0.85 0 0);

  @variant dark {
    --primary: oklch(0.6 0.22 264.052);
    /* Brighter blue for dark mode */
    --primary-foreground: oklch(0.985 0 0);
    --sidebar-accent: oklch(0.985 0 0 / 10%);
    --sidebar-accent-hover: oklch(0.985 0 0 / 5%);
  }
}

.theme-blue,
.theme-blue-scaled {
  --primary: oklch(0.556 0.182 264.052);
  --primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.88 0 0);
  --sidebar-accent-hover: oklch(0.85 0 0);

  @variant dark {
    --primary: oklch(0.6 0.22 264.052);
    --primary-foreground: oklch(0.985 0 0);
    --sidebar-accent: oklch(0.985 0 0 / 50%);
    --sidebar-accent-hover: oklch(0.985 0 0 / 20%);
  }
}

.theme-green,
.theme-green-scaled {
  --primary: oklch(0.56 0.15 142);
  /* Green alternative */
  --primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.88 0 0);
  --sidebar-accent-hover: oklch(0.85 0 0);

  @variant dark {
    --primary: oklch(0.6 0.17 142);
    --primary-foreground: oklch(0.985 0 0);
    --sidebar-accent: oklch(0.985 0 0 / 50%);
    --sidebar-accent-hover: oklch(0.985 0 0 / 20%);
  }
}

.theme-amber,
.theme-amber-scaled {
  --primary: oklch(0.65 0.15 85);
  /* Amber alternative */
  --primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.88 0 0);
  --sidebar-accent-hover: oklch(0.85 0 0);

  @variant dark {
    --primary: oklch(0.7 0.17 85);
    --primary-foreground: oklch(0.985 0 0);
    --sidebar-accent: oklch(0.985 0 0 / 50%);
    --sidebar-accent-hover: oklch(0.985 0 0 / 20%);
  }
}

.theme-mono,
.theme-mono-scaled {
  --font-sans: var(--font-mono);
  --primary: oklch(0.556 0.182 264.052);
  /* Blue even for mono theme */
  --primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.88 0 0);
  --sidebar-accent-hover: oklch(0.85 0 0);

  @variant dark {
    --primary: oklch(0.6 0.22 264.052);
    --primary-foreground: oklch(0.985 0 0);
    --sidebar-accent: oklch(0.985 0 0 / 50%);
    --sidebar-accent-hover: oklch(0.985 0 0 / 20%);
  }

  .rounded-xs,
  .rounded-sm,
  .rounded-md,
  .rounded-lg,
  .rounded-xl {
    @apply !rounded-none;
    border-radius: 0;
  }

  .shadow-xs,
  .shadow-sm,
  .shadow-md,
  .shadow-lg,
  .shadow-xl {
    @apply !shadow-none;
  }

  [data-slot='toggle-group'],
  [data-slot='toggle-group-item'] {
    @apply !rounded-none !shadow-none;
  }
}