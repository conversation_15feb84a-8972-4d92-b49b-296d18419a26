'use client';

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { 
  CheckCircle2, 
  ArrowRight,
  Github,
  GitPullRequest,
  Bug,
  Shield,
  Zap,
  Clock
} from 'lucide-react';

// Mock activity data to show Backspace in action
const activityItems = [
  {
    id: 1,
    type: 'scan',
    title: 'Deep code scan completed',
    description: 'Backspace analyzed 12 files and found 3 optimization opportunities',
    time: '2 minutes ago',
    icon: Shield,
    iconColor: 'text-green-500',
    user: 'Backspace Bot'
  },
  {
    id: 2,
    type: 'pr',
    title: 'Created pull request #47',
    description: 'Fix memory leak in user authentication module',
    time: '5 minutes ago',
    icon: GitPullRequest,
    iconColor: 'text-blue-500',
    user: 'Backspace Bot'
  },
  {
    id: 3,
    type: 'issue',
    title: 'Fixed 2 security vulnerabilities',
    description: 'Updated dependencies to patch CVE-2024-1234',
    time: '8 minutes ago',
    icon: Bug,
    iconColor: 'text-orange-500',
    user: 'Backspace Bot'
  },
  {
    id: 4,
    type: 'optimization',
    title: 'Performance optimization applied',
    description: 'Reduced bundle size by 15% through tree shaking',
    time: '12 minutes ago',
    icon: Zap,
    iconColor: 'text-purple-500',
    user: 'Backspace Bot'
  }
];

export default function InstallPage() {
  const [isConnected, setIsConnected] = useState(false);

  const handleInstallGitHub = () => {
    // Open GitHub app installation
    window.open('https://github.com/apps/backspace-agent/installations/new', '_blank');
    // For demo purposes, mark as connected after a delay
    setTimeout(() => {
      setIsConnected(true);
    }, 2000);
  };

  return (
    <div className="min-h-screen bg-background">
      {/* Header with Logo - No border */}
      <div className="bg-card/30 backdrop-blur-sm">
        <div className="container mx-auto px-6 py-4">
          <div className="flex items-center space-x-3">
            {/* Backspace Logo */}
            <div className="w-8 h-8 bg-white flex items-center justify-center">
              <div className="flex items-center font-mono">
                <span className="text-black text-lg font-bold leading-none">&lt;</span>
                <span className="text-black text-lg font-bold leading-none">_</span>
              </div>
            </div>
            <span className="text-xl font-semibold">Backspace</span>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="container mx-auto px-6 py-16 max-w-4xl">
        
        {/* Main Activity Card */}
        <Card className="border-0 shadow-xl bg-card/90 backdrop-blur-sm max-w-2xl mx-auto">
          <CardContent className="p-8">
            
            <div className="space-y-6">
              
              {/* GitHub Icon + Title */}
              <div className="text-center">
                <div className="w-16 h-16 mx-auto mb-6 rounded-full bg-white dark:bg-gray-900 flex items-center justify-center shadow-lg">
                  <svg className="w-8 h-8" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 *********** 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/>
                  </svg>
                </div>
                <h1 className="text-2xl font-semibold mb-3">
                  Connect to GitHub
                </h1>
                <p className="text-muted-foreground">
                  See what Backspace will do for your repositories
                </p>
              </div>

                  {/* Centered Activity Feed */}
                  <div className="bg-muted/30 rounded-lg p-6">
                    <h3 className="font-semibold mb-4 flex items-center justify-center">
                      <Clock className="w-4 h-4 mr-2" />
                      What Backspace does for you
                    </h3>
                    <div className="space-y-4">
                      {activityItems.map((item, index) => (
                        <div 
                          key={item.id} 
                          className="flex items-start space-x-3"
                          style={{ 
                            animationDelay: `${index * 0.5}s`,
                            animation: 'fadeInUp 0.6s ease-out forwards'
                          }}
                        >
                          <Avatar className="w-8 h-8 mt-1">
                            <AvatarFallback className="bg-primary/10">
                              <item.icon className={`w-4 h-4 ${item.iconColor}`} />
                            </AvatarFallback>
                          </Avatar>
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center justify-between">
                              <p className="text-sm font-medium">{item.title}</p>
                              <span className="text-xs text-muted-foreground">{item.time}</span>
                            </div>
                            <p className="text-xs text-muted-foreground mt-1">{item.description}</p>
                            <div className="flex items-center mt-2 space-x-2">
                              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-green-100 text-green-700 dark:bg-green-900/20 dark:text-green-400">
                                ✓ Automated
                              </span>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>

              {/* Centered Action Button */}
              <div className="text-center pt-4">
                <Button 
                  size="lg" 
                  onClick={handleInstallGitHub}
                  className="px-8"
                >
                  <svg className="w-5 h-5 mr-2" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 *********** 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/>
                  </svg>
                  Install on GitHub
                  <ArrowRight className="w-4 h-4 ml-2" />
                </Button>
                
                <div className="mt-3">
                  <Button 
                    variant="ghost" 
                    size="sm"
                    className="text-muted-foreground"
                  >
                    Skip for now
                  </Button>
                </div>
              </div>

            </div>
            
          </CardContent>
        </Card>
        
      </div>
    </div>
  );
}