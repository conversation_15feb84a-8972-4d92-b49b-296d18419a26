'use client';

import React, { useState, useMemo, useEffect } from 'react';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';
import { format, formatDistanceToNow } from 'date-fns';
import {
  GitPullRequest,
  SignalHigh,
  SignalLow,
  SignalMedium
} from 'lucide-react';
import {
  IconExternalLink,
  IconCheck,
  IconCode,
  IconMessageCircle,
  IconGitCommit,
  IconBug,
  IconShield,
  IconGitBranch
} from '@tabler/icons-react';
import { Markdown } from '@/components/ui/markdown';
import {
  useInboxIssues,
  type IssueWithMetadata
} from '@/hooks/supabase/use-issues';
import { useParams } from 'next/navigation';
import PageContainer from '@/components/layout/page-container';

type Issue = IssueWithMetadata;

interface AgentStep {
  id: string;
  type: 'analysis' | 'code_generation' | 'testing' | 'review' | 'deployment';
  title: string;
  description: string;
  status: 'running' | 'completed';
  timestamp: string;
  duration?: number; // duration in minutes
  artifacts?: {
    type: 'code' | 'test' | 'documentation' | 'commit';
    name: string;
    url?: string;
  }[];
}

function getStatusBadge(status: string) {
  return (
    <Badge className='bg-green-100 text-sm text-green-800 dark:bg-green-900/30 dark:text-green-300'>
      Completed
    </Badge>
  );
}

function getPriorityBadge(priority: Issue['priority']) {
  const badges = {
    low: (
      <Badge className='bg-yellow-100 px-2 py-0 text-sm text-green-800 dark:bg-green-900/30 dark:text-green-300'>
        <div className='flex items-center gap-1.5'>
          <SignalLow className='h-3 w-3' />
          <span>Low</span>
        </div>
      </Badge>
    ),
    medium: (
      <Badge className='bg-orange-100 px-2 py-0 text-sm text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300'>
        <div className='flex items-center gap-1.5'>
          <SignalMedium className='h-3 w-3' />
          <span>Medium</span>
        </div>
      </Badge>
    ),
    high: (
      <Badge className='bg-red-100 px-2 py-0 text-sm text-orange-800 dark:bg-orange-900/30 dark:text-orange-300'>
        <div className='flex items-center gap-1.5'>
          <SignalHigh className='h-3 w-3' />
          <span>High</span>
        </div>
      </Badge>
    )
  };
  return badges[priority];
}

function IssueListItem({
  issue,
  isSelected,
  onClick
}: {
  issue: Issue;
  isSelected: boolean;
  onClick: () => void;
}) {
  return (
    <div
      className={cn(
        'm-1 ml-2 cursor-pointer rounded-sm px-3 py-2 transition-colors',
        !isSelected && 'hover:bg-black/3 hover:dark:bg-white/8',
        isSelected && 'bg-black/5 dark:bg-white/10'
      )}
      onClick={onClick}
    >
      <div className='flex items-center justify-between'>
        <div className='min-w-0 flex-1'>
          <div className='flex items-center justify-between'>
            <div className='truncate text-sm font-medium'>{issue.name}</div>
          </div>
          <div className='text-muted-foreground mt-1 text-xs'>
            Completed{' '}
            {formatDistanceToNow(new Date(issue.created_at), {
              addSuffix: true
            })}
          </div>
        </div>
      </div>
    </div>
  );
}

const generateMockAgentSteps = (issue: Issue): AgentStep[] => {
  const baseSteps: Omit<AgentStep, 'id' | 'timestamp'>[] = [
    {
      type: 'analysis',
      title: 'Issue Analysis',
      description: `Analyzed the reported issue: "${issue.name}". Identified root cause in authentication flow.`,
      status: 'completed',
      duration: 2,
      artifacts: [{ type: 'documentation', name: 'Analysis Report', url: '#' }]
    },
    {
      type: 'code_generation',
      title: 'Solution Implementation',
      description:
        'Generated fix for authentication timeout issue. Updated session management logic.',
      status: 'completed',
      duration: 13,
      artifacts: [
        { type: 'code', name: 'auth-session.ts', url: '#' },
        { type: 'code', name: 'middleware.ts', url: '#' }
      ]
    },
    {
      type: 'testing',
      title: 'Test Generation',
      description:
        'Created comprehensive test suite to verify the fix and prevent regression.',
      status: 'completed',
      duration: 28,
      artifacts: [{ type: 'test', name: 'auth-session.test.ts', url: '#' }]
    },
    {
      type: 'review',
      title: 'Code Review',
      description:
        'Automated code review completed. No issues found. Ready for deployment.',
      status: 'completed',
      duration: 43,
      artifacts: [{ type: 'documentation', name: 'Review Summary', url: '#' }]
    }
  ];

  if (issue.githubPrLink) {
    baseSteps.push({
      type: 'deployment',
      title: 'GitHub PR Creation',
      description:
        'Created pull request with the proposed changes. Awaiting review.',
      status: 'completed',
      artifacts: [
        { type: 'commit', name: `Fix: ${issue.name}`, url: issue.githubPrLink }
      ]
    });
  }

  return baseSteps.map((step, index) => ({
    ...step,
    id: `step-${index}`,
    timestamp: new Date(
      new Date(issue.created_at).getTime() + index * 15 * 60 * 1000
    ).toISOString()
  }));
};

const getStepIcon = (type: AgentStep['type']) => {
  const icons = {
    analysis: IconBug,
    code_generation: IconCode,
    testing: IconShield,
    review: IconMessageCircle,
    deployment: IconGitBranch
  };
  return icons[type];
};

const formatDuration = (minutes: number) => {
  if (minutes < 1) {
    return '< 1 minute';
  } else if (minutes < 60) {
    return `${minutes} minute${minutes === 1 ? '' : 's'}`;
  } else {
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;
    if (remainingMinutes === 0) {
      return `${hours} hour${hours === 1 ? '' : 's'}`;
    }
    return `${hours}h ${remainingMinutes}m`;
  }
};

export default function InboxPage() {
  const params = useParams();
  const orgSlug = params['org-slug'] as string;

  // Use the hook to fetch inbox issues
  const {
    data: issues = [],
    isLoading: loading,
    error: hookError
  } = useInboxIssues(orgSlug);

  const [selectedIssueId, setSelectedIssueId] = useState<string>('');
  const [searchTerm, setSearchTerm] = useState('');

  const error = hookError ? 'Failed to load issues' : null;

  // Set the first issue as selected by default when issues are loaded
  useEffect(() => {
    if (issues.length > 0 && !selectedIssueId) {
      setSelectedIssueId(issues[0].id.toString());
    }
  }, [issues, selectedIssueId]);

  const filteredIssues = useMemo(() => {
    return issues.filter(
      (issue) =>
        issue.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        issue.description?.toLowerCase().includes(searchTerm.toLowerCase())
    );
  }, [issues, searchTerm]);

  const selectedIssue = issues.find(
    (issue) => issue.id.toString() === selectedIssueId
  );
  const agentSteps = selectedIssue ? generateMockAgentSteps(selectedIssue) : [];

  // Keyboard navigation
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Only handle keyboard navigation if no input is focused
      if (
        document.activeElement?.tagName === 'INPUT' ||
        document.activeElement?.tagName === 'TEXTAREA'
      ) {
        return;
      }

      const currentIndex = filteredIssues.findIndex(
        (issue) => issue.id.toString() === selectedIssueId
      );

      switch (event.key.toLowerCase()) {
        case 'j': // Move down
          event.preventDefault();
          if (currentIndex < filteredIssues.length - 1) {
            setSelectedIssueId(filteredIssues[currentIndex + 1].id.toString());
          }
          break;

        case 'k': // Move up
          event.preventDefault();
          if (currentIndex > 0) {
            setSelectedIssueId(filteredIssues[currentIndex - 1].id.toString());
          }
          break;

        case 'enter': // Open GitHub PR link
          event.preventDefault();
          if (selectedIssue?.githubPrLink) {
            window.open(
              selectedIssue.githubPrLink,
              '_blank',
              'noopener,noreferrer'
            );
          }
          break;
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [filteredIssues, selectedIssueId, selectedIssue]);

  if (loading) {
    return (
      <PageContainer scrollable>
        <div className='flex items-center justify-center'>
          <div className='text-muted-foreground text-sm'>Loading inbox...</div>
        </div>
      </PageContainer>
    );
  }

  if (error) {
    return (
      <div className='bg-background flex h-screen items-center justify-center'>
        <div className='text-red-500'>{error}</div>
      </div>
    );
  }

  return (
    <div className='bg-background flex h-screen'>
      {/* Keyboard shortcuts - Top of screen */}
      <div className='absolute top-2 left-1/2 z-10 -translate-x-1/2 transform opacity-80'>
        <div className='bg-background/80 text-muted-foreground flex items-center gap-4 rounded-sm px-3 py-1.5 text-xs font-medium'>
          <span className='flex items-center gap-1'>
            <kbd className='bg-muted rounded px-1 py-0 text-xs'>j</kbd>
            <kbd className='bg-muted rounded px-1 py-0 text-xs'>k</kbd>
            navigate
          </span>
          <span className='flex items-center gap-1'>
            <kbd className='bg-muted rounded px-1 py-0 text-xs'>enter</kbd>
            open PR
          </span>
        </div>
      </div>

      {/* Left Panel - Issues List */}
      <div className='flex w-80 flex-col'>
        {/* Issues List */}
        <div className='flex-1 overflow-y-auto'>
          {filteredIssues.map((issue) => (
            <IssueListItem
              key={issue.id}
              issue={issue}
              isSelected={issue.id.toString() === selectedIssueId}
              onClick={() => setSelectedIssueId(issue.id.toString())}
            />
          ))}

          {filteredIssues.length === 0 && !loading && (
            <div className='text-muted-foreground p-8 text-center'>
              <div className='mb-2 text-lg'>No completed issues found</div>
            </div>
          )}
        </div>
      </div>

      {/* Main Content */}
      <div className='flex flex-1'>
        {selectedIssue ? (
          <>
            {/* Issue Content */}
            <div className='flex-1 overflow-y-auto'>
              <div className='p-8 pt-0'>
                <div className='mb-6'>
                  <div className='mb-4 flex items-center gap-3'>
                    <h1 className='text-xl font-semibold'>
                      {selectedIssue.name}
                    </h1>
                  </div>
                </div>

                <div className='space-y-8'>
                  {/* Issue Summary */}
                  <section id='issue-summary'>
                    <div className='prose prose-sm max-w-none'>
                      <Markdown content={selectedIssue.description || ''} />
                    </div>
                  </section>

                  {/* Agent Activity */}
                  <section id='agent-activity' className='border-t pt-6'>
                    <div className='mb-6'>
                      <div className='mb-4 flex items-center gap-3'>
                        <h3 className='text-md font-medium'>Activity</h3>
                      </div>
                    </div>

                    {/* Activity Timeline */}
                    <div className='space-y-0'>
                      {agentSteps.map((step, index) => {
                        const StepIcon = getStepIcon(step.type);
                        return (
                          <div key={step.id} className='flex gap-4'>
                            <div className='flex flex-col items-center'>
                              {step.status === 'completed' ? (
                                <div className='relative flex h-8 w-8 items-center justify-center rounded-full border-2'>
                                  <IconCheck className='h-4 w-4 text-green-600' />
                                </div>
                              ) : step.status === 'running' ? (
                                <div className='h-8 w-8 animate-spin rounded-full border-2 border-t-transparent' />
                              ) : (
                                <div className='relative flex h-8 w-8 items-center justify-center rounded-full border-2'>
                                  <StepIcon className='h-4 w-4 text-gray-400' />
                                </div>
                              )}
                              {index < agentSteps.length - 1 && (
                                <div className='min-h-[4rem] w-0.5 flex-1 bg-gray-200' />
                              )}
                            </div>

                            <div className='flex-1 pb-8'>
                              <div className='mb-2 flex items-center gap-2'>
                                <h4 className='text-sm font-medium'>
                                  {step.title}
                                </h4>
                              </div>

                              <p className='mb-3 text-sm text-gray-600'>
                                {step.description}
                              </p>

                              <div className='flex items-center gap-4 text-xs text-gray-500'>
                                {step.duration && (
                                  <span>{formatDuration(step.duration)}</span>
                                )}

                                {step.artifacts &&
                                  step.artifacts.length > 0 && (
                                    <div className='flex items-center gap-2'>
                                      {step.artifacts.map((artifact, i) => (
                                        <a
                                          key={i}
                                          href={artifact.url || '#'}
                                          className='inline-flex items-center gap-1 rounded bg-gray-100 px-2 py-1 text-xs text-gray-700 hover:bg-gray-200'
                                        >
                                          {artifact.type === 'code' && (
                                            <IconCode className='h-3 w-3' />
                                          )}
                                          {artifact.type === 'commit' && (
                                            <IconGitCommit className='h-3 w-3' />
                                          )}
                                          {artifact.type === 'test' && (
                                            <IconShield className='h-3 w-3' />
                                          )}
                                          {artifact.type ===
                                            'documentation' && (
                                            <IconMessageCircle className='h-3 w-3' />
                                          )}
                                          {artifact.name}
                                        </a>
                                      ))}
                                    </div>
                                  )}
                              </div>
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  </section>
                </div>
              </div>
            </div>

            {/* Right Sidebar - Issue Metadata */}
            <div className='w-64 flex-shrink-0'>
              <div className='sticky top-0 p-6 pt-0'>
                <div className='text-muted-foreground space-y-6 text-sm'>
                  <div>
                    <span className='font-medium'>Status:</span>
                    <div className='mt-1'>{getStatusBadge('completed')}</div>
                  </div>

                  <div>
                    <span className='font-medium'>Priority:</span>
                    <div className='mt-1'>
                      {getPriorityBadge(selectedIssue.priority)}
                    </div>
                  </div>

                  <div>
                    <span className='font-medium'>Trigger:</span>
                    <div className='mt-1'>{selectedIssue.triggerSource}</div>
                  </div>

                  <div>
                    <span className='font-medium'>Created:</span>
                    <div className='mt-1'>
                      {format(
                        new Date(selectedIssue.created_at),
                        "MMM d, yyyy 'at' h:mm a"
                      )}
                    </div>
                  </div>

                  <div>
                    <span className='font-medium'>Last Updated:</span>
                    <div className='mt-1'>
                      {formatDistanceToNow(new Date(selectedIssue.created_at), {
                        addSuffix: true
                      })}
                    </div>
                  </div>

                  {selectedIssue.githubPrLink && (
                    <div>
                      <span className='font-medium'>GitHub PR:</span>
                      <div className='mt-1'>
                        <a
                          href={selectedIssue.githubPrLink}
                          target='_blank'
                          rel='noopener noreferrer'
                          className='inline-flex items-center gap-1 text-blue-600 hover:text-blue-800'
                        >
                          #{selectedIssue.githubPrLink.split('/').pop()}
                          <IconExternalLink className='h-3 w-3' />
                        </a>
                      </div>
                    </div>
                  )}

                  <div>
                    <span className='font-medium'>Issue ID:</span>
                    <div className='mt-1 font-mono text-sm'>
                      {selectedIssue.id}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </>
        ) : (
          <div className='text-muted-foreground flex flex-1 items-center justify-center'>
            <div className='text-center'>
              <div className='mb-2 text-lg'>No completed issues</div>
              <div className='text-sm'>
                Complete some issues to see them here
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
