'use client';

import { useState, useMemo } from 'react';
import {
  TrendingUp,
  Activity,
  AlertTriangle,
  Target,
  Zap,
  DollarSign,
  GitMerge,
  Filter,
  Check,
  Expand,
  Minimize
} from 'lucide-react';
import PageContainer from '@/components/layout/page-container';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';
import {
  Popover,
  PopoverContent,
  PopoverTrigger
} from '@/components/ui/popover';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
  CommandSeparator
} from '@/components/ui/command';
import {
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent
} from '@/components/ui/chart';
import { CartesianGrid, Line, LineChart, XAxis, YAxis } from 'recharts';

// Simplified time ranges
const TIME_RANGES = [
  { label: '1h', value: '1h', hours: 1 },
  { label: '1d', value: '1d', hours: 24 },
  { label: '7d', value: '7d', hours: 168 },
  { label: '30d', value: '30d', hours: 720 }
];

// Simplified projects data
const PROJECTS = [
  {
    id: 'website-redesign',
    name: 'Website Redesign',
    color: 'hsl(142 76% 36%)'
  },
  { id: 'mobile-app', name: 'Mobile App', color: 'hsl(221 83% 53%)' },
  { id: 'api-development', name: 'API Development', color: 'hsl(271 81% 56%)' },
  {
    id: 'analytics-dashboard',
    name: 'Analytics Dashboard',
    color: 'hsl(12 76% 61%)'
  },
  { id: 'payment-system', name: 'Payment System', color: 'hsl(173 58% 39%)' }
];

// Simplified chart config - generate from projects
const chartConfig = Object.fromEntries(
  PROJECTS.map((project) => [
    project.id,
    { label: project.name, color: project.color }
  ])
);

// Simplified data generation
const generateTimeSeriesData = (selectedRange: string, projects: string[]) => {
  const hours = TIME_RANGES.find((r) => r.value === selectedRange)?.hours || 24;
  const dataPoints = Math.min(hours, 24);
  const baseTime = Date.now();

  return Array.from({ length: dataPoints }, (_, i) => {
    const timestamp =
      baseTime - (dataPoints - 1 - i) * (hours / dataPoints) * 60 * 60 * 1000;
    const point: any = {
      time: formatTimeAxis(timestamp, selectedRange),
      timestamp
    };

    projects.forEach((projectId) => {
      const baseValue = projectId.length + i * 0.1; // Simple base calculation
      point[`incidents-${projectId}`] = Math.max(
        0,
        15 - i * 0.5 + Math.sin(baseValue) * 3
      );
      point[`improvements-${projectId}`] =
        5 + i * 0.3 + Math.cos(baseValue) * 2;
      point[`scans-${projectId}`] = 20 + Math.sin(baseValue * 0.5) * 5;
      point[`tokens-${projectId}`] = 1000 + Math.sin(baseValue * 0.7) * 200;
      point[`quality-${projectId}`] = Math.min(
        100,
        70 + i * 0.8 + Math.cos(baseValue * 0.3) * 5
      );
      point[`velocity-${projectId}`] = 30 + Math.sin(baseValue * 0.2) * 10;
    });

    return point;
  });
};

// Simplified time formatting
const formatTimeAxis = (timestamp: number, range: string) => {
  const date = new Date(timestamp);
  if (range === '7d' || range === '30d') {
    return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
  }
  return date.toLocaleTimeString('en-US', {
    hour: '2-digit',
    minute: '2-digit'
  });
};

function ProjectFilterPopover({
  selectedProjects,
  onProjectsChange
}: {
  selectedProjects: string[];
  onProjectsChange: (projects: string[]) => void;
}) {
  const toggleProject = (projectId: string) => {
    const isSelected = selectedProjects.includes(projectId);
    onProjectsChange(
      isSelected
        ? selectedProjects.filter((id) => id !== projectId)
        : [...selectedProjects, projectId]
    );
  };

  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button variant='outline' size='sm' className='h-7 text-xs'>
          <Filter className='mr-1 h-3 w-3' />
          Projects
          {selectedProjects.length > 0 &&
            selectedProjects.length < PROJECTS.length && (
              <>
                <div className='bg-border mx-1 h-3 w-px' />
                <Badge variant='secondary' className='px-1 py-0 text-sm'>
                  {selectedProjects.length}
                </Badge>
              </>
            )}
        </Button>
      </PopoverTrigger>
      <PopoverContent
        className='w-64 p-0'
        align='start'
        side='bottom'
        sideOffset={4}
      >
        <Command>
          <CommandInput placeholder='Search projects...' className='text-xs' />
          <CommandList>
            <CommandEmpty className='text-xs'>No projects found.</CommandEmpty>
            <CommandGroup>
              {PROJECTS.map((project) => (
                <CommandItem
                  key={project.id}
                  onSelect={() => toggleProject(project.id)}
                  className='justify-start text-xs'
                >
                  <div
                    className={cn(
                      'mr-2 flex h-3 w-3 items-center justify-center',
                      selectedProjects.includes(project.id)
                        ? 'text-primary-foreground'
                        : 'opacity-50 [&_svg]:invisible'
                    )}
                  >
                    <Check className='h-2 w-2' />
                  </div>
                  <span>{project.name}</span>
                </CommandItem>
              ))}
            </CommandGroup>
            <CommandSeparator />
            <CommandGroup>
              <CommandItem
                onSelect={() => onProjectsChange(PROJECTS.map((p) => p.id))}
                className='text-xs'
              >
                Select All
              </CommandItem>
              <CommandItem
                onSelect={() => onProjectsChange([])}
                className='text-xs'
              >
                Clear All
              </CommandItem>
            </CommandGroup>
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  );
}

function MetricChart({
  title,
  icon: Icon,
  data,
  selectedProjects,
  metricKey,
  yAxisFormatter = (value: number) => value.toFixed(2),
  description,
  isExpanded,
  onToggleExpand
}: {
  title: string;
  icon: React.ElementType;
  data: any[];
  selectedProjects: string[];
  metricKey: string;
  yAxisFormatter?: (value: number) => string;
  description?: string;
  isExpanded?: boolean;
  onToggleExpand?: () => void;
}) {
  // Simplified Y-axis domain calculation
  const allValues = data.flatMap((point) =>
    selectedProjects.map((projectId) => point[`${metricKey}-${projectId}`] || 0)
  );
  const minValue = Math.min(...allValues);
  const maxValue = Math.max(...allValues);
  const padding = (maxValue - minValue) * 0.1;

  return (
    <Card
      className={cn(
        'bg-muted/50 rounded-lg border-0 px-0 py-4 shadow-none',
        isExpanded &&
          'bg-muted fixed inset-4 z-50 h-auto w-auto border shadow-2xl'
      )}
    >
      <CardHeader className='pb-4'>
        <div className='flex items-center justify-between'>
          <CardTitle className='flex items-center text-sm font-medium'>
            <Icon className='mr-2 h-4 w-4' />
            {title}
          </CardTitle>
          {onToggleExpand && (
            <Button
              variant='ghost'
              size='sm'
              onClick={onToggleExpand}
              className='h-6 w-6 p-0'
            >
              {isExpanded ? (
                <Minimize className='h-3 w-3' />
              ) : (
                <Expand className='h-3 w-3' />
              )}
            </Button>
          )}
        </div>
        {description && (
          <p className='text-muted-foreground text-xs'>{description}</p>
        )}
      </CardHeader>
      <CardContent>
        <div className='space-y-4'>
          <div className={cn('w-full', isExpanded ? 'h-96' : 'h-48')}>
            <ChartContainer config={chartConfig} className='h-full w-full'>
              <LineChart
                accessibilityLayer
                data={data}
              >
                <CartesianGrid vertical={false} />
                <XAxis
                  dataKey='time'
                  tickLine={false}
                  axisLine={false}
                  tickMargin={8}
                  className='text-xs'
                />
                <YAxis
                  tickLine={false}
                  axisLine={false}
                  tickMargin={8}
                  domain={[Math.max(0, minValue - padding), maxValue + padding]}
                  tickFormatter={yAxisFormatter}
                  className='text-xs'
                />
                <ChartTooltip
                  cursor={false}
                  content={<ChartTooltipContent />}
                />
                {selectedProjects.map((projectId) => (
                  <Line
                    key={projectId}
                    dataKey={`${metricKey}-${projectId}`}
                    type='monotone'
                    stroke={chartConfig[projectId]?.color}
                    strokeWidth={2}
                    dot={false}
                    connectNulls={false}
                    isAnimationActive={false}
                  />
                ))}
              </LineChart>
            </ChartContainer>
          </div>

          <div className='flex flex-wrap justify-center gap-4'>
            {selectedProjects.map((projectId) => {
              const project = PROJECTS.find((p) => p.id === projectId);
              const latestValue =
                data[data.length - 1]?.[`${metricKey}-${projectId}`];

              if (!project) return null;

              return (
                <div
                  key={projectId}
                  className='flex flex-col items-center space-y-1 text-center'
                >
                  <span className='text-sm font-semibold'>
                    {latestValue !== undefined
                      ? yAxisFormatter(Number(latestValue))
                      : '-'}
                  </span>
                  <div className='flex items-center space-x-2'>
                    <div
                      className='h-2 w-2 rounded-full'
                      style={{ backgroundColor: project.color }}
                    />
                    <span className='text-xs text-gray-600'>
                      {project.name}
                    </span>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

export default function MonitorPage() {
  const [selectedTimeRange, setSelectedTimeRange] = useState('1d');
  const [selectedProjects, setSelectedProjects] = useState<string[]>(
    PROJECTS.map((p) => p.id)
  );
  const [expandedChart, setExpandedChart] = useState<string | null>(null);

  const chartData = useMemo(
    () => generateTimeSeriesData(selectedTimeRange, selectedProjects),
    [selectedTimeRange, selectedProjects]
  );

  const handleToggleExpand = (chartKey: string) => {
    setExpandedChart(expandedChart === chartKey ? null : chartKey);
  };

  return (
    <PageContainer scrollable>
      <div className='w-full space-y-6'>
        <div className='flex items-center justify-between'>
          <div className='flex items-center gap-2'>
            <ProjectFilterPopover
              selectedProjects={selectedProjects}
              onProjectsChange={setSelectedProjects}
            />
          </div>

          <div className='bg-background flex gap-2 rounded-sm border p-1'>
            {TIME_RANGES.map((range) => (
              <Button
                key={range.value}
                variant={
                  selectedTimeRange === range.value ? 'default' : 'ghost'
                }
                size='sm'
                className={cn(
                  'h-6 px-2 text-xs font-medium',
                  selectedTimeRange === range.value
                    ? 'bg-primary text-primary-foreground shadow-sm'
                    : 'text-muted-foreground hover:text-foreground'
                )}
                onClick={() => setSelectedTimeRange(range.value)}
              >
                {range.label}
              </Button>
            ))}
          </div>
        </div>

        {selectedProjects.length > 0 ? (
          <div className='grid grid-cols-1 gap-6 lg:grid-cols-2'>
            <MetricChart
              title='Incidents Resolved'
              icon={AlertTriangle}
              data={chartData}
              selectedProjects={selectedProjects}
              metricKey='incidents'
              description='Number of incidents resolved over time'
              isExpanded={expandedChart === 'incidents'}
              onToggleExpand={() => handleToggleExpand('incidents')}
            />

            <MetricChart
              title='Improvements Merged'
              icon={GitMerge}
              data={chartData}
              selectedProjects={selectedProjects}
              metricKey='improvements'
              description='Code improvements and fixes merged'
              isExpanded={expandedChart === 'improvements'}
              onToggleExpand={() => handleToggleExpand('improvements')}
            />

            <MetricChart
              title='Scans Performed'
              icon={Zap}
              data={chartData}
              selectedProjects={selectedProjects}
              metricKey='scans'
              description='Number of security and quality scans executed'
              isExpanded={expandedChart === 'scans'}
              onToggleExpand={() => handleToggleExpand('scans')}
            />

            <MetricChart
              title='Token Spend'
              icon={DollarSign}
              data={chartData}
              selectedProjects={selectedProjects}
              metricKey='tokens'
              yAxisFormatter={(value) =>
                `${(Math.round(value) / 1000).toFixed(2)}K`
              }
              description='AI token consumption across projects'
              isExpanded={expandedChart === 'tokens'}
              onToggleExpand={() => handleToggleExpand('tokens')}
            />

            <MetricChart
              title='Code Quality Index'
              icon={Target}
              data={chartData}
              selectedProjects={selectedProjects}
              metricKey='quality'
              yAxisFormatter={(value) => `${value.toFixed(2)}%`}
              description='Overall code quality score'
              isExpanded={expandedChart === 'quality'}
              onToggleExpand={() => handleToggleExpand('quality')}
            />

            <MetricChart
              title='Project Velocity (AI vs Human)'
              icon={TrendingUp}
              data={chartData}
              selectedProjects={selectedProjects}
              metricKey='velocity'
              yAxisFormatter={(value) => `${value.toFixed(2)}% AI`}
              description='Percentage of merges done by AI agents'
              isExpanded={expandedChart === 'velocity'}
              onToggleExpand={() => handleToggleExpand('velocity')}
            />
          </div>
        ) : (
          <Card className='bg-muted/50 rounded-lg border-0 p-8 shadow-none'>
            <div className='text-center'>
              <Activity className='mx-auto h-12 w-12 text-gray-400' />
              <h3 className='mt-4 text-lg font-medium'>No Projects Selected</h3>
              <p className='text-muted-foreground mt-2'>
                Select one or more projects to view monitoring data.
              </p>
            </div>
          </Card>
        )}
      </div>
    </PageContainer>
  );
}
