import PageContainer from '@/components/layout/page-container';
import { SettingsSidebar } from '@/app/[org-slug]/settings/components/settings-sidebar';

interface SettingsLayoutProps {
  children: React.ReactNode;
  params: Promise<{
    'org-slug': string;
  }>;
}

export default async function SettingsLayout({
  children,
  params
}: SettingsLayoutProps) {
  const resolvedParams = await params;
  
  return (
    <PageContainer scrollable>
      <div className='flex h-full w-full'>
        <SettingsSidebar orgSlug={resolvedParams['org-slug']} />
        <div className='flex-1'>{children}</div>
      </div>
    </PageContainer>
  );
}
