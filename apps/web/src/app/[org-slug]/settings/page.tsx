import { redirect } from 'next/navigation';

interface SettingsPageProps {
  params: Promise<{
    'org-slug': string;
  }>;
}

export const metadata = {
  title: 'Backspace : Settings'
};

export default async function SettingsPage({ params }: SettingsPageProps) {
  const resolvedParams = await params;
  // Redirect to members section by default
  redirect(`/${resolvedParams['org-slug']}/settings/members`);
}
