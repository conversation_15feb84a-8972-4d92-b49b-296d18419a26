'use client';

import { Button } from '@/components/ui/button';
import { Users, Key, Database, User } from 'lucide-react';
import { usePathname } from 'next/navigation';
import Link from 'next/link';
import { cn } from '@/lib/utils';

const organizationSections = [
  { id: 'members', title: 'Members', icon: Users, href: '/members' },
  {
    id: 'integrations',
    title: 'Integrations',
    icon: Database,
    href: '/integrations'
  },
  { id: 'secrets', title: 'Secrets', icon: Key, href: '/secrets' }
  // { id: 'billing', title: 'Billing', icon: CreditCard, href: '/billing' }
];

const personalSections = [
  { id: 'profile', title: 'Profile', icon: User, href: '/profile' }
];

interface SettingsSidebarProps {
  orgSlug: string;
}

export function SettingsSidebar({ orgSlug }: SettingsSidebarProps) {
  const pathname = usePathname();

  return (
    <div className='w-64 pr-6'>
      <div className='space-y-6'>
        <div>
          <h3 className='text-muted-foreground mb-3 text-sm font-medium'>
            Organization
          </h3>
          <div className='space-y-1'>
            {organizationSections.map((section) => {
              const Icon = section.icon;
              const isActive = pathname.endsWith(section.href);
              return (
                <Button
                  key={section.id}
                  variant='ghost'
                  className={cn(
                    'w-full justify-start transition-colors',
                    !isActive && 'hover:bg-black/3 hover:dark:bg-white/8',
                    isActive && 'bg-black/5 dark:bg-white/10'
                  )}
                  asChild
                >
                  <Link href={`/${orgSlug}/settings${section.href}`}>
                    <Icon className='mr-2 h-4 w-4' />
                    {section.title}
                  </Link>
                </Button>
              );
            })}
          </div>
        </div>

        {/* Skipping personal */}
        {/* <div>
          <h3 className='text-muted-foreground mb-3 text-sm font-medium'>
            Personal
          </h3>
          <div className='space-y-1'>
            {personalSections.map((section) => {
              const Icon = section.icon;
              const isActive = pathname.endsWith(section.href);
              return (
                <Button
                  key={section.id}
                  variant={isActive ? 'secondary' : 'ghost'}
                  className='w-full justify-start'
                  asChild
                >
                  <Link href={`/${orgSlug}/settings${section.href}`}>
                    <Icon className='mr-2 h-4 w-4' />
                    {section.title}
                  </Link>
                </Button>
              );
            })}
          </div>
        </div> */}
      </div>
    </div>
  );
}
