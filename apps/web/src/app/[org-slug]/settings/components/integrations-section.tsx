'use client';

import { useState, useMemo, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';
import {
  Popover,
  PopoverContent,
  PopoverTrigger
} from '@/components/ui/popover';
import {
  Command,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList
} from '@/components/ui/command';
import {
  IconColumns3,
  IconCheck,
  IconPlus,
  IconSettings,
  IconTrash,
  IconBrandGithub,
  IconBrandSlack,
  IconChartLine,
  IconCalendar
} from '@tabler/icons-react';
import { cn } from '@/lib/utils';

// Types
interface Integration {
  id: string;
  appName: string;
  type: 'github' | 'slack' | 'linear' | 'jira' | 'discord';
  account: string;
  description: string;
  addedAt: string;
  addedBy: {
    name: string;
    avatar?: string;
    initials: string;
  };
}

type ColumnKey = 'app' | 'account' | 'description' | 'addedAt' | 'addedBy';
type VisibleColumns = Record<ColumnKey, boolean>;

const COLUMN_LABELS: Record<ColumnKey, string> = {
  app: 'App',
  account: 'Account',
  description: 'Description',
  addedAt: 'Added At',
  addedBy: 'Added By'
};

const getTypeIcon = (type: string) => {
  const icons = {
    github: IconBrandGithub,
    slack: IconBrandSlack,
    linear: IconChartLine,
    jira: IconChartLine,
    discord: IconBrandSlack
  };
  return icons[type as keyof typeof icons] || IconChartLine;
};

function IntegrationTableFilters({
  searchTerm,
  onSearchChange,
  visibleColumns,
  onVisibleColumnsChange,
  children
}: {
  searchTerm: string;
  onSearchChange: (value: string) => void;
  visibleColumns: VisibleColumns;
  onVisibleColumnsChange: (columns: VisibleColumns) => void;
  children?: React.ReactNode;
}) {
  return (
    <div className='flex items-center justify-between gap-3'>
      <div className='flex items-center gap-3'>
        <Input
          placeholder='Search integrations...'
          value={searchTerm}
          onChange={(e) => onSearchChange(e.target.value)}
          className='h-7 w-64 border-0 text-sm shadow-none'
        />
      </div>

      <div className='flex gap-2'>{children}</div>
    </div>
  );
}

function IntegrationRow({
  integration,
  visibleColumns,
  isHovered,
  onMouseEnter,
  onMouseLeave,
  onClick,
  onDelete
}: {
  integration: Integration;
  visibleColumns: VisibleColumns;
  isHovered: boolean;
  onMouseEnter: () => void;
  onMouseLeave: () => void;
  onClick: () => void;
  onDelete: () => void;
}) {
  const IconComponent = getTypeIcon(integration.type);

  return (
    <TableRow
      className={cn(
        'cursor-pointer transition-colors',
        isHovered && 'bg-muted/50'
      )}
      onMouseEnter={onMouseEnter}
      onMouseLeave={onMouseLeave}
      onClick={onClick}
    >
      {visibleColumns.app && (
        <TableCell className='py-2'>
          <div className='flex items-center gap-2'>
            <div className='flex items-center justify-center rounded-full'>
              <IconComponent className='h-4 w-4' />
            </div>
            <span className='text-sm font-medium'>{integration.appName}</span>
          </div>
        </TableCell>
      )}
      {visibleColumns.account && (
        <TableCell className='py-2'>
          <div className='text-sm font-medium'>{integration.account}</div>
        </TableCell>
      )}
      {visibleColumns.description && (
        <TableCell className='py-2'>
          <div className='text-muted-foreground text-sm'>
            {integration.description}
          </div>
        </TableCell>
      )}
      {visibleColumns.addedAt && (
        <TableCell className='text-muted-foreground py-2 text-sm'>
          <div className='flex items-center gap-1'>
            <IconCalendar className='h-3 w-3' />
            {integration.addedAt}
          </div>
        </TableCell>
      )}
      {visibleColumns.addedBy && (
        <TableCell className='py-2'>
          <div className='flex items-center gap-2'>
            <div className='bg-primary text-primary-foreground flex h-5 w-5 items-center justify-center rounded-full text-sm'>
              {integration.addedBy.initials}
            </div>
            <span className='text-sm'>{integration.addedBy.name}</span>
          </div>
        </TableCell>
      )}
      <TableCell className='py-2' onClick={(e) => e.stopPropagation()}>
        <Button
          variant='ghost'
          size='sm'
          className='text-muted-foreground hover:text-destructive h-6 w-6 p-0'
          onClick={onDelete}
        >
          <IconTrash className='h-3 w-3' />
        </Button>
      </TableCell>
    </TableRow>
  );
}

export function IntegrationsSection() {
  const [searchTerm, setSearchTerm] = useState('');
  const [visibleColumns, setVisibleColumns] = useState<VisibleColumns>({
    app: true,
    account: true,
    description: true,
    addedAt: true,
    addedBy: true
  });
  const [hoveredIntegration, setHoveredIntegration] = useState<string | null>(
    null
  );
  const [isClient, setIsClient] = useState(false);
  const [integrations, setIntegrations] = useState<Integration[]>([]);

  useEffect(() => {
    setIsClient(true);
    // Initialize integrations data - only connected ones
    setIntegrations([
      {
        id: 'github-1',
        appName: 'GitHub',
        type: 'github',
        account: '@rolandgvc',
        description:
          'Has access to open_deep_research, flaxvision, and 60 more repositories',
        addedAt: 'Mar 15, 2024',
        addedBy: {
          name: 'Roland Gavrilescu',
          initials: 'RG'
        }
      },
      {
        id: 'github-2',
        appName: 'GitHub',
        type: 'github',
        account: '@backspace-org',
        description:
          'Has access to deep-research-code, nextjs-starter-test, codegen',
        addedAt: 'Apr 2, 2024',
        addedBy: {
          name: 'Sarah Chen',
          initials: 'SC'
        }
      },
      {
        id: 'slack-1',
        appName: 'Slack',
        type: 'slack',
        account: 'backspace-org',
        description: 'Connected to 5 channels in eng-team.slack.com',
        addedAt: 'Apr 8, 2024',
        addedBy: {
          name: 'Alex Rodriguez',
          initials: 'AR'
        }
      },
      {
        id: 'linear-1',
        appName: 'Linear',
        type: 'linear',
        account: 'backspace-org',
        description: 'Access to 3 projects',
        addedAt: 'Apr 12, 2024',
        addedBy: {
          name: 'Jordan Kim',
          initials: 'JK'
        }
      }
    ]);
  }, []);

  const filteredIntegrations = useMemo(() => {
    return integrations.filter((integration) => {
      const matchesSearch = [
        integration.appName,
        integration.account,
        integration.description
      ].some((text) => text.toLowerCase().includes(searchTerm.toLowerCase()));
      return matchesSearch;
    });
  }, [searchTerm, integrations]);

  const handleIntegrationClick = (integrationId: string) => {
    // Handle integration click if needed
  };

  const handleDeleteIntegration = (integrationId: string) => {
    setIntegrations((prev) =>
      prev.filter((integration) => integration.id !== integrationId)
    );
  };

  if (!isClient) {
    return <></>;
  }

  return (
    <div className='space-y-6'>
      {/* Integrations Table */}
      <div className='space-y-4'>
        <IntegrationTableFilters
          searchTerm={searchTerm}
          onSearchChange={setSearchTerm}
          visibleColumns={visibleColumns}
          onVisibleColumnsChange={setVisibleColumns}
        >
          <Button 
            size='sm' 
            className='h-7 text-sm'
            onClick={() => window.open('https://github.com/apps/backspace-agent/installations/new', '_blank')}
          >
            <IconPlus className='mr-1 h-3 w-3' />
            Add Integration
          </Button>
        </IntegrationTableFilters>

        <Table>
          <TableHeader>
            <TableRow>
              {Object.entries(visibleColumns).map(
                ([key, visible]) =>
                  visible && (
                    <TableHead key={key} className='text-sm font-medium'>
                      {COLUMN_LABELS[key as ColumnKey]}
                    </TableHead>
                  )
              )}
              <TableHead className='w-8'></TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredIntegrations.map((integration) => (
              <IntegrationRow
                key={integration.id}
                integration={integration}
                visibleColumns={visibleColumns}
                isHovered={hoveredIntegration === integration.id}
                onMouseEnter={() => setHoveredIntegration(integration.id)}
                onMouseLeave={() => setHoveredIntegration(null)}
                onClick={() => handleIntegrationClick(integration.id)}
                onDelete={() => handleDeleteIntegration(integration.id)}
              />
            ))}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}
