'use client';

import { useState, useMemo, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';
import {
  FilterPopover,
  createFilterTrigger,
  FilterItem,
  FilterActions,
  CommandGroup,
  CommandItem,
  CommandSeparator
} from '@/components/ui/filter-popover';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  IconFilter,
  IconColumns3,
  IconCheck,
  IconPlus,
  IconSettings,
  IconUser,
  IconMail,
  IconCalendar
} from '@tabler/icons-react';
import { cn } from '@/lib/utils';
import { createClient } from '@/lib/supabase/client';
import type { Session, User } from '@supabase/supabase-js';

// Types
interface Member {
  id: string;
  name: string;
  email: string;
  avatar?: string;
  joinedDate: string;
  role: 'owner' | 'admin' | 'member';
  status: 'active' | 'pending' | 'inactive';
  lastActive: string;
}

type ColumnKey =
  | 'name'
  | 'role'
  | 'status'
  | 'joinedDate'
  | 'lastActive'
  | 'email';
type VisibleColumns = Record<ColumnKey, boolean>;

const ROLE_OPTIONS = ['owner', 'admin', 'member'];
const STATUS_OPTIONS = ['active', 'pending', 'inactive'];

const COLUMN_LABELS: Record<ColumnKey, string> = {
  name: 'Name',
  role: 'Role',
  status: 'Status',
  joinedDate: 'Joined',
  lastActive: 'Last Active',
  email: 'Email'
};

const getRoleBadge = (role: string) => {
  const badges = {
    owner: (
      <Badge className='bg-purple-100 px-2 py-0 text-xs text-purple-800 dark:bg-purple-900/30 dark:text-purple-300'>
        OWNER
      </Badge>
    ),
    admin: (
      <Badge className='bg-blue-100 px-2 py-0 text-xs text-blue-800 dark:bg-blue-900/30 dark:text-blue-300'>
        ADMIN
      </Badge>
    ),
    member: (
      <Badge variant='outline' className='px-2 py-0 text-xs'>
        MEMBER
      </Badge>
    )
  };
  return badges[role as keyof typeof badges] || badges.member;
};

const getStatusBadge = (status: string) => {
  const badges = {
    active: (
      <Badge className='bg-green-100 px-2 py-0 text-xs text-green-800 dark:bg-green-900/30 dark:text-green-300'>
        ACTIVE
      </Badge>
    ),
    pending: (
      <Badge className='bg-yellow-100 px-2 py-0 text-xs text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300'>
        PENDING
      </Badge>
    ),
    inactive: (
      <Badge variant='secondary' className='px-2 py-0 text-xs'>
        INACTIVE
      </Badge>
    )
  };
  return badges[status as keyof typeof badges] || badges.active;
};

function StatusFilterPopover({
  label,
  value,
  onChange,
  options
}: {
  label: string;
  value: string[];
  onChange: (value: string[]) => void;
  options: string[];
}) {
  const toggleFilter = (option: string) => {
    onChange(
      value.includes(option)
        ? value.filter((v) => v !== option)
        : [...value, option]
    );
  };

  const selectAll = () => {
    onChange(options);
  };

  const clearAll = () => {
    onChange([]);
  };

  const trigger = createFilterTrigger({
    label
  });

  return (
    <FilterPopover
      trigger={trigger}
      searchPlaceholder={`Search ${label.toLowerCase()}...`}
    >
      <CommandGroup>
        {options.map((option) => (
          <FilterItem
            key={option}
            label={option}
            isSelected={value.includes(option)}
            onToggle={() => toggleFilter(option)}
          />
        ))}
      </CommandGroup>
      <FilterActions
        onSelectAll={selectAll}
        onClearAll={clearAll}
        hasSelections={value.length > 0}
      />
    </FilterPopover>
  );
}

function ColumnsFilterPopover({
  visibleColumns,
  onVisibleColumnsChange
}: {
  visibleColumns: VisibleColumns;
  onVisibleColumnsChange: (columns: VisibleColumns) => void;
}) {
  const toggleColumn = (key: keyof VisibleColumns) => {
    onVisibleColumnsChange({
      ...visibleColumns,
      [key]: !visibleColumns[key]
    });
  };

  const selectAll = () => {
    const allVisible = Object.keys(visibleColumns).reduce(
      (acc, key) => ({ ...acc, [key]: true }),
      {} as VisibleColumns
    );
    onVisibleColumnsChange(allVisible);
  };

  const clearAll = () => {
    const allHidden = Object.keys(visibleColumns).reduce(
      (acc, key) => ({ ...acc, [key]: false }),
      {} as VisibleColumns
    );
    onVisibleColumnsChange(allHidden);
  };

  const trigger = createFilterTrigger({
    label: 'Columns',
    icon: IconColumns3
  });

  return (
    <FilterPopover trigger={trigger} searchPlaceholder='Search columns...'>
      <CommandGroup>
        {Object.entries(visibleColumns).map(([key, visible]) => (
          <FilterItem
            key={key}
            label={COLUMN_LABELS[key as ColumnKey]}
            isSelected={visible}
            onToggle={() => toggleColumn(key as keyof VisibleColumns)}
          />
        ))}
      </CommandGroup>
      <FilterActions
        onSelectAll={selectAll}
        onClearAll={clearAll}
        hasSelections={Object.values(visibleColumns).some(Boolean)}
      />
    </FilterPopover>
  );
}

function MemberTableFilters({
  searchTerm,
  onSearchChange,
  roleFilter,
  onRoleFilterChange,
  statusFilter,
  onStatusFilterChange,
  visibleColumns,
  onVisibleColumnsChange,
  children
}: {
  searchTerm: string;
  onSearchChange: (value: string) => void;
  roleFilter: string[];
  onRoleFilterChange: (value: string[]) => void;
  statusFilter: string[];
  onStatusFilterChange: (value: string[]) => void;
  visibleColumns: VisibleColumns;
  onVisibleColumnsChange: (columns: VisibleColumns) => void;
  children?: React.ReactNode;
}) {
  return (
    <div className='flex items-center justify-between gap-3'>
      <div className='flex items-center gap-3'>
        <div className='flex items-center gap-2'>
          <StatusFilterPopover
            label='Role'
            value={roleFilter}
            onChange={onRoleFilterChange}
            options={ROLE_OPTIONS}
          />

          <StatusFilterPopover
            label='Status'
            value={statusFilter}
            onChange={onStatusFilterChange}
            options={STATUS_OPTIONS}
          />

          <ColumnsFilterPopover
            visibleColumns={visibleColumns}
            onVisibleColumnsChange={onVisibleColumnsChange}
          />
        </div>

        <Input
          placeholder='Search members...'
          value={searchTerm}
          onChange={(e) => onSearchChange(e.target.value)}
          className='h-7 w-64 border-0 text-xs shadow-none'
        />
      </div>

      {children && <div className='flex items-center gap-2'>{children}</div>}
    </div>
  );
}

function MemberRow({
  member,
  visibleColumns,
  isHovered,
  onMouseEnter,
  onMouseLeave,
  onClick
}: {
  member: Member;
  visibleColumns: VisibleColumns;
  isHovered: boolean;
  onMouseEnter: () => void;
  onMouseLeave: () => void;
  onClick: () => void;
}) {
  return (
    <TableRow
      className={cn(
        'cursor-pointer transition-colors',
        isHovered && 'bg-black/5 dark:bg-white/5'
      )}
      onMouseEnter={onMouseEnter}
      onMouseLeave={onMouseLeave}
      onClick={onClick}
    >
      {visibleColumns.name && (
        <TableCell className='py-2'>
          <div className='flex items-center gap-2'>
            <Avatar className='h-6 w-6'>
              <AvatarImage src={member.avatar} />
              <AvatarFallback className='text-xs'>
                {member.name
                  .split(' ')
                  .map((n) => n[0])
                  .join('')}
              </AvatarFallback>
            </Avatar>
            <div>
              <div className='text-sm font-medium'>{member.name}</div>
              <div className='text-muted-foreground text-xs'>
                {member.email}
              </div>
            </div>
          </div>
        </TableCell>
      )}
      {visibleColumns.role && (
        <TableCell className='py-2'>{getRoleBadge(member.role)}</TableCell>
      )}
      {visibleColumns.status && (
        <TableCell className='py-2'>{getStatusBadge(member.status)}</TableCell>
      )}
      {visibleColumns.email && (
        <TableCell className='py-2'>
          <div className='text-muted-foreground flex items-center gap-1 text-xs'>
            <IconMail className='h-3 w-3' />
            {member.email}
          </div>
        </TableCell>
      )}
      {visibleColumns.joinedDate && (
        <TableCell className='text-muted-foreground py-2 text-xs'>
          <div className='flex items-center gap-1'>
            <IconCalendar className='h-3 w-3' />
            {member.joinedDate}
          </div>
        </TableCell>
      )}
      {visibleColumns.lastActive && (
        <TableCell className='text-muted-foreground py-2 text-xs'>
          {member.lastActive}
        </TableCell>
      )}
    </TableRow>
  );
}

export function MembersSection() {
  const [session, setSession] = useState<Session | null>(null);
  const [user, setUser] = useState<User | null>(null);
  const [authLoading, setAuthLoading] = useState(true);

  // Initialize auth state
  useEffect(() => {
    const supabase = createClient();

    // Get initial session
    supabase.auth.getSession().then(({ data: { session } }) => {
      setSession(session);
      setUser(session?.user ?? null);
      setAuthLoading(false);
    });

    // Listen for auth changes
    const {
      data: { subscription }
    } = supabase.auth.onAuthStateChange((_event, session) => {
      setSession(session);
      setUser(session?.user ?? null);
      setAuthLoading(false);
    });

    return () => subscription.unsubscribe();
  }, []);
  const [searchTerm, setSearchTerm] = useState('');
  const [roleFilter, setRoleFilter] = useState<string[]>([]);
  const [statusFilter, setStatusFilter] = useState<string[]>([]);
  const [visibleColumns, setVisibleColumns] = useState<VisibleColumns>({
    name: true,
    role: true,
    status: true,
    email: false,
    joinedDate: true,
    lastActive: true
  });
  const [hoveredMember, setHoveredMember] = useState<string | null>(null);
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  const members: Member[] = useMemo(
    () => [
      {
        id: '1',
        name:
          user?.user_metadata?.full_name ||
          user?.email?.split('@')[0] ||
          'Roland Gavrilescu',
        email: user?.email || '<EMAIL>',
        avatar: user?.user_metadata?.avatar_url,
        joinedDate: '23 Apr 19:52',
        role: 'owner',
        status: 'active',
        lastActive: '2 hours ago'
      },
      {
        id: '2',
        name: 'Sarah Chen',
        email: '<EMAIL>',
        avatar: 'https://api.slingacademy.com/public/sample-users/1.png',
        joinedDate: '15 Mar 14:30',
        role: 'admin',
        status: 'active',
        lastActive: '1 day ago'
      },
      {
        id: '3',
        name: 'Alex Rodriguez',
        email: '<EMAIL>',
        avatar: 'https://api.slingacademy.com/public/sample-users/2.png',
        joinedDate: '8 Feb 09:15',
        role: 'member',
        status: 'pending',
        lastActive: '3 days ago'
      }
    ],
    [user]
  );

  const filteredMembers = useMemo(() => {
    return members.filter((member) => {
      const matchesSearch = [member.name, member.email].some((text) =>
        text.toLowerCase().includes(searchTerm.toLowerCase())
      );

      // Show all if all options are selected (default behavior) or if the specific option is selected
      const matchesRole =
        roleFilter.length === 0 ||
        roleFilter.length === ROLE_OPTIONS.length ||
        roleFilter.includes(member.role);
      const matchesStatus =
        statusFilter.length === 0 ||
        statusFilter.length === STATUS_OPTIONS.length ||
        statusFilter.includes(member.status);

      return matchesSearch && matchesRole && matchesStatus;
    });
  }, [searchTerm, roleFilter, statusFilter, members]);

  const handleMemberClick = (memberId: string) => {
    // Handle member click if needed
  };

  if (!isClient) {
    return <></>;
  }

  return (
    <div className='space-y-6'>
      {/* Members Table */}
      <div className='space-y-4'>
        <MemberTableFilters
          searchTerm={searchTerm}
          onSearchChange={setSearchTerm}
          roleFilter={roleFilter}
          onRoleFilterChange={setRoleFilter}
          statusFilter={statusFilter}
          onStatusFilterChange={setStatusFilter}
          visibleColumns={visibleColumns}
          onVisibleColumnsChange={setVisibleColumns}
        >
          <Button variant='outline' size='sm' className='h-7 text-sm'>
            <IconSettings className='mr-1 h-3 w-3' />
            Settings
          </Button>
          <Button size='sm' className='h-7 text-sm'>
            <IconPlus className='mr-1 h-3 w-3' />
            Invite Member
          </Button>
        </MemberTableFilters>

        <Table>
          <TableHeader>
            <TableRow>
              {Object.entries(visibleColumns).map(
                ([key, visible]) =>
                  visible && (
                    <TableHead key={key} className='text-sm font-medium'>
                      {COLUMN_LABELS[key as ColumnKey]}
                    </TableHead>
                  )
              )}
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredMembers.map((member) => (
              <MemberRow
                key={member.id}
                member={member}
                visibleColumns={visibleColumns}
                isHovered={hoveredMember === member.id}
                onMouseEnter={() => setHoveredMember(member.id)}
                onMouseLeave={() => setHoveredMember(null)}
                onClick={() => handleMemberClick(member.id)}
              />
            ))}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}
