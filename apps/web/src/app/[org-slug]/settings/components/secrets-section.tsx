'use client';

import { useState, useMemo, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';
import {
  Popover,
  PopoverContent,
  PopoverTrigger
} from '@/components/ui/popover';
import {
  Command,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList
} from '@/components/ui/command';
import {
  IconColumns3,
  IconCheck,
  IconPlus,
  IconSettings,
  IconTrash,
  IconKey,
  IconCalendar
} from '@tabler/icons-react';
import { cn } from '@/lib/utils';

// Types
interface Secret {
  id: string;
  name: string;
  addedAt: string;
  addedBy: {
    name: string;
    avatar?: string;
    initials: string;
  };
  lastUsed?: string;
}

type ColumnKey = 'name' | 'addedAt' | 'addedBy' | 'lastUsed';
type VisibleColumns = Record<ColumnKey, boolean>;

const COLUMN_LABELS: Record<ColumnKey, string> = {
  name: 'Name',
  addedAt: 'Added At',
  addedBy: 'Added By',
  lastUsed: 'Last Used'
};

function SecretTableFilters({
  searchTerm,
  onSearchChange,
  visibleColumns,
  onVisibleColumnsChange,
  children
}: {
  searchTerm: string;
  onSearchChange: (value: string) => void;
  visibleColumns: VisibleColumns;
  onVisibleColumnsChange: (columns: VisibleColumns) => void;
  children?: React.ReactNode;
}) {
  return (
    <div className='flex items-center justify-between gap-3'>
      <div className='flex items-center gap-3'>
        <Input
          placeholder='Search secrets...'
          value={searchTerm}
          onChange={(e) => onSearchChange(e.target.value)}
          className='h-7 w-64 border-0 text-sm shadow-none'
        />
      </div>

      <div className='flex gap-2'>{children}</div>
    </div>
  );
}

function SecretRow({
  secret,
  visibleColumns,
  isHovered,
  onMouseEnter,
  onMouseLeave,
  onClick,
  onDelete
}: {
  secret: Secret;
  visibleColumns: VisibleColumns;
  isHovered: boolean;
  onMouseEnter: () => void;
  onMouseLeave: () => void;
  onClick: () => void;
  onDelete: () => void;
}) {
  return (
    <TableRow
      className={cn(
        'cursor-pointer transition-colors',
        isHovered && 'bg-muted/50'
      )}
      onMouseEnter={onMouseEnter}
      onMouseLeave={onMouseLeave}
      onClick={onClick}
    >
      {visibleColumns.name && (
        <TableCell className='py-2'>
          <div className='flex items-center gap-2'>
            <IconKey className='text-muted-foreground h-4 w-4' />
            <div className='font-mono text-sm font-medium'>{secret.name}</div>
          </div>
        </TableCell>
      )}
      {visibleColumns.addedAt && (
        <TableCell className='text-muted-foreground py-2 text-sm'>
          <div className='flex items-center gap-1'>
            <IconCalendar className='h-3 w-3' />
            {secret.addedAt}
          </div>
        </TableCell>
      )}
      {visibleColumns.addedBy && (
        <TableCell className='py-2'>
          <div className='flex items-center gap-2'>
            <div className='bg-primary text-primary-foreground flex h-5 w-5 items-center justify-center rounded-full text-sm'>
              {secret.addedBy.initials}
            </div>
            <span className='text-sm'>{secret.addedBy.name}</span>
          </div>
        </TableCell>
      )}
      {visibleColumns.lastUsed && (
        <TableCell className='text-muted-foreground py-2 text-sm'>
          {secret.lastUsed || 'Never'}
        </TableCell>
      )}
      <TableCell className='py-2' onClick={(e) => e.stopPropagation()}>
        <Button
          variant='ghost'
          size='sm'
          className='text-muted-foreground hover:text-destructive h-6 w-6 p-0'
          onClick={onDelete}
        >
          <IconTrash className='h-3 w-3' />
        </Button>
      </TableCell>
    </TableRow>
  );
}

export function SecretsSection() {
  const [searchTerm, setSearchTerm] = useState('');
  const [visibleColumns, setVisibleColumns] = useState<VisibleColumns>({
    name: true,
    addedAt: true,
    addedBy: true,
    lastUsed: false
  });
  const [hoveredSecret, setHoveredSecret] = useState<string | null>(null);
  const [isClient, setIsClient] = useState(false);
  const [secrets, setSecrets] = useState<Secret[]>([]);

  useEffect(() => {
    setIsClient(true);
    // Initialize secrets data
    setSecrets([
      {
        id: '1',
        name: 'E2B_API_KEY',
        addedAt: 'Apr 10, 2025',
        addedBy: {
          name: 'Roland Gavrilescu',
          initials: 'RG'
        },
        lastUsed: '2 hours ago'
      },
      {
        id: '2',
        name: 'OPENAI_API_KEY',
        addedAt: 'Apr 10, 2025',
        addedBy: {
          name: 'Sarah Chen',
          initials: 'SC'
        },
        lastUsed: '1 day ago'
      },
      {
        id: '3',
        name: 'DATABASE_URL',
        addedAt: 'Apr 9, 2025',
        addedBy: {
          name: 'Alex Rodriguez',
          initials: 'AR'
        },
        lastUsed: '3 hours ago'
      },
      {
        id: '4',
        name: 'AUTH_SECRET',
        addedAt: 'Apr 8, 2025',
        addedBy: {
          name: 'Jordan Kim',
          initials: 'JK'
        }
      },
      {
        id: '5',
        name: 'SSL_CERTIFICATE',
        addedAt: 'Apr 7, 2025',
        addedBy: {
          name: 'Morgan Davis',
          initials: 'MD'
        },
        lastUsed: '12 hours ago'
      },
      {
        id: '6',
        name: 'CONVEX_DEPLOYMENT',
        addedAt: 'Apr 6, 2025',
        addedBy: {
          name: 'Roland Gavrilescu',
          initials: 'RG'
        },
        lastUsed: '5 minutes ago'
      },
      {
        id: '7',
        name: 'TEST_API_KEY',
        addedAt: 'Apr 5, 2025',
        addedBy: {
          name: 'Sarah Chen',
          initials: 'SC'
        },
        lastUsed: '2 days ago'
      }
    ]);
  }, []);

  const filteredSecrets = useMemo(() => {
    return secrets.filter((secret) => {
      const matchesSearch = secret.name
        .toLowerCase()
        .includes(searchTerm.toLowerCase());
      return matchesSearch;
    });
  }, [searchTerm, secrets]);

  const handleSecretClick = (secretId: string) => {
    // Handle secret click if needed
  };

  const handleDeleteSecret = (secretId: string) => {
    setSecrets((prev) => prev.filter((secret) => secret.id !== secretId));
  };

  if (!isClient) {
    return <></>;
  }

  return (
    <div className='space-y-6'>
      {/* Secrets Table */}
      <div className='space-y-4'>
        <SecretTableFilters
          searchTerm={searchTerm}
          onSearchChange={setSearchTerm}
          visibleColumns={visibleColumns}
          onVisibleColumnsChange={setVisibleColumns}
        >
          <Button size='sm' className='h-7 text-sm'>
            <IconPlus className='mr-1 h-3 w-3' />
            Add Secret
          </Button>
        </SecretTableFilters>

        <Table>
          <TableHeader>
            <TableRow>
              {Object.entries(visibleColumns).map(
                ([key, visible]) =>
                  visible && (
                    <TableHead key={key} className='text-sm font-medium'>
                      {COLUMN_LABELS[key as ColumnKey]}
                    </TableHead>
                  )
              )}
              <TableHead className='w-8'></TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredSecrets.map((secret) => (
              <SecretRow
                key={secret.id}
                secret={secret}
                visibleColumns={visibleColumns}
                isHovered={hoveredSecret === secret.id}
                onMouseEnter={() => setHoveredSecret(secret.id)}
                onMouseLeave={() => setHoveredSecret(null)}
                onClick={() => handleSecretClick(secret.id)}
                onDelete={() => handleDeleteSecret(secret.id)}
              />
            ))}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}
