'use client';

import { useState, useEffect } from 'react';
import * as React from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';
import {
  Users,
  Shield,
  Key,
  Brain,
  Database,
  BarChart3,
  FileText,
  CreditCard,
  User as UserIcon,
  Flag,
  Search,
  MoreHorizontal,
  Trash2,
  Settings as SettingsIcon,
  Github,
  Slack,
  LineChart
} from 'lucide-react';
import { createClient } from '@/lib/supabase/client';
import type { Session, User } from '@supabase/supabase-js';

const organizationSections = [
  { id: 'members', title: 'Members', icon: Users },
  { id: 'integrations', title: 'Integrations', icon: Database },
  { id: 'secrets', title: 'Secrets', icon: Key },
  { id: 'billing', title: 'Billing', icon: CreditCard }
];

const personalSections = [
  { id: 'profile', title: 'Profile', icon: UserIcon },
  { id: 'feature-flags', title: 'Feature flags', icon: Flag }
];

export function SettingsContent() {
  const [activeSection, setActiveSection] = useState('members');
  const [searchTerm, setSearchTerm] = useState('');
  const [session, setSession] = useState<Session | null>(null);
  const [user, setUser] = useState<User | null>(null);
  const [authLoading, setAuthLoading] = useState(true);

  // Initialize auth state
  useEffect(() => {
    const supabase = createClient();

    // Get initial session
    supabase.auth.getSession().then(({ data: { session } }) => {
      setSession(session);
      setUser(session?.user ?? null);
      setAuthLoading(false);
    });

    // Listen for auth changes
    const {
      data: { subscription }
    } = supabase.auth.onAuthStateChange((_event, session) => {
      setSession(session);
      setUser(session?.user ?? null);
      setAuthLoading(false);
    });

    return () => subscription.unsubscribe();
  }, []);

  const renderSectionContent = () => {
    switch (activeSection) {
      case 'members':
        return <MembersSection searchTerm={searchTerm} user={user} />;
      case 'permissions':
        return <PermissionsSection />;
      case 'integrations':
        return <IntegrationsSection searchTerm={searchTerm} />;
      case 'secrets':
        return <SecretsSection searchTerm={searchTerm} />;
      case 'billing':
        return <BillingSection />;
      case 'profile':
        return <ProfileSection user={user} />;
      case 'feature-flags':
        return <FeatureFlagsSection />;
      default:
        return <MembersSection searchTerm={searchTerm} user={user} />;
    }
  };

  return (
    <div className='flex h-full w-full'>
      {/* Sidebar */}
      <div className='bg-muted/10 w-64 pr-6'>
        <div className='space-y-6'>
          <div>
            <h3 className='text-muted-foreground mb-3 text-sm font-medium'>
              Organization
            </h3>
            <div className='space-y-1'>
              {organizationSections.map((section) => {
                const Icon = section.icon;
                return (
                  <Button
                    key={section.id}
                    variant={
                      activeSection === section.id ? 'secondary' : 'ghost'
                    }
                    className='w-full justify-start'
                    onClick={() => setActiveSection(section.id)}
                  >
                    <Icon className='mr-2 h-4 w-4' />
                    {section.title}
                  </Button>
                );
              })}
            </div>
          </div>

          <div>
            <h3 className='text-muted-foreground mb-3 text-sm font-medium'>
              Personal
            </h3>
            <div className='space-y-1'>
              {personalSections.map((section) => {
                const Icon = section.icon;
                return (
                  <Button
                    key={section.id}
                    variant={
                      activeSection === section.id ? 'secondary' : 'ghost'
                    }
                    className='w-full justify-start'
                    onClick={() => setActiveSection(section.id)}
                  >
                    <Icon className='mr-2 h-4 w-4' />
                    {section.title}
                  </Button>
                );
              })}
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className='flex-1'>
        {/* Header with Search and Invite for Members */}
        <div className='mb-6 flex items-center justify-between gap-4'>
          <div className='relative max-w-md flex-1'>
            <Search className='text-muted-foreground absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2 transform' />
            <Input
              placeholder={
                activeSection === 'members'
                  ? 'Find members'
                  : activeSection === 'integrations'
                    ? 'Find integrations'
                    : activeSection === 'secrets'
                      ? 'Search secrets'
                      : 'Search'
              }
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className='pl-10'
            />
          </div>
          <div className='flex gap-2'>
            {activeSection === 'secrets' && (
              <Button variant='outline'>Bulk import secrets</Button>
            )}
            <Button>
              {activeSection === 'members'
                ? 'Invite member'
                : activeSection === 'integrations'
                  ? 'Add integration'
                  : activeSection === 'secrets'
                    ? 'Add secret'
                    : 'Add'}
            </Button>
          </div>
        </div>

        {/* Section Content */}
        {renderSectionContent()}
      </div>
    </div>
  );
}

function MembersSection({
  searchTerm,
  user
}: {
  searchTerm: string;
  user: User | null;
}) {
  const members = [
    {
      id: '1',
      name:
        user?.user_metadata?.full_name || user?.email?.split('@')[0] || 'User',
      email: user?.email || '<EMAIL>',
      avatar: user?.user_metadata?.avatar_url,
      joinedDate: 'Joined 23 Apr 19:52',
      role: 'Owner'
    }
  ];

  const filteredMembers = members.filter(
    (member) =>
      member.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      member.email.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <div className='space-y-1'>
      {filteredMembers.map((member) => (
        <div
          key={member.id}
          className='bg-card hover:bg-accent/50 flex items-center justify-between rounded-lg border p-4 transition-colors'
        >
          <div className='flex items-center space-x-3'>
            <Avatar className='h-10 w-10'>
              <AvatarImage src={member.avatar} alt={member.name} />
              <AvatarFallback className='bg-primary text-primary-foreground'>
                {member.name
                  .split(' ')
                  .map((n: string) => n[0])
                  .join('')}
              </AvatarFallback>
            </Avatar>
            <div className='space-y-0.5'>
              <div className='text-sm font-medium'>{member.name}</div>
              <div className='text-muted-foreground text-sm'>
                {member.email}
              </div>
              <div className='text-muted-foreground text-xs'>
                {member.joinedDate}
              </div>
            </div>
          </div>
          <div className='flex items-center space-x-1'>
            <Button variant='ghost' size='icon' className='h-8 w-8'>
              <Trash2 className='h-4 w-4' />
            </Button>
            <Button variant='ghost' size='icon' className='h-8 w-8'>
              <SettingsIcon className='h-4 w-4' />
            </Button>
          </div>
        </div>
      ))}
    </div>
  );
}

function PermissionsSection() {
  return (
    <div className='space-y-4'>
      <Card>
        <CardHeader>
          <CardTitle>Permission Groups</CardTitle>
          <CardDescription>
            Manage permission groups and access levels for your organization.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <p className='text-muted-foreground'>
            Permission groups configuration will be available here.
          </p>
        </CardContent>
      </Card>
    </div>
  );
}

function ApiKeysSection() {
  return (
    <div className='space-y-4'>
      <Card>
        <CardHeader>
          <CardTitle>API Keys</CardTitle>
          <CardDescription>
            Manage your API keys for external integrations.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <p className='text-muted-foreground'>
            API keys management will be available here.
          </p>
        </CardContent>
      </Card>
    </div>
  );
}

function IntegrationsSection({ searchTerm }: { searchTerm: string }) {
  // Flatten all integrations into a single array
  const allIntegrations = [
    // GitHub integrations
    {
      id: 'github-1',
      type: 'GitHub',
      icon: Github,
      name: '@rolandgvc',
      username: '@rolandgvc',
      description: 'Has access to open_deep_research, flaxvision, and 60 more',
      connectedCount: 32,
      totalCount: 64,
      itemType: 'repositories',
      status: 'Connected',
      lastSync: 'Connected 2 hours ago'
    },
    {
      id: 'github-2',
      type: 'GitHub',
      icon: Github,
      name: '@backspace-org',
      username: '@backspace-org',
      description:
        'Has access to deep-research-code, nextjs-starter-test, codegen',
      connectedCount: 3,
      totalCount: 15,
      itemType: 'repositories',
      status: 'Connected',
      lastSync: 'Connected 1 hour ago'
    },
    // Slack integrations
    {
      id: 'slack-2',
      type: 'Slack',
      icon: Slack,
      name: 'Connect to Slack',
      username: 'eng-team.slack.com',
      description: '',
      connectedCount: 0,
      totalCount: 0,
      itemType: 'channels',
      status: 'Not Connected',
      lastSync: null
    },
    // Linear integrations
    {
      id: 'linear-1',
      type: 'Linear',
      icon: LineChart,
      name: 'Connect to Linear',
      username: 'research-lab',
      description: '',
      connectedCount: 0,
      totalCount: 0,
      itemType: 'projects',
      status: 'Not Connected',
      lastSync: null
    }
  ];

  const filteredIntegrations = allIntegrations.filter(
    (integration) =>
      integration.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      integration.username.toLowerCase().includes(searchTerm.toLowerCase()) ||
      integration.description
        .toLowerCase()
        .includes(searchTerm.toLowerCase()) ||
      integration.type.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <div className='space-y-1'>
      {filteredIntegrations.map((integration) => {
        const Icon = integration.icon;
        const isConnected = integration.status === 'Connected';

        return (
          <div
            key={integration.id}
            className='bg-card hover:bg-accent/50 flex items-center justify-between rounded-lg border p-4 transition-colors'
          >
            <div className='flex items-center space-x-3'>
              <div
                className={`flex h-10 w-10 items-center justify-center rounded-full ${
                  isConnected
                    ? 'bg-green-100 text-green-600'
                    : 'bg-gray-100 text-gray-400'
                }`}
              >
                <Icon className='h-5 w-5' />
              </div>
              <div className='space-y-0.5'>
                <div className='flex items-center space-x-2'>
                  <div className='text-sm font-medium'>{integration.name}</div>
                </div>
                <div className='text-muted-foreground text-sm'>
                  {integration.description}
                </div>
                {isConnected && (
                  <div className='text-muted-foreground text-xs'>
                    {integration.lastSync && ` ${integration.lastSync}`}
                  </div>
                )}
              </div>
            </div>
            <div className='flex items-center space-x-1'>
              {isConnected ? (
                <>
                  <Button variant='ghost' size='icon' className='h-8 w-8'>
                    <Trash2 className='h-4 w-4' />
                  </Button>
                  <Button variant='ghost' size='icon' className='h-8 w-8'>
                    <SettingsIcon className='h-4 w-4' />
                  </Button>
                </>
              ) : (
                <Button size='sm'>Connect</Button>
              )}
            </div>
          </div>
        );
      })}
    </div>
  );
}

function BillingSection() {
  return (
    <div className='space-y-4'>
      <Card>
        <CardHeader>
          <CardTitle>Billing</CardTitle>
          <CardDescription>
            Manage your billing information and subscription.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <p className='text-muted-foreground'>
            Billing management will be available here.
          </p>
        </CardContent>
      </Card>
    </div>
  );
}

function ProfileSection({ user }: { user: User | null }) {
  return (
    <div className='space-y-6'>
      <Card>
        <CardHeader>
          <CardTitle>Profile Information</CardTitle>
          <CardDescription>
            Update your personal information and preferences.
          </CardDescription>
        </CardHeader>
        <CardContent className='space-y-4'>
          <div className='flex items-center space-x-4'>
            <Avatar className='h-20 w-20'>
              <AvatarImage
                src={user?.user_metadata?.avatar_url || undefined}
                alt={user?.user_metadata?.full_name || user?.email || ''}
              />
              <AvatarFallback>
                {(
                  user?.user_metadata?.full_name ||
                  user?.email?.split('@')[0] ||
                  ''
                )
                  ?.split(' ')
                  .map((n: string) => n[0])
                  .join('')}
              </AvatarFallback>
            </Avatar>
            <div>
              <Button variant='outline'>Change Avatar</Button>
            </div>
          </div>

          <div className='grid grid-cols-2 gap-4'>
            <div>
              <label className='text-sm font-medium'>Full Name</label>
              <Input
                defaultValue={
                  user?.user_metadata?.full_name ||
                  user?.email?.split('@')[0] ||
                  ''
                }
              />
            </div>
            <div>
              <label className='text-sm font-medium'>Email</label>
              <Input defaultValue={user?.email || ''} />
            </div>
          </div>

          <div>
            <Button>Save Changes</Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

function FeatureFlagsSection() {
  return (
    <div className='space-y-4'>
      <Card>
        <CardHeader>
          <CardTitle>Feature Flags</CardTitle>
          <CardDescription>
            Manage feature flags and experimental features.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <p className='text-muted-foreground'>
            Feature flags management will be available here.
          </p>
        </CardContent>
      </Card>
    </div>
  );
}

function SecretsSection({ searchTerm }: { searchTerm: string }) {
  const secrets = [
    {
      id: '1',
      type: 'Raw Secret',
      name: 'E2B_API_KEY',
      addedAt: 'Apr 10, 2025, 22:45:52'
    },
    {
      id: '2',
      type: 'Raw Secret',
      name: 'OPENAI_API_KEY',
      addedAt: 'Apr 10, 2025, 22:45:52'
    },
    {
      id: '3',
      type: 'Raw Secret',
      name: 'ANTHROPIC_API_KEY',
      addedAt: 'Apr 10, 2025, 22:45:52'
    },
    {
      id: '4',
      type: 'Raw Secret',
      name: 'XAI_API_KEY',
      addedAt: 'Apr 10, 2025, 22:45:52'
    },
    {
      id: '5',
      type: 'Raw Secret',
      name: 'CONVEX_DEPLOYMENT',
      addedAt: 'Apr 10, 2025, 22:45:52'
    },
    {
      id: '6',
      type: 'Raw Secret',
      name: 'CONVEX_URL',
      addedAt: 'Apr 10, 2025, 22:45:52'
    },
    {
      id: '7',
      type: 'Raw Secret',
      name: 'MODEL',
      addedAt: 'Apr 10, 2025, 22:45:52'
    },
    {
      id: '8',
      type: 'Raw Secret',
      name: 'PERPLEXITY_MODEL',
      addedAt: 'Apr 10, 2025, 22:45:52'
    },
    {
      id: '9',
      type: 'Raw Secret',
      name: 'MAX_TOKENS',
      addedAt: 'Apr 10, 2025, 22:45:52'
    },
    {
      id: '10',
      type: 'Raw Secret',
      name: 'TEMPERATURE',
      addedAt: 'Apr 10, 2025, 22:45:52'
    }
  ];

  const filteredSecrets = secrets.filter(
    (secret) =>
      secret.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      secret.type.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <div className='space-y-4'>
      <div className='rounded-lg border'>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className='text-muted-foreground text-sm font-medium'>
                Secret Type
              </TableHead>
              <TableHead className='text-muted-foreground text-sm font-medium'>
                Name
              </TableHead>
              <TableHead className='text-muted-foreground text-sm font-medium'>
                Added At
              </TableHead>
              <TableHead className='w-12'></TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredSecrets.map((secret) => (
              <TableRow key={secret.id} className='hover:bg-accent/50'>
                <TableCell className='font-medium'>{secret.type}</TableCell>
                <TableCell className='font-mono text-sm'>
                  {secret.name}
                </TableCell>
                <TableCell className='text-muted-foreground text-sm'>
                  {secret.addedAt}
                </TableCell>
                <TableCell>
                  <Button
                    variant='ghost'
                    size='icon'
                    className='text-muted-foreground hover:text-destructive h-8 w-8'
                  >
                    <Trash2 className='h-4 w-4' />
                  </Button>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      {filteredSecrets.length === 0 && (
        <div className='py-8 text-center'>
          <p className='text-muted-foreground'>
            No secrets found matching &quot;{searchTerm}&quot;
          </p>
        </div>
      )}
    </div>
  );
}
