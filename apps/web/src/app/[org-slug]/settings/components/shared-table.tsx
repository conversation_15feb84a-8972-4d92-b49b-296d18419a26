'use client';

import { useState, useMemo } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';
import { cn } from '@/lib/utils';

export interface TableColumn<T = any> {
  key: string;
  label: string;
  render: (item: T) => React.ReactNode;
  visible?: boolean;
}

export interface TableAction<T = any> {
  label: string;
  icon?: React.ComponentType<{ className?: string }>;
  onClick: (item: T) => void;
  variant?: 'default' | 'destructive';
}

interface SettingsTableProps<T> {
  data: T[];
  columns: TableColumn<T>[];
  searchTerm: string;
  onSearchChange: (value: string) => void;
  searchPlaceholder?: string;
  onRowClick?: (item: T) => void;
  actions?: TableAction<T>[];
  emptyMessage?: string;
  filterFn?: (item: T, searchTerm: string) => boolean;
  children?: React.ReactNode;
}

export function SettingsTable<T extends { id: string }>({
  data,
  columns,
  searchTerm,
  onSearchChange,
  searchPlaceholder = 'Search...',
  onRowClick,
  actions = [],
  emptyMessage = 'No items found.',
  filterFn,
  children
}: SettingsTableProps<T>) {
  const [hoveredRow, setHoveredRow] = useState<string | null>(null);

  const visibleColumns = useMemo(
    () => columns.filter((col) => col.visible !== false),
    [columns]
  );

  const filteredData = useMemo(() => {
    if (!searchTerm) return data;

    if (filterFn) {
      return data.filter((item) => filterFn(item, searchTerm));
    }

    // Default search implementation
    return data.filter((item) =>
      Object.values(item).some((value) =>
        String(value).toLowerCase().includes(searchTerm.toLowerCase())
      )
    );
  }, [data, searchTerm, filterFn]);

  return (
    <div className='space-y-4'>
      {/* Search and Actions Bar */}
      <div className='flex items-center justify-between gap-3'>
        <div className='flex items-center gap-3'>
          <Input
            placeholder={searchPlaceholder}
            value={searchTerm}
            onChange={(e) => onSearchChange(e.target.value)}
            className='h-7 w-64 border-0 text-sm shadow-none'
          />
        </div>
        <div className='flex gap-2'>{children}</div>
      </div>

      {/* Table */}
      <div className='rounded-md border'>
        <Table>
          <TableHeader>
            <TableRow>
              {visibleColumns.map((column) => (
                <TableHead key={column.key} className='h-8 text-xs font-medium'>
                  {column.label}
                </TableHead>
              ))}
              {actions.length > 0 && (
                <TableHead className='h-8 w-12'></TableHead>
              )}
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredData.length === 0 ? (
              <TableRow>
                <TableCell
                  colSpan={visibleColumns.length + (actions.length > 0 ? 1 : 0)}
                  className='text-muted-foreground h-16 text-center text-sm'
                >
                  {emptyMessage}
                </TableCell>
              </TableRow>
            ) : (
              filteredData.map((item) => (
                <TableRow
                  key={item.id}
                  className={cn(
                    'transition-colors',
                    onRowClick && 'cursor-pointer',
                    hoveredRow === item.id && 'bg-muted/50'
                  )}
                  onMouseEnter={() => setHoveredRow(item.id)}
                  onMouseLeave={() => setHoveredRow(null)}
                  onClick={() => onRowClick?.(item)}
                >
                  {visibleColumns.map((column) => (
                    <TableCell key={column.key} className='py-2'>
                      {column.render(item)}
                    </TableCell>
                  ))}
                  {actions.length > 0 && (
                    <TableCell
                      className='py-2'
                      onClick={(e) => e.stopPropagation()}
                    >
                      <div className='flex gap-1'>
                        {actions.map((action, index) => {
                          const Icon = action.icon;
                          return (
                            <Button
                              key={index}
                              variant='ghost'
                              size='sm'
                              className={cn(
                                'h-6 w-6 p-0',
                                action.variant === 'destructive'
                                  ? 'text-muted-foreground hover:text-destructive'
                                  : 'text-muted-foreground hover:text-foreground'
                              )}
                              onClick={() => action.onClick(item)}
                            >
                              {Icon && <Icon className='h-3 w-3' />}
                            </Button>
                          );
                        })}
                      </div>
                    </TableCell>
                  )}
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}
