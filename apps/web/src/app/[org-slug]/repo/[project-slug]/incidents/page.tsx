'use client';

import React, { useState, useMemo, useEffect } from 'react';
import PageContainer from '@/components/layout/page-container';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';
import {
  FilterPopover,
  createFilterTrigger,
  FilterItem,
  FilterActions,
  CommandGroup
} from '@/components/ui/filter-popover';
import { IconColumns3, IconSettings } from '@tabler/icons-react';
import { SignalHigh, SignalLow, SignalMedium } from 'lucide-react';
import { cn } from '@/lib/utils';
import { format } from 'date-fns';
import { IncidentDetailsModal } from '@/components/modal/incident-details-modal';
import {
  useRepositoryIncidents,
  getIncidentDescription,
  type Incident
} from '@/hooks/supabase/use-incidents';

interface Props {
  params: Promise<{
    'org-slug': string;
    'project-slug': string;
  }>;
}

const columnLabels = {
  severity: 'Severity',
  status: 'Status',
  description: 'Description',
  service: 'Service',
  environment: 'Environment',
  timestamp: 'Timestamp'
};

type ColumnKey = keyof typeof columnLabels;

function Badge2({
  type,
  value
}: {
  type: 'severity' | 'status';
  value: string;
}) {
  if (type === 'severity') {
    switch (value) {
      case 'high':
        return (
          <Badge className='bg-red-100 px-2 py-0 text-sm text-red-800 dark:bg-red-900/30 dark:text-red-300'>
            <div className='flex items-center gap-1.5'>
              <SignalHigh className='h-3 w-3' />
              <span>High</span>
            </div>
          </Badge>
        );
      case 'medium':
        return (
          <Badge className='bg-orange-100 px-2 py-0 text-sm text-orange-800 dark:bg-orange-900/30 dark:text-orange-300'>
            <div className='flex items-center gap-1.5'>
              <SignalMedium className='h-3 w-3' />
              <span>Medium</span>
            </div>
          </Badge>
        );
      case 'low':
        return (
          <Badge className='bg-yellow-100 px-2 py-0 text-sm text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300'>
            <div className='flex items-center gap-1.5'>
              <SignalLow className='h-3 w-3' />
              <span>Low</span>
            </div>
          </Badge>
        );
    }
  } else {
    switch (value) {
      case 'running':
        return (
          <Badge className='bg-blue-100 px-2 py-0 text-sm text-blue-800 dark:bg-blue-900/30 dark:text-blue-300'>
            Running
          </Badge>
        );
      case 'completed':
        return (
          <Badge className='bg-green-100 px-2 py-0 text-sm text-green-800 dark:bg-green-900/30 dark:text-green-300'>
            Completed
          </Badge>
        );
    }
  }
  return <Badge className='px-2 py-0 text-sm'>{value}</Badge>;
}

function FilterDropdown({
  label,
  value,
  onChange,
  options,
  icon
}: {
  label: string;
  value: string[];
  onChange: (value: string[]) => void;
  options: string[];
  icon?: React.ComponentType<any>;
}) {
  const getOptionIcon = (option: string) => {
    if (label === 'Severity') {
      switch (option) {
        case 'high':
          return SignalHigh;
        case 'medium':
          return SignalMedium;
        case 'low':
          return SignalLow;
        default:
          return undefined;
      }
    }
    return undefined;
  };

  return (
    <FilterPopover
      trigger={createFilterTrigger({ label, icon })}
      searchPlaceholder={`Search ${label.toLowerCase()}...`}
    >
      <CommandGroup>
        {options.map((option) => (
          <FilterItem
            key={option}
            label={option}
            isSelected={value.includes(option)}
            onToggle={() =>
              onChange(
                value.includes(option)
                  ? value.filter((v) => v !== option)
                  : [...value, option]
              )
            }
            icon={getOptionIcon(option)}
          />
        ))}
      </CommandGroup>
      <FilterActions
        onSelectAll={() => onChange(options)}
        onClearAll={() => onChange([])}
        hasSelections={value.length > 0}
      />
    </FilterPopover>
  );
}

export default function IncidentsPage({ params: paramsPromise }: Props) {
  const params = React.use(paramsPromise);
  const {
    data: incidents = [],
    isLoading: loading,
    error
  } = useRepositoryIncidents(params['org-slug'], params['project-slug']);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string[]>([]);
  const [severityFilter, setSeverityFilter] = useState<string[]>([]);
  const [visibleColumns, setVisibleColumns] = useState<
    Record<ColumnKey, boolean>
  >({
    severity: true,
    status: true,
    description: true,
    service: true,
    environment: true,
    timestamp: true
  });
  const [hoveredIncident, setHoveredIncident] = useState<string | null>(null);
  const [selectedIncident, setSelectedIncident] = useState<Incident | null>(
    null
  );
  const [modalOpen, setModalOpen] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [isClient, setIsClient] = useState(false);

  useEffect(() => setIsClient(true), []);

  const filteredIncidents = useMemo(() => {
    return incidents.filter((incident) => {
      const incidentDescription = getIncidentDescription(incident);
      const matchesSearch = [incidentDescription, incident.service].some(
        (text) => text.toLowerCase().includes(searchTerm.toLowerCase())
      );
      const matchesStatus =
        statusFilter.length === 0 || statusFilter.includes(incident.status);
      const matchesSeverity =
        severityFilter.length === 0 ||
        severityFilter.includes(incident.severity);
      return matchesSearch && matchesStatus && matchesSeverity;
    });
  }, [searchTerm, statusFilter, severityFilter, incidents]);

  const handleIncidentClick = (incidentId: string) => {
    const incident = incidents.find((i) => i.id === incidentId);
    if (incident) {
      setSelectedIncident(incident);
      setModalOpen(true);
      setTimeout(() => setModalVisible(true), 10);
    }
  };

  const closeModal = () => {
    setModalVisible(false);
    setTimeout(() => {
      setModalOpen(false);
      setSelectedIncident(null);
    }, 300);
  };

  const getCellValue = (incident: Incident, key: ColumnKey) => {
    switch (key) {
      case 'timestamp':
        return format(new Date(incident.timestamp), 'MMM d, HH:mm');
      case 'severity':
        return <Badge2 type='severity' value={incident.severity} />;
      case 'status':
        return <Badge2 type='status' value={incident.status} />;
      case 'description':
        return (
          <div className='text-muted-foreground pr-4 text-sm break-words whitespace-normal'>
            {incident.description}
          </div>
        );
      case 'service':
        return incident.service;
      case 'environment':
        return incident.environment;
    }
  };

  if (!isClient) return <></>;

  if (loading) {
    return (
      <PageContainer scrollable>
        <div className='flex items-center justify-center'>
          <div className='text-muted-foreground text-sm'>
            Loading incidents...
          </div>
        </div>
      </PageContainer>
    );
  }

  if (error) {
    return (
      <PageContainer scrollable>
        <div className='flex items-center justify-center py-12'>
          <div className='text-red-500'>
            {error instanceof Error
              ? error.message
              : 'Failed to load incidents'}
          </div>
        </div>
      </PageContainer>
    );
  }

  return (
    <PageContainer scrollable>
      <div className='w-full space-y-6'>
        <div className='space-y-4'>
          <div className='flex items-center justify-between gap-3'>
            <div className='flex items-center gap-3'>
              <div className='flex items-center gap-2'>
                <FilterDropdown
                  label='Status'
                  value={statusFilter}
                  onChange={setStatusFilter}
                  options={['running', 'completed']}
                />
                <FilterDropdown
                  label='Severity'
                  value={severityFilter}
                  onChange={setSeverityFilter}
                  options={['high', 'medium', 'low']}
                />
                <FilterDropdown
                  label='Columns'
                  value={Object.entries(visibleColumns)
                    .filter(([_, visible]) => visible)
                    .map(([key]) => key)}
                  onChange={(selected) =>
                    setVisibleColumns(
                      Object.keys(columnLabels).reduce(
                        (acc, key) => ({
                          ...acc,
                          [key]: selected.includes(key)
                        }),
                        {} as Record<ColumnKey, boolean>
                      )
                    )
                  }
                  options={Object.keys(columnLabels)}
                  icon={IconColumns3}
                />
              </div>
              <Input
                placeholder='Search incidents...'
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className='h-7 w-64 border-0 text-xs shadow-none'
              />
            </div>
            <div className='flex items-center gap-2'>
              <Button variant='outline' size='sm' className='h-7 text-xs'>
                <IconSettings className='mr-1 h-3 w-3' />
                Configure
              </Button>
            </div>
          </div>

          <Table>
            <TableHeader>
              <TableRow>
                {Object.entries(visibleColumns).map(
                  ([key, visible]) =>
                    visible && (
                      <TableHead
                        key={key}
                        className={cn(
                          'text-sm font-medium',
                          key === 'description' && 'w-1/3 min-w-0'
                        )}
                      >
                        {columnLabels[key as ColumnKey]}
                      </TableHead>
                    )
                )}
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredIncidents.map((incident) => (
                <TableRow
                  key={incident.id}
                  className={cn(
                    'cursor-pointer transition-colors',
                    hoveredIncident === incident.id && 'bg-muted/50'
                  )}
                  onMouseEnter={() => setHoveredIncident(incident.id)}
                  onMouseLeave={() => setHoveredIncident(null)}
                  onClick={() => handleIncidentClick(incident.id)}
                >
                  {Object.entries(visibleColumns).map(
                    ([key, visible]) =>
                      visible && (
                        <TableCell
                          key={key}
                          className={cn(
                            'py-2',
                            key === 'description' && 'w-1/3 min-w-0',
                            key === 'timestamp'
                              ? 'text-muted-foreground text-sm'
                              : [
                                    'description',
                                    'service',
                                    'environment'
                                  ].includes(key)
                                ? 'text-muted-foreground text-sm'
                                : ''
                          )}
                        >
                          {getCellValue(incident, key as ColumnKey)}
                        </TableCell>
                      )
                  )}
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </div>

      {modalOpen && selectedIncident && (
        <IncidentDetailsModal
          incident={selectedIncident}
          isVisible={modalVisible}
          onClose={closeModal}
        />
      )}
    </PageContainer>
  );
}
