'use client';

import React, { useState, useMemo, useEffect } from 'react';
import PageContainer from '@/components/layout/page-container';
import { Button } from '@/components/ui/button';
import {
  Table,
  TableBody,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';
import { IconSettings, IconPlayerPlay } from '@tabler/icons-react';
import { ScanRow } from '@/components/scans/ScanRow';
import { TableFilters } from '@/components/scans/TableFilters';
import { ScanDetailsModal } from '@/components/modal/scan-details-modal';
import { type VisibleColumns } from '@/constants/scans';
import {
  useRepositoryScans,
  getScanDescription,
  type Scan
} from '@/hooks/supabase/use-scans';

interface Props {
  params: Promise<{
    'org-slug': string;
    'project-slug': string;
  }>;
}

export default function ScansPage({ params: paramsPromise }: Props) {
  const params = React.use(paramsPromise);
  const {
    data: scans = [],
    isLoading: loading,
    error
  } = useRepositoryScans(params['org-slug'], params['project-slug']);

  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string[]>([]);
  const [triggerFilter, setTriggerFilter] = useState<string[]>([]);
  const [visibleColumns, setVisibleColumns] = useState<VisibleColumns>({
    trigger: true,
    status: true,
    description: true,
    issues: true,
    duration: true,
    cost: true,
    creator: true,
    timestamp: true
  });
  const [hoveredScan, setHoveredScan] = useState<string | null>(null);
  const [isClient, setIsClient] = useState(false);
  const [modalOpen, setModalOpen] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [selectedScan, setSelectedScan] = useState<Scan | null>(null);

  useEffect(() => {
    setIsClient(true);
  }, []);

  const filteredScans = useMemo(() => {
    const statusOptions = ['completed', 'running', 'failed'];
    const triggerOptions = ['manual', 'scheduled', 'push', 'pr'];

    return scans.filter((scan) => {
      const scanDescription = getScanDescription(scan);
      const matchesSearch = [scanDescription, scan.name].some((text) =>
        text.toLowerCase().includes(searchTerm.toLowerCase())
      );

      const matchesStatus =
        statusFilter.length === 0 ||
        statusFilter.length === statusOptions.length ||
        statusFilter.includes(scan.status);
      const matchesTrigger =
        triggerFilter.length === 0 ||
        triggerFilter.length === triggerOptions.length ||
        triggerFilter.includes(scan.triggerReason);

      return matchesSearch && matchesStatus && matchesTrigger;
    });
  }, [searchTerm, statusFilter, triggerFilter, scans]);

  const handleScanClick = (scanId: string) => {
    const scan = scans.find((s) => s.id === scanId);
    if (scan) {
      setSelectedScan(scan);
      setModalOpen(true);
      setTimeout(() => setModalVisible(true), 10);
    }
  };

  const closeModal = () => {
    setModalVisible(false);
    setTimeout(() => {
      setModalOpen(false);
      setSelectedScan(null);
    }, 300);
  };

  if (!isClient) {
    return <></>;
  }

  if (loading) {
    return (
      <PageContainer scrollable>
        <div className='flex items-center justify-center'>
          <div className='text-muted-foreground text-sm'>Loading scans...</div>
        </div>
      </PageContainer>
    );
  }

  if (error) {
    return (
      <PageContainer scrollable>
        <div className='flex items-center justify-center py-12'>
          <div className='text-red-500'>
            {error instanceof Error ? error.message : 'Failed to load scans'}
          </div>
        </div>
      </PageContainer>
    );
  }

  return (
    <PageContainer scrollable>
      <div className='w-full space-y-6'>
        {/* Scans Table */}
        <div className='space-y-4'>
          <TableFilters
            searchTerm={searchTerm}
            onSearchChange={setSearchTerm}
            statusFilter={statusFilter}
            onStatusFilterChange={setStatusFilter}
            triggerFilter={triggerFilter}
            onTriggerFilterChange={setTriggerFilter}
            visibleColumns={visibleColumns}
            onVisibleColumnsChange={setVisibleColumns}
          >
            <Button variant='outline' size='sm' className='h-7 text-xs'>
              <IconSettings className='mr-1 h-3 w-3' />
              Configure
            </Button>
            <Button size='sm' className='h-7 text-xs'>
              <IconPlayerPlay className='mr-1 h-3 w-3' />
              Run New Scan
            </Button>
          </TableFilters>

          <Table>
            <TableHeader>
              <TableRow>
                {Object.entries(visibleColumns).map(
                  ([key, visible]) =>
                    visible && (
                      <TableHead key={key} className='text-sm font-medium'>
                        {key === 'timestamp' && 'Timestamp'}
                        {key === 'trigger' && 'Trigger'}
                        {key === 'status' && 'Status'}
                        {key === 'description' && 'Description'}
                        {key === 'issues' && 'Issues Found'}
                        {key === 'duration' && 'Duration'}
                        {key === 'cost' && 'Token Cost'}
                        {key === 'creator' && 'Triggered By'}
                      </TableHead>
                    )
                )}
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredScans.map((scan) => (
                <ScanRow
                  key={scan.id}
                  scan={scan}
                  visibleColumns={visibleColumns}
                  isHovered={hoveredScan === scan.id}
                  onMouseEnter={() => setHoveredScan(scan.id)}
                  onMouseLeave={() => setHoveredScan(null)}
                  onClick={() => handleScanClick(scan.id)}
                  getScanDescription={getScanDescription}
                />
              ))}
            </TableBody>
          </Table>

          {filteredScans.length === 0 && (
            <div className='py-12 text-center'>
              <div className='text-muted-foreground'>
                {searchTerm ||
                statusFilter.length > 0 ||
                triggerFilter.length > 0
                  ? 'No scans match your filters'
                  : 'No scans found. Run your first scan to get started.'}
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Modal */}
      {modalOpen && selectedScan && (
        <ScanDetailsModal
          scan={selectedScan}
          isVisible={modalVisible}
          onClose={closeModal}
        />
      )}
    </PageContainer>
  );
}
