'use client';

import React, { useState, useMemo, useEffect, ReactElement } from 'react';
import PageContainer from '@/components/layout/page-container';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';
import {
  FilterPopover,
  createFilterTrigger,
  FilterItem,
  FilterActions,
  CommandGroup
} from '@/components/ui/filter-popover';
import {
  IconBug,
  IconCheck,
  IconPlayerPlay,
  IconColumns3,
  IconScan,
  IconActivityHeartbeat,
  IconBrandGithub,
  IconExternalLink,
  IconClock,
  IconGitMerge,
  IconList,
  IconLayoutKanban
} from '@tabler/icons-react';
import { cn } from '@/lib/utils';
import { format, formatDistanceToNow } from 'date-fns';
import { IssueDetailsModal } from '@/components/modal/issue-details-modal';
import { SignalHigh, SignalLow, SignalMedium } from 'lucide-react';
import { createClient } from '@/lib/supabase/client';
import type { Tables } from '@/lib/supabase/schema';
import {
  useRepositoryIssues,
  type IssueWithMetadata
} from '@/hooks/supabase/use-issues';

interface Props {
  params: Promise<{
    'org-slug': string;
    'project-slug': string;
  }>;
}

type Issue = IssueWithMetadata & {
  updated_at: string;
  status: 'queued' | 'running' | 'completed' | 'merged';
};

type VisibleColumns = {
  timestamp: boolean;
  status: boolean;
  priority: boolean;
  trigger: boolean;
  cost: boolean;
  githubPr: boolean;
  created: boolean;
};

type ViewMode = 'list' | 'kanban';

const STATUS_OPTIONS = ['queued', 'running', 'completed', 'merged'];
const PRIORITY_OPTIONS = ['low', 'medium', 'high'];
const TRIGGER_OPTIONS = ['scan', 'incident'];

function StatusFilterPopover({
  label,
  value,
  onChange,
  options
}: {
  label: string;
  value: string[];
  onChange: (value: string[]) => void;
  options: string[];
}) {
  const toggleFilter = (option: string) => {
    onChange(
      value.includes(option)
        ? value.filter((v) => v !== option)
        : [...value, option]
    );
  };

  const selectAll = () => {
    onChange(options);
  };

  const clearAll = () => {
    onChange([]);
  };

  const getOptionIcon = (option: string) => {
    if (label === 'Status') {
      switch (option) {
        case 'queued':
          return IconClock;
        case 'running':
          return IconPlayerPlay;
        case 'completed':
          return IconCheck;
        case 'merged':
          return IconGitMerge;
        default:
          return undefined;
      }
    }
    if (label === 'Priority') {
      switch (option) {
        case 'high':
          return SignalHigh;
        case 'medium':
          return SignalMedium;
        case 'low':
          return SignalLow;
        default:
          return undefined;
      }
    }
    if (label === 'Trigger') {
      switch (option) {
        case 'scan':
          return IconScan;
        case 'incident':
          return IconActivityHeartbeat;
        default:
          return undefined;
      }
    }
    return undefined;
  };

  const trigger = createFilterTrigger({
    label
  });

  return (
    <FilterPopover
      trigger={trigger}
      searchPlaceholder={`Search ${label.toLowerCase()}...`}
    >
      <CommandGroup>
        {options.map((option) => (
          <FilterItem
            key={option}
            label={option.replace('_', ' ')}
            isSelected={value.includes(option)}
            onToggle={() => toggleFilter(option)}
            icon={getOptionIcon(option)}
          />
        ))}
      </CommandGroup>
      <FilterActions
        onSelectAll={selectAll}
        onClearAll={clearAll}
        hasSelections={value.length > 0}
      />
    </FilterPopover>
  );
}

function ColumnsFilterPopover({
  visibleColumns,
  onVisibleColumnsChange
}: {
  visibleColumns: VisibleColumns;
  onVisibleColumnsChange: (columns: VisibleColumns) => void;
}) {
  const columnLabels = {
    timestamp: 'Timestamp',
    status: 'Status',
    priority: 'Priority',
    trigger: 'Trigger',
    cost: 'Token Cost',
    githubPr: 'GitHub PR',
    created: 'Created'
  };

  const toggleColumn = (key: keyof VisibleColumns) => {
    onVisibleColumnsChange({
      ...visibleColumns,
      [key]: !visibleColumns[key]
    });
  };

  const selectAll = () => {
    const allVisible = Object.keys(visibleColumns).reduce(
      (acc, key) => ({ ...acc, [key]: true }),
      {} as VisibleColumns
    );
    onVisibleColumnsChange(allVisible);
  };

  const clearAll = () => {
    const allHidden = Object.keys(visibleColumns).reduce(
      (acc, key) => ({ ...acc, [key]: false }),
      {} as VisibleColumns
    );
    onVisibleColumnsChange(allHidden);
  };

  const trigger = createFilterTrigger({
    label: 'Columns',
    icon: IconColumns3
  });

  return (
    <FilterPopover trigger={trigger} searchPlaceholder='Search columns...'>
      <CommandGroup>
        {Object.entries(visibleColumns).map(([key, visible]) => (
          <FilterItem
            key={key}
            label={columnLabels[key as keyof typeof columnLabels]}
            isSelected={visible}
            onToggle={() => toggleColumn(key as keyof VisibleColumns)}
          />
        ))}
      </CommandGroup>
      <FilterActions
        onSelectAll={selectAll}
        onClearAll={clearAll}
        hasSelections={Object.values(visibleColumns).some(Boolean)}
      />
    </FilterPopover>
  );
}

function ViewSelector({
  currentView,
  onViewChange
}: {
  currentView: ViewMode;
  onViewChange: (view: ViewMode) => void;
}) {
  return (
    <div className='bg-background flex items-center rounded-md border p-1'>
      <Button
        variant={currentView === 'list' ? 'default' : 'ghost'}
        size='sm'
        onClick={() => onViewChange('list')}
        className='h-6 px-3 text-xs'
      >
        <IconList className='mr-1.5 h-4 w-4' />
        List
      </Button>
      <Button
        variant={currentView === 'kanban' ? 'default' : 'ghost'}
        size='sm'
        onClick={() => onViewChange('kanban')}
        className='h-6 px-3 text-xs'
      >
        <IconLayoutKanban className='mr-1.5 h-4 w-4' />
        Kanban
      </Button>
    </div>
  );
}

function TableFilters({
  searchTerm,
  onSearchChange,
  statusFilter,
  onStatusFilterChange,
  priorityFilter,
  onPriorityFilterChange,
  triggerFilter,
  onTriggerFilterChange,
  visibleColumns,
  onVisibleColumnsChange,
  currentView,
  onViewChange,
  children
}: {
  searchTerm: string;
  onSearchChange: (value: string) => void;
  statusFilter: string[];
  onStatusFilterChange: (value: string[]) => void;
  priorityFilter: string[];
  onPriorityFilterChange: (value: string[]) => void;
  triggerFilter: string[];
  onTriggerFilterChange: (value: string[]) => void;
  visibleColumns: VisibleColumns;
  onVisibleColumnsChange: (columns: VisibleColumns) => void;
  currentView: ViewMode;
  onViewChange: (view: ViewMode) => void;
  children?: React.ReactNode;
}) {
  return (
    <div className='flex flex-col gap-4'>
      {/* Main toolbar */}
      <div className='flex items-center justify-between'>
        <div className='flex items-center gap-3'>
          <StatusFilterPopover
            label='Status'
            value={statusFilter}
            onChange={onStatusFilterChange}
            options={STATUS_OPTIONS}
          />

          <StatusFilterPopover
            label='Priority'
            value={priorityFilter}
            onChange={onPriorityFilterChange}
            options={PRIORITY_OPTIONS}
          />

          <StatusFilterPopover
            label='Trigger'
            value={triggerFilter}
            onChange={onTriggerFilterChange}
            options={TRIGGER_OPTIONS}
          />
          {currentView === 'list' && (
            <ColumnsFilterPopover
              visibleColumns={visibleColumns}
              onVisibleColumnsChange={onVisibleColumnsChange}
            />
          )}
          <Input
            placeholder='Search issues...'
            value={searchTerm}
            onChange={(e) => onSearchChange(e.target.value)}
            className='w-full/3 border-none shadow-none'
          />
        </div>

        <ViewSelector currentView={currentView} onViewChange={onViewChange} />
      </div>

      {children}
    </div>
  );
}

function getStatusBadge(status: Issue['status']) {
  const badges: Record<Issue['status'], ReactElement> = {
    queued: (
      <Badge className='bg-gray-100 text-sm text-gray-800 dark:bg-gray-900/30 dark:text-gray-300'>
        Queued
      </Badge>
    ),
    running: (
      <Badge className='bg-blue-100 text-sm text-blue-800 dark:bg-blue-900/30 dark:text-blue-300'>
        Running
      </Badge>
    ),
    completed: (
      <Badge className='bg-green-100 text-sm text-green-800 dark:bg-green-900/30 dark:text-green-300'>
        Completed
      </Badge>
    ),
    merged: (
      <Badge className='bg-purple-100 text-sm text-purple-800 dark:bg-purple-900/30 dark:text-purple-300'>
        Merged
      </Badge>
    )
  };
  return badges[status];
}

function getPriorityBadge(priority: Issue['priority']) {
  const badges = {
    low: (
      <Badge className='bg-yellow-100 px-2 py-0 text-sm text-yellow-800 dark:bg-green-900/30 dark:text-green-300'>
        <div className='flex items-center gap-1.5'>
          <SignalLow className='h-3 w-3' />
          <span className='text-sm'>Low</span>
        </div>
      </Badge>
    ),
    medium: (
      <Badge className='bg-orange-100 px-2 py-0 text-sm text-orange-800 dark:bg-yellow-900/30 dark:text-yellow-300'>
        <div className='flex items-center gap-1.5'>
          <SignalMedium className='h-3 w-3' />
          <span className='text-sm'>Medium</span>
        </div>
      </Badge>
    ),
    high: (
      <Badge className='bg-red-100 px-2 py-0 text-sm text-red-800 dark:bg-orange-900/30 dark:text-orange-300'>
        <div className='flex items-center gap-1.5'>
          <SignalHigh className='h-3 w-3' />
          <span className='text-sm'>High</span>
        </div>
      </Badge>
    )
  };
  return badges[priority];
}

function getTriggerBadge(trigger: Issue['trigger'], triggerSource: string) {
  const triggerBadges = {
    scan: (
      <Badge className='max-w-full bg-indigo-100 text-indigo-800 dark:bg-indigo-900/30 dark:text-indigo-300'>
        <div className='flex min-w-0 items-center'>
          <IconScan className='mr-1.5 h-3 w-3 flex-shrink-0' />
          <span className='truncate'>{triggerSource}</span>
        </div>
      </Badge>
    ),
    incident: (
      <Badge className='max-w-full bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300'>
        <div className='flex min-w-0 items-center'>
          <IconActivityHeartbeat className='mr-1.5 h-3 w-3 flex-shrink-0' />
          <span className='truncate'>{triggerSource}</span>
        </div>
      </Badge>
    )
  };
  return triggerBadges[trigger];
}

function KanbanCard({ issue, onClick }: { issue: Issue; onClick: () => void }) {
  return (
    <div
      className='hover:bg-muted/50 mb-3 cursor-pointer rounded-lg border p-3 transition-shadow'
      onClick={onClick}
    >
      <div className='mb-2 space-y-2'>
        <h4 className='line-clamp-2 text-sm font-medium text-gray-900 dark:text-gray-100'>
          {issue.name}
        </h4>
        <div className='flex min-w-0 items-center justify-start'>
          <span className='mr-2 flex-shrink-0 text-sm text-gray-500 dark:text-gray-400'>
            Priority:
          </span>
          <div className='min-w-0'>{getPriorityBadge(issue.priority)}</div>
        </div>
      </div>

      <div className='space-y-2 text-gray-500 dark:text-gray-400'>
        <div className='flex min-w-0 items-center justify-start'>
          <span className='mr-2 flex-shrink-0 text-sm text-gray-500 dark:text-gray-400'>
            Trigger:
          </span>
          <div className='min-w-0 truncate'>
            {getTriggerBadge(issue.trigger, issue.triggerSource)}
          </div>
        </div>
        <div className='text-sm'>
          <span className='mr-2 text-gray-500 dark:text-gray-400'>
            Created:
          </span>
          <span className='truncate'>
            {formatDistanceToNow(new Date(issue.created_at), {
              addSuffix: true
            })}
          </span>
        </div>
      </div>

      {issue.githubPrLink && (
        <div className='mt-2 border-t border-gray-100 pt-2 dark:border-gray-700'>
          <a
            href={issue.githubPrLink}
            target='_blank'
            rel='noopener noreferrer'
            className='inline-flex min-w-0 items-center gap-1 text-sm text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300'
            onClick={(e) => e.stopPropagation()}
          >
            <IconBrandGithub className='h-3 w-3 flex-shrink-0' />
            <span className='truncate'>
              #{issue.githubPrLink.split('/').pop()}
            </span>
            <IconExternalLink className='h-2.5 w-2.5 flex-shrink-0' />
          </a>
        </div>
      )}
    </div>
  );
}

function KanbanColumn({
  title,
  status,
  issues,
  onIssueClick
}: {
  title: string;
  status: Issue['status'];
  issues: Issue[];
  onIssueClick: (issueId: string) => void;
}) {
  const getColumnIcon = () => {
    switch (status) {
      case 'queued':
        return <IconClock className='h-4 w-4' />;
      case 'running':
        return <IconPlayerPlay className='h-4 w-4' />;
      case 'completed':
        return <IconCheck className='h-4 w-4' />;
      case 'merged':
        return <IconGitMerge className='h-4 w-4' />;
    }
  };

  const getColumnColor = () => {
    switch (status) {
      case 'queued':
        return 'text-gray-600 dark:text-gray-400';
      case 'running':
        return 'text-blue-600 dark:text-blue-400';
      case 'completed':
        return 'text-green-600 dark:text-green-400';
      case 'merged':
        return 'text-purple-600 dark:text-purple-400';
    }
  };

  return (
    <div className='min-w-[320px] flex-shrink-0'>
      <div className='rounded-lg'>
        <div className={cn('mb-3 flex items-center gap-1.5', getColumnColor())}>
          {getColumnIcon()}
          <h3 className='text-sm font-medium'>{title}</h3>
          <span className='text-muted-foreground px-1 text-sm font-medium'>
            {issues.length}
          </span>
        </div>

        <div className='space-y-3'>
          {issues.map((issue) => (
            <KanbanCard
              key={issue.id}
              issue={issue}
              onClick={() => onIssueClick(issue.id.toString())}
            />
          ))}

          {issues.length === 0 && (
            <div className='py-8 text-center text-gray-400 dark:text-gray-600'>
              <div className='mb-2 text-2xl'>·</div>
              <p className='text-sm'>No issues</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

function KanbanView({
  issues,
  onIssueClick
}: {
  issues: Issue[];
  onIssueClick: (issueId: string) => void;
}) {
  const columns = [
    { status: 'queued' as const, title: 'Queued' },
    { status: 'running' as const, title: 'Running' },
    { status: 'completed' as const, title: 'Completed' },
    { status: 'merged' as const, title: 'Merged' }
  ];

  return (
    <div className='flex flex-nowrap gap-6 overflow-x-auto py-3'>
      {columns.map((column) => (
        <KanbanColumn
          key={column.status}
          title={column.title}
          status={column.status}
          issues={issues.filter((issue) => issue.status === column.status)}
          onIssueClick={(id) => onIssueClick(id.toString())}
        />
      ))}
    </div>
  );
}

function IssueRow({
  issue,
  visibleColumns,
  isHovered,
  onMouseEnter,
  onMouseLeave,
  onClick
}: {
  issue: Issue;
  visibleColumns: VisibleColumns;
  isHovered: boolean;
  onMouseEnter: () => void;
  onMouseLeave: () => void;
  onClick: () => void;
}) {
  return (
    <TableRow
      className={cn(
        'cursor-pointer transition-colors',
        isHovered && 'bg-muted/50'
      )}
      onMouseEnter={onMouseEnter}
      onMouseLeave={onMouseLeave}
      onClick={onClick}
    >
      <TableCell className='py-3'>
        <div className='space-y-1'>
          <div className='text-sm leading-none font-medium'>{issue.name}</div>
        </div>
      </TableCell>

      {visibleColumns.status && (
        <TableCell className='py-2'>{getStatusBadge(issue.status)}</TableCell>
      )}

      {visibleColumns.priority && (
        <TableCell className='py-2'>
          <div className='flex justify-start'>
            {getPriorityBadge(issue.priority)}
          </div>
        </TableCell>
      )}

      {visibleColumns.trigger && (
        <TableCell className='py-2'>
          {getTriggerBadge(issue.trigger, issue.triggerSource)}
        </TableCell>
      )}

      {visibleColumns.cost && (
        <TableCell className='py-2'>
          <span className='text-muted-foreground'>
            ${issue.tokenCost.toFixed(3)}
          </span>
        </TableCell>
      )}

      {visibleColumns.githubPr && (
        <TableCell className='py-2'>
          {issue.githubPrLink ? (
            <a
              href={issue.githubPrLink}
              target='_blank'
              rel='noopener noreferrer'
              className='flex items-center gap-1.5 text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300'
              onClick={(e) => e.stopPropagation()}
            >
              <IconBrandGithub className='h-3 w-3' />
              <span className='text-sm'>
                #{issue.githubPrLink.split('/').pop()}
              </span>
              <IconExternalLink className='h-2.5 w-2.5' />
            </a>
          ) : (
            <span className='text-muted-foreground flex items-center gap-1 text-sm'>
              -
            </span>
          )}
        </TableCell>
      )}

      {visibleColumns.created && (
        <TableCell className='py-2'>
          <span className='text-muted-foreground text-xs'>
            {formatDistanceToNow(new Date(issue.created_at), {
              addSuffix: true
            })}
          </span>
        </TableCell>
      )}

      {visibleColumns.timestamp && (
        <TableCell className='text-muted-foreground py-2 text-sm'>
          {format(new Date(issue.created_at), 'MMM d, h:mm a')}
        </TableCell>
      )}
    </TableRow>
  );
}

export default function IssuesPage({ params }: Props) {
  const resolvedParams = React.use(params);
  const orgSlug = resolvedParams['org-slug'];
  const projectSlug = resolvedParams['project-slug'];

  // Use the hook to fetch issues for this repository
  const {
    data: issuesData = [],
    isLoading: loading,
    error: hookError
  } = useRepositoryIssues(orgSlug, projectSlug);

  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string[]>([]);
  const [priorityFilter, setPriorityFilter] = useState<string[]>([]);
  const [triggerFilter, setTriggerFilter] = useState<string[]>([]);
  const [hoveredRow, setHoveredRow] = useState<string | null>(null);
  const [currentView, setCurrentView] = useState<ViewMode>('list');
  const [visibleColumns, setVisibleColumns] = useState<VisibleColumns>({
    timestamp: true,
    status: true,
    priority: true,
    trigger: true,
    cost: false,
    githubPr: true,
    created: false
  });

  // Modal state
  const [modalOpen, setModalOpen] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [selectedIssue, setSelectedIssue] = useState<Issue | null>(null);

  // Transform IssueWithMetadata to Issue type for backward compatibility
  const issues: Issue[] = issuesData.map((issue) => ({
    ...issue,
    updated_at: issue.created_at, // Use created_at as updated_at if not available
    status:
      (issue.status as 'queued' | 'running' | 'completed' | 'merged') ||
      'queued'
  }));

  const error = hookError ? 'Failed to load issues' : null;

  const filteredIssues = useMemo(() => {
    return issues.filter((issue) => {
      const matchesSearch =
        issue.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        issue.description?.toLowerCase().includes(searchTerm.toLowerCase());

      // Show all if all options are selected (default behavior) or if the specific option is selected
      const matchesStatus =
        statusFilter.length === 0 ||
        statusFilter.length === STATUS_OPTIONS.length ||
        statusFilter.includes(issue.status);
      const matchesPriority =
        priorityFilter.length === 0 ||
        priorityFilter.length === PRIORITY_OPTIONS.length ||
        priorityFilter.includes(issue.priority);
      const matchesTrigger =
        triggerFilter.length === 0 ||
        triggerFilter.length === TRIGGER_OPTIONS.length ||
        triggerFilter.includes(issue.trigger);

      return (
        matchesSearch && matchesStatus && matchesPriority && matchesTrigger
      );
    });
  }, [issues, searchTerm, statusFilter, priorityFilter, triggerFilter]);

  const handleIssueClick = (issueId: string) => {
    const issue = issues.find((i) => i.id.toString() === issueId);
    if (issue) {
      setSelectedIssue(issue);
      setModalOpen(true);
      setTimeout(() => setModalVisible(true), 10);
    }
  };

  const closeModal = () => {
    setModalVisible(false);
    setTimeout(() => {
      setModalOpen(false);
      setSelectedIssue(null);
    }, 300);
  };

  if (loading) {
    return (
      <PageContainer scrollable>
        <div className='flex items-center justify-center'>
          <div className='text-muted-foreground text-sm'>Loading issues...</div>
        </div>
      </PageContainer>
    );
  }

  if (error) {
    return (
      <PageContainer scrollable>
        <div className='flex items-center justify-center py-12'>
          <div className='text-red-500'>{error}</div>
        </div>
      </PageContainer>
    );
  }

  return (
    <PageContainer scrollable>
      <div className='w-full space-y-6'>
        {/* Issues View */}
        <div className='space-y-4'>
          <TableFilters
            searchTerm={searchTerm}
            onSearchChange={setSearchTerm}
            statusFilter={statusFilter}
            onStatusFilterChange={setStatusFilter}
            priorityFilter={priorityFilter}
            onPriorityFilterChange={setPriorityFilter}
            triggerFilter={triggerFilter}
            onTriggerFilterChange={setTriggerFilter}
            visibleColumns={visibleColumns}
            onVisibleColumnsChange={setVisibleColumns}
            currentView={currentView}
            onViewChange={setCurrentView}
          />

          {currentView === 'list' ? (
            <>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className='text-sm font-medium'>Issue</TableHead>
                    {visibleColumns.status && (
                      <TableHead className='text-sm font-medium'>
                        Status
                      </TableHead>
                    )}
                    {visibleColumns.priority && (
                      <TableHead className='text-sm font-medium'>
                        Priority
                      </TableHead>
                    )}
                    {visibleColumns.trigger && (
                      <TableHead className='text-sm font-medium'>
                        Trigger
                      </TableHead>
                    )}
                    {visibleColumns.cost && (
                      <TableHead className='text-sm font-medium'>
                        Token Cost
                      </TableHead>
                    )}
                    {visibleColumns.githubPr && (
                      <TableHead className='text-sm font-medium'>
                        GitHub PR
                      </TableHead>
                    )}
                    {visibleColumns.created && (
                      <TableHead className='text-sm font-medium'>
                        Created
                      </TableHead>
                    )}
                    {visibleColumns.timestamp && (
                      <TableHead className='text-sm font-medium'>
                        Timestamp
                      </TableHead>
                    )}
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredIssues.map((issue) => (
                    <IssueRow
                      key={issue.id}
                      issue={issue}
                      visibleColumns={visibleColumns}
                      isHovered={hoveredRow === issue.id.toString()}
                      onMouseEnter={() => setHoveredRow(issue.id.toString())}
                      onMouseLeave={() => setHoveredRow(null)}
                      onClick={() => handleIssueClick(issue.id.toString())}
                    />
                  ))}
                </TableBody>
              </Table>

              {filteredIssues.length === 0 && (
                <div className='py-12 text-center'>
                  <IconBug className='text-muted-foreground/50 mx-auto h-12 w-12' />
                  <h3 className='text-md mt-4 font-medium'>No issues found</h3>
                  <p className='text-muted-foreground mt-1'>
                    {searchTerm ||
                    statusFilter.length > 0 ||
                    priorityFilter.length > 0 ||
                    triggerFilter.length > 0
                      ? 'Try adjusting your filters'
                      : 'Issues will appear here when generated from scans or incidents'}
                  </p>
                </div>
              )}
            </>
          ) : (
            <>
              <KanbanView
                issues={filteredIssues}
                onIssueClick={handleIssueClick}
              />

              {filteredIssues.length === 0 && (
                <div className='py-12 text-center'>
                  <IconBug className='text-muted-foreground/50 mx-auto h-12 w-12' />
                  <h3 className='text-md mt-4 font-medium'>No issues found</h3>
                  <p className='text-muted-foreground mt-1'>
                    {searchTerm ||
                    statusFilter.length > 0 ||
                    priorityFilter.length > 0 ||
                    triggerFilter.length > 0
                      ? 'Try adjusting your filters'
                      : 'Issues will appear here when generated from scans or incidents'}
                  </p>
                </div>
              )}
            </>
          )}
        </div>
      </div>

      {/* Issue Details Modal - Need to update this component to handle new Issue type */}
      {modalOpen && selectedIssue && (
        <IssueDetailsModal
          issue={{
            id: selectedIssue.id.toString(),
            name: selectedIssue.name || '',
            description: selectedIssue.description || '',
            status: selectedIssue.status as 'running' | 'completed',
            priority: selectedIssue.priority,
            trigger: selectedIssue.trigger,
            triggerSource: selectedIssue.triggerSource,
            githubPrLink: selectedIssue.githubPrLink,
            createdAt: selectedIssue.created_at,
            updatedAt: selectedIssue.created_at,
            tokenCost: selectedIssue.tokenCost
          }}
          isVisible={modalVisible}
          onClose={closeModal}
        />
      )}
    </PageContainer>
  );
}
