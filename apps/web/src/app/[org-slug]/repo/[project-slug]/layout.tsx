import type { Metadata } from 'next';

interface Props {
  children: React.ReactNode;
  params: Promise<{
    'org-slug': string;
    'project-slug': string;
  }>;
}

// Mock function to get project name from slug
// In a real app, this would fetch from your API or database
function getProjectName(projectSlug: string): string {
  const projects: Record<string, string> = {
    'website-redesign': 'Website Redesign',
    'mobile-app': 'Mobile App',
    'api-development': 'API Development',
    dashboard: 'Dashboard',
    analytics: 'Analytics',
    reporting: 'Reporting',
    ecommerce: 'E-commerce',
    inventory: 'Inventory',
    shipping: 'Shipping'
  };

  return (
    projects[projectSlug] ||
    projectSlug.replace(/-/g, ' ').replace(/\b\w/g, (l) => l.toUpperCase())
  );
}

export async function generateMetadata({ params }: Props): Promise<Metadata> {
  const resolvedParams = await params;
  const projectSlug = resolvedParams['project-slug'];
  const projectName = getProjectName(projectSlug);

  return {
    title: `${projectName} - Backspace`,
    description: 'Backspace automates code quality and maintenance.'
  };
}

export default function ProjectLayout({ children }: Props) {
  return <>{children}</>;
}
