'use client';

import React, { useState, useMemo, useEffect } from 'react';
import { useRouter, useSearchParams, useParams } from 'next/navigation';
import PageContainer from '@/components/layout/page-container';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  ChartConfig,
  ChartContainer,
  ChartTooltip
} from '@/components/ui/chart';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';
import {
  FilterPopover,
  createFilterTrigger,
  FilterItem,
  FilterActions,
  CommandGroup,
  CommandItem
} from '@/components/ui/filter-popover';
import {
  IconGitPullRequest,
  IconAlertTriangle,
  IconScan,
  IconColumns3,
  IconFilter,
  IconCheck
} from '@tabler/icons-react';
import { cn } from '@/lib/utils';
import { format } from 'date-fns';
import { Cartesian<PERSON>rid, Line, LineChart, XAxis, <PERSON>Axis } from 'recharts';

// Import Supabase hooks
import { useIncidents, type Incident } from '@/hooks/supabase/use-incidents';
import {
  useRepositoryIssues,
  type IssueWithMetadata
} from '@/hooks/supabase/use-issues';
import { useRepositoryScans, type Scan } from '@/hooks/supabase/use-scans';
import { createClient } from '@/lib/supabase/client';
import { useQuery } from '@tanstack/react-query';
import { Tables } from '@/lib/supabase/schema';

// Types and Constants
interface Event {
  id: string;
  type: 'incident' | 'scan' | 'pull_request';
  name: string;
  description: string;
  timestamp: string;
  timestampMs: number;
  branch: string | null;
  creator?: { name: string; avatar?: string; initials: string };
  metricsImpact: Array<{
    metric: string;
    oldValue: number;
    newValue: number;
    change: number;
  }>;
  errors?: number;
  duration: string;
  agentStatus?: 'dispatched' | 'in_progress' | 'completed' | 'failed';
}

const TIME_RANGES = [
  { label: '1h', value: '1h', hours: 1 },
  { label: '1d', value: '1d', hours: 24 },
  { label: '7d', value: '7d', hours: 168 },
  { label: '30d', value: '30d', hours: 720 }
] as const;

const METRICS = [
  'testCoverage',
  'clarity',
  'modularity',
  'security',
  'performance',
  'faultResilience',
  'agentReadiness'
] as const;

const METRICS_CONFIG = {
  testCoverage: { label: 'Test Coverage', color: 'hsl(142 76% 36%)' },
  clarity: { label: 'Clarity', color: 'hsl(221 83% 53%)' },
  modularity: { label: 'Modularity', color: 'hsl(271 81% 56%)' },
  security: { label: 'Security', color: 'hsl(12 76% 61%)' },
  performance: { label: 'Performance', color: 'hsl(173 58% 39%)' },
  faultResilience: { label: 'Fault Resilience', color: 'hsl(43 74% 49%)' },
  agentReadiness: { label: 'Agent Readiness', color: 'hsl(262 83% 58%)' }
};

const EVENT_TYPES = {
  incident: { label: 'Incident', icon: IconAlertTriangle, color: 'red' },
  scan: { label: 'Scan', icon: IconScan, color: 'purple' },
  pull_request: {
    label: 'Pull Request',
    icon: IconGitPullRequest,
    color: 'green'
  }
};

const chartConfig: ChartConfig = {
  testCoverage: { label: 'Test Coverage', color: 'hsl(142 76% 36%)' },
  clarity: { label: 'Clarity', color: 'hsl(221 83% 53%)' },
  modularity: { label: 'Modularity', color: 'hsl(271 81% 56%)' },
  security: { label: 'Security', color: 'hsl(12 76% 61%)' },
  performance: { label: 'Performance', color: 'hsl(173 58% 39%)' },
  faultResilience: { label: 'Fault Resilience', color: 'hsl(43 74% 49%)' },
  agentReadiness: { label: 'Agent Readiness', color: 'hsl(262 83% 58%)' }
};

// Custom hook to get raw scans with branch information
function useRawRepositoryScans(orgSlug: string, repositorySlug: string) {
  return useQuery({
    queryKey: ['raw-scans', orgSlug, repositorySlug],
    queryFn: async (): Promise<Tables<'scans'>[]> => {
      const supabase = createClient();
      const { data } = await supabase.auth.getSession();
      if (!data.session?.user) {
        return [];
      }

      // First, find the repository by name
      const { data: repoData, error: repoError } = await supabase
        .from('repositories')
        .select('id, name')
        .eq('name', repositorySlug)
        .single();

      if (repoError || !repoData) {
        // If repository not found, try to get all scans
        const { data: allScansData, error: scansError } = await supabase
          .from('scans')
          .select('*')
          .order('created_at', { ascending: false });

        if (scansError || !allScansData) {
          return [];
        }

        return allScansData;
      }

      // Get scans for this specific repository
      const { data: scansData, error: scansError } = await supabase
        .from('scans')
        .select('*')
        .eq('repo_id', repoData.id)
        .order('created_at', { ascending: false });

      if (scansError) {
        return [];
      }

      return scansData || [];
    },
    enabled: !!orgSlug && !!repositorySlug,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000 // 10 minutes
  });
}

// Helper function to transform data to events
function transformToEvents(
  incidents: Incident[],
  issues: IssueWithMetadata[],
  scans: Scan[],
  rawScans: Tables<'scans'>[] = []
): Event[] {
  const events: Event[] = [];

  // Transform incidents
  incidents.forEach((incident) => {
    events.push({
      id: `incident-${incident.id}`,
      type: 'incident',
      name: incident.title,
      description: incident.description,
      timestamp: format(new Date(incident.timestamp), 'PPp'),
      timestampMs: new Date(incident.timestamp).getTime(),
      branch: incident.branch || 'main',
      creator: {
        name: 'Sentry',
        initials: 'S'
      },
      metricsImpact: [],
      duration: '0m',
      errors: Math.floor(Math.random() * 50) + 10 // Mock error count
    });
  });

  // Transform completed issues as pull requests
  issues
    .filter((issue) => issue.status === 'completed' && issue.pr_link)
    .forEach((issue) => {
      // Find the scan that created this issue to get branch info
      const relatedScan = rawScans.find((scan) => scan.id === issue.scan_id);

      events.push({
        id: `pull_request-${issue.id}`,
        type: 'pull_request',
        name: issue.name || 'Pull Request',
        description: issue.description || 'No description available',
        timestamp: format(new Date(issue.created_at), 'PPp'),
        timestampMs: new Date(issue.created_at).getTime(),
        branch: relatedScan?.branch || 'main',
        creator: {
          name: 'Backspace',
          initials: 'B'
        },
        metricsImpact: [],
        duration: '5m'
      });
    });

  // Transform scans
  scans.forEach((scan) => {
    // Find the corresponding raw scan data to get branch info
    const rawScan = rawScans.find((s) => s.id.toString() === scan.id);

    events.push({
      id: `scan-${scan.id}`,
      type: 'scan',
      name: scan.name,
      description: scan.description,
      timestamp: scan.timestamp,
      timestampMs: scan.timestampMs,
      branch: rawScan?.branch || 'main',
      creator: scan.creator,
      metricsImpact: Object.entries(scan.metrics).map(([metric, value]) => ({
        metric,
        oldValue: value - 5, // Mock previous value
        newValue: value,
        change: 5 // Mock change
      })),
      duration: scan.duration,
      agentStatus:
        scan.status === 'completed'
          ? 'completed'
          : scan.status === 'running'
            ? 'in_progress'
            : 'failed'
    });
  });

  return events.sort((a, b) => b.timestampMs - a.timestampMs);
}

// Chart Data Generator
const generateChartData = (
  events: Event[],
  scans: Scan[],
  timeRange: string
) => {
  // Use scans for chart data as they have the metrics
  return scans
    .map((scan) => ({
      id: scan.id,
      timestamp: scan.timestampMs,
      time: format(
        new Date(scan.timestampMs),
        timeRange === '1h' || timeRange === '1d' ? 'HH:mm' : 'MMM d'
      ),
      ...scan.metrics,
      event: events.find((e) => e.id === `scan-${scan.id}`),
      eventType: 'scan' as const
    }))
    .sort((a, b) => a.timestamp - b.timestamp);
};

// Helper Components
const StatusBadge = ({ event }: { event: Event }) => {
  const IconComponent = EVENT_TYPES[event.type].icon;

  const typeColors = {
    incident: 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300',
    pull_request:
      'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300',
    scan: 'bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-300'
  };

  return (
    <Badge
      className={`flex items-center gap-1 px-2 py-0 text-sm ${typeColors[event.type]}`}
    >
      <IconComponent className='h-3 w-3' />
      {EVENT_TYPES[event.type].label}
    </Badge>
  );
};

const MetricDot = ({ cx, cy, payload, metricKey }: any) => {
  if (!payload?.event) return null;

  const event = payload.event;
  const hasMetricImpact = event.metricsImpact?.some(
    (impact: any) => impact.metric === metricKey
  );

  if (!hasMetricImpact) return null;

  // Use the metric-specific color from METRICS_CONFIG
  const metricColor =
    METRICS_CONFIG[metricKey as keyof typeof METRICS_CONFIG]?.color ||
    'hsl(142 76% 36%)';

  return (
    <circle
      cx={cx}
      cy={cy}
      r={4}
      fill={metricColor}
      stroke='#fff'
      strokeWidth={2}
    />
  );
};

const CustomTooltip = ({ active, payload, label }: any) => {
  if (!active || !payload?.length) return null;

  const data = payload[0].payload;
  const event = data.event;

  return (
    <div className='bg-background rounded-lg border p-3 shadow-lg'>
      <p className='text-sm font-medium'>{label}</p>
      {event && (
        <div className='mt-2 space-y-1'>
          <p className='text-sm font-medium'>{event.name}</p>
          <p className='text-muted-foreground text-xs'>{event.timestamp}</p>
        </div>
      )}
      <div className='mt-2 space-y-1'>
        {payload.map((entry: any) => (
          <div key={entry.dataKey} className='flex items-center gap-2 text-xs'>
            <div
              className='h-2 w-2 rounded-full'
              style={{ backgroundColor: entry.color }}
            />
            <span>
              {
                METRICS_CONFIG[entry.dataKey as keyof typeof METRICS_CONFIG]
                  ?.label
              }
              : {Math.round(entry.value)}%
            </span>
          </div>
        ))}
      </div>
    </div>
  );
};

function TypeFilterPopover({
  value,
  onChange
}: {
  value: string[];
  onChange: (value: string[]) => void;
}) {
  const options = Object.keys(EVENT_TYPES);

  const toggleFilter = (option: string) => {
    onChange(
      value.includes(option)
        ? value.filter((v) => v !== option)
        : [...value, option]
    );
  };

  const selectAll = () => {
    onChange(options);
  };

  const clearAll = () => {
    onChange([]);
  };

  const trigger = createFilterTrigger({
    label: 'Type',
    icon: IconFilter
  });

  return (
    <FilterPopover trigger={trigger} searchPlaceholder='Search types...'>
      <CommandGroup>
        {options.map((option) => {
          const config = EVENT_TYPES[option as keyof typeof EVENT_TYPES];
          const IconComponent = config.icon as React.ElementType;
          return (
            <FilterItem
              key={option}
              label={config.label}
              isSelected={value.includes(option)}
              onToggle={() => toggleFilter(option)}
              icon={IconComponent}
            />
          );
        })}
      </CommandGroup>
      <FilterActions
        onSelectAll={selectAll}
        onClearAll={clearAll}
        hasSelections={value.length > 0}
      />
    </FilterPopover>
  );
}

// Main Component
export default function OverviewPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const params = useParams();

  const orgSlug = params['org-slug'] as string;
  const projectSlug = params['project-slug'] as string;

  const [searchTerm, setSearchTerm] = useState('');
  const [typeFilter, setTypeFilter] = useState<string[]>([]);
  const [selectedTimeRange, setSelectedTimeRange] = useState('7d');
  const [visibleColumns, setVisibleColumns] = useState({
    type: true,
    name: true,
    description: true,
    branch: true,
    timestamp: true
  });
  const [hoveredEvent, setHoveredEvent] = useState<string | null>(null);
  const [hoveredChartPoint, setHoveredChartPoint] = useState<string | null>(
    null
  );
  const [isClient, setIsClient] = useState(false);

  // Fetch real data from Supabase
  const { data: incidents = [], isLoading: incidentsLoading } =
    useIncidents(orgSlug);
  const { data: issues = [], isLoading: issuesLoading } = useRepositoryIssues(
    orgSlug,
    projectSlug
  );
  const { data: scans = [], isLoading: scansLoading } = useRepositoryScans(
    orgSlug,
    projectSlug
  );
  const { data: rawScans = [], isLoading: rawScansLoading } =
    useRawRepositoryScans(orgSlug, projectSlug);

  const isLoading =
    incidentsLoading || issuesLoading || scansLoading || rawScansLoading;

  // Transform data to events
  const events = useMemo(() => {
    if (isLoading) return [];
    return transformToEvents(incidents, issues, scans, rawScans);
  }, [incidents, issues, scans, rawScans, isLoading]);

  useEffect(() => {
    setIsClient(true);
    const timeInterval = searchParams.get('timeInterval');
    if (timeInterval && TIME_RANGES.some((r) => r.value === timeInterval)) {
      setSelectedTimeRange(timeInterval);
    }
  }, [searchParams]);

  const filteredEvents = useMemo(() => {
    const timeRangeHours =
      TIME_RANGES.find((r) => r.value === selectedTimeRange)?.hours || 168;
    const cutoffTime = Date.now() - timeRangeHours * 60 * 60 * 1000;

    return events.filter((event) => {
      const matchesSearch = [event.name, event.description].some((text) =>
        text.toLowerCase().includes(searchTerm.toLowerCase())
      );
      const matchesType =
        typeFilter.length === 0 || typeFilter.includes(event.type);
      const matchesTimeRange = event.timestampMs >= cutoffTime;
      return matchesSearch && matchesType && matchesTimeRange;
    });
  }, [events, searchTerm, typeFilter, selectedTimeRange]);

  const chartData = useMemo(
    () => generateChartData(filteredEvents, scans, selectedTimeRange),
    [filteredEvents, scans, selectedTimeRange]
  );

  const yAxisDomain = useMemo(() => {
    if (chartData.length === 0) return [0, 100];
    const allValues = chartData
      .flatMap((d) => METRICS.map((m) => d[m]))
      .filter((v) => typeof v === 'number');
    if (allValues.length === 0) return [0, 100];
    const min = Math.min(...allValues);
    const max = Math.max(...allValues);
    const padding = Math.max((max - min) * 0.1, 5);
    return [
      Math.max(0, Math.floor(min - padding)),
      Math.min(100, Math.ceil(max + padding))
    ];
  }, [chartData]);

  const handleTimeRangeChange = (range: string) => {
    setSelectedTimeRange(range);
    const params = new URLSearchParams(searchParams.toString());
    params.set('timeInterval', range);
    router.replace(`?${params.toString()}`, { scroll: false });
  };

  if (!isClient) return null;

  if (isLoading) {
    return (
      <PageContainer>
        <div className='flex flex-1 flex-col space-y-6'>
          <div className='animate-pulse space-y-6'>
            <div className='h-6 w-48 rounded bg-gray-200'></div>
            <div className='h-64 w-full rounded bg-gray-200'></div>
            <div className='space-y-4'>
              <div className='h-4 w-full rounded bg-gray-200'></div>
              <div className='h-4 w-full rounded bg-gray-200'></div>
              <div className='h-4 w-full rounded bg-gray-200'></div>
            </div>
          </div>
        </div>
      </PageContainer>
    );
  }

  return (
    <PageContainer>
      <div className='flex flex-1 flex-col space-y-6'>
        <div className='space-y-6'>
          <div className='flex items-center'>
            <h2 className='text-sm font-medium'>Events Timeline</h2>
          </div>

          {/* Chart */}
          <div className='flex gap-6 pb-6'>
            <div className='flex-1'>
              <ChartContainer config={chartConfig} className='h-64 w-full'>
                <LineChart
                  data={chartData}
                  onMouseMove={(e) => {
                    if (e?.activeLabel) {
                      const dataPoint = chartData.find(
                        (d) => d.time === e.activeLabel
                      );
                      setHoveredChartPoint(dataPoint?.id || null);
                    }
                  }}
                  onMouseLeave={() => setHoveredChartPoint(null)}
                >
                  <CartesianGrid vertical={false} />
                  <XAxis
                    dataKey='time'
                    tickLine={false}
                    axisLine={false}
                    tickMargin={8}
                    className='text-sm'
                  />
                  <YAxis
                    tickLine={false}
                    axisLine={false}
                    tickMargin={8}
                    domain={yAxisDomain}
                    tickFormatter={(value) => `${value}%`}
                    className='text-sm'
                  />
                  <ChartTooltip cursor={false} content={<CustomTooltip />} />
                  {METRICS.map((metric) => (
                    <Line
                      key={metric}
                      dataKey={metric}
                      type='monotone'
                      stroke={`var(--color-${metric})`}
                      strokeWidth={2}
                      isAnimationActive={false}
                      dot={(props) => {
                        const { key, ...dotProps } = props;
                        return (
                          <MetricDot
                            key={key}
                            {...dotProps}
                            metricKey={metric}
                          />
                        );
                      }}
                    />
                  ))}
                </LineChart>
              </ChartContainer>
            </div>

            {/* Legend */}
            <div className='flex w-48 flex-col justify-center space-y-3 text-sm'>
              {Object.entries(METRICS_CONFIG).map(([metricKey, config]) => (
                <div key={metricKey} className='flex items-center gap-2'>
                  <div
                    className='h-2 w-2 rounded-full'
                    style={{ backgroundColor: config.color }}
                  />
                  <span className='font-medium'>{config.label}</span>
                  <span className='text-muted-foreground ml-auto text-sm'>
                    {chartData.length > 0
                      ? Math.round(
                          (chartData[chartData.length - 1] as any)?.[
                            metricKey
                          ] || 0
                        )
                      : 0}
                    %
                  </span>
                </div>
              ))}
            </div>
          </div>

          {/* Events Table */}
          <div className='space-y-2'>
            {/* Filters */}
            <div className='flex items-center justify-between'>
              <div className='flex items-center gap-2'>
                <TypeFilterPopover
                  value={typeFilter}
                  onChange={setTypeFilter}
                />

                <FilterPopover
                  trigger={
                    <Button variant='outline' size='sm' className='h-7 text-xs'>
                      <IconColumns3 className='mr-1 h-3 w-3' />
                      Columns
                    </Button>
                  }
                >
                  <CommandGroup>
                    {Object.entries(visibleColumns).map(([key, visible]) => (
                      <CommandItem
                        key={key}
                        onSelect={() =>
                          setVisibleColumns((prev) => ({
                            ...prev,
                            [key]: !prev[key as keyof typeof prev]
                          }))
                        }
                        className='text-xs'
                      >
                        <span className='truncate'>
                          {key.charAt(0).toUpperCase() + key.slice(1)}
                        </span>
                        <IconCheck
                          className={cn(
                            'ml-auto h-3 w-3',
                            visible ? 'opacity-100' : 'opacity-0'
                          )}
                        />
                      </CommandItem>
                    ))}
                  </CommandGroup>
                </FilterPopover>

                <Input
                  placeholder='Search events...'
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className='h-7 w-64 border-0 text-xs shadow-none'
                />
              </div>

              <div className='bg-background flex gap-2 rounded-sm border p-1'>
                {TIME_RANGES.map((range) => (
                  <Button
                    key={range.value}
                    variant={
                      selectedTimeRange === range.value ? 'default' : 'ghost'
                    }
                    size='sm'
                    className={cn(
                      'h-6 px-2 text-xs font-medium',
                      selectedTimeRange === range.value
                        ? 'bg-primary text-primary-foreground shadow-sm'
                        : 'text-muted-foreground hover:text-foreground'
                    )}
                    onClick={() => handleTimeRangeChange(range.value)}
                  >
                    {range.label}
                  </Button>
                ))}
              </div>
            </div>

            <Table>
              <TableHeader>
                <TableRow>
                  {Object.entries(visibleColumns).map(
                    ([key, visible]) =>
                      visible && (
                        <TableHead
                          key={key}
                          className={cn(
                            'text-sm font-medium',
                            key === 'description' && 'w-1/3 min-w-0'
                          )}
                        >
                          {key.charAt(0).toUpperCase() + key.slice(1)}
                        </TableHead>
                      )
                  )}
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredEvents.map((event) => {
                  const isHovered = hoveredEvent === event.id;
                  const isChartHighlighted = hoveredChartPoint === event.id;

                  return (
                    <TableRow
                      key={event.id}
                      className={cn(
                        'cursor-pointer transition-colors',
                        (isHovered || isChartHighlighted) && 'bg-muted/50'
                      )}
                      onMouseEnter={() => setHoveredEvent(event.id)}
                      onMouseLeave={() => setHoveredEvent(null)}
                    >
                      {visibleColumns.type && (
                        <TableCell className='w-32 py-2'>
                          <StatusBadge event={event} />
                        </TableCell>
                      )}
                      {visibleColumns.name && (
                        <TableCell className='w-64 py-2'>
                          <div className='truncate text-sm font-medium'>
                            {event.name}
                          </div>
                        </TableCell>
                      )}
                      {visibleColumns.description && (
                        <TableCell className='w-1/3 min-w-0 py-2'>
                          <div className='text-muted-foreground pr-4 text-sm break-words whitespace-normal'>
                            {event.description}
                          </div>
                        </TableCell>
                      )}
                      {visibleColumns.branch && (
                        <TableCell className='w-24 py-2'>
                          <span className='text-sm'>
                            {event.branch || 'main'}
                          </span>
                        </TableCell>
                      )}
                      {visibleColumns.timestamp && (
                        <TableCell className='text-muted-foreground w-32 py-2 text-sm'>
                          {event.timestamp}
                        </TableCell>
                      )}
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>

            {filteredEvents.length === 0 && !isLoading && (
              <div className='text-muted-foreground py-8 text-center'>
                <p>No events found for the selected time range and filters.</p>
              </div>
            )}
          </div>
        </div>
      </div>
    </PageContainer>
  );
}
