import AppSidebar from '@/components/layout/app-sidebar';
import Header from '@/components/layout/header';
import { SidebarInset, SidebarProvider } from '@/components/ui/sidebar';

import { createClient } from '@/lib/supabase/server';
import { redirect } from 'next/navigation';

export default async function DashboardLayout({
  children
}: {
  children: React.ReactNode;
}) {
  const supabase = await createClient();
  const {
    data: { user }
  } = await supabase.auth.getUser();
  if (!user) {
    return redirect('/auth');
  }

  // Fetch orgs and repos
  const { data: userOrgs } = await supabase
    .from('organization_users')
    .select(
      `
      org_id,
      organizations (
        id, 
        name, 
        slug
      )
    `
    )
    .eq('user_id', user.id);

  // TODO: redirect to onboarding flow
  if (!userOrgs || userOrgs.length === 0) {
    return <></>;
  }

  // Transform the data to match the expected format
  const orgs = userOrgs.map((userOrg: any) => ({
    id: userOrg.organizations?.id,
    name: userOrg.organizations?.name,
    slug: userOrg.organizations?.slug
  }));

  // Fetch repositories/projects for all user organizations
  const orgIds = orgs.map((org) => org.id);
  const { data: repos } = await supabase
    .from('repositories')
    .select('id, name, url, org_id')
    .in('org_id', orgIds);

  if (!repos || repos.length === 0) {
    return <></>;
  }

  return (
    <SidebarProvider>
      <AppSidebar orgs={orgs} repos={repos} />
      <SidebarInset>
        <Header />
        {/* page main content */}
        {children}
        {/* page main content ends */}
      </SidebarInset>
    </SidebarProvider>
  );
}
