'use client';

import { useState, useMemo } from 'react';
import { Plus, Clock, Github } from 'lucide-react';
import PageContainer from '@/components/layout/page-container';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';

import Link from 'next/link';
import { useParams } from 'next/navigation';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';
import { IconPlus, IconGitPullRequest } from '@tabler/icons-react';
import { cn } from '@/lib/utils';
import {
  useRepositories,
  type RepositoryWithScans
} from '@/hooks/supabase/use-repositories';

type ColumnKey = 'repository' | 'scanCount' | 'addedAt';
type VisibleColumns = Record<ColumnKey, boolean>;

const COLUMN_LABELS: Record<ColumnKey, string> = {
  repository: 'Repository',
  scanCount: 'Recent Scans',
  addedAt: 'Added at'
};

// Helper function to extract repository name from URL
function getRepositoryDisplayName(repo: RepositoryWithScans): string {
  if (repo.name) {
    return repo.name;
  }

  if (repo.url) {
    const urlParts = repo.url.split('/');
    if (urlParts.length >= 2) {
      return `${urlParts[urlParts.length - 2]}/${urlParts[urlParts.length - 1]}`;
    }
  }

  return 'Unknown Repository';
}

function EmptyState({ orgSlug }: { orgSlug: string }) {
  return (
    <div className='py-16 text-center'>
      <div className='bg-muted mx-auto mb-8 flex h-48 w-48 items-center justify-center rounded-lg'>
        <Github className='text-muted-foreground h-24 w-24 opacity-50' />
      </div>
      <h3 className='mb-2 text-xl font-semibold'>No repositories yet</h3>
      <p className='text-muted-foreground mx-auto mb-6 max-w-md'>
        Connect your first repository to start monitoring code health and
        automating maintenance.
      </p>
      <Button size='lg'>
        <Plus className='mr-2 h-4 w-4' />
        Connect Repository
      </Button>
    </div>
  );
}

function ProjectTableFilters({
  searchTerm,
  onSearchChange,
  children
}: {
  searchTerm: string;
  onSearchChange: (value: string) => void;
  children?: React.ReactNode;
}) {
  return (
    <div className='flex items-center justify-between gap-3'>
      <Input
        placeholder='Search repositories...'
        value={searchTerm}
        onChange={(e) => onSearchChange(e.target.value)}
        className='h-7 w-64 border-0 text-xs shadow-none'
      />
      <div className='flex gap-2'>{children}</div>
    </div>
  );
}

function ProjectRow({
  repository,
  orgSlug,
  visibleColumns,
  isHovered,
  onMouseEnter,
  onMouseLeave,
  onClick
}: {
  repository: RepositoryWithScans;
  orgSlug: string;
  visibleColumns: VisibleColumns;
  isHovered: boolean;
  onMouseEnter: () => void;
  onMouseLeave: () => void;
  onClick: () => void;
}) {
  const displayName = getRepositoryDisplayName(repository);

  return (
    <TableRow
      className={cn(
        'cursor-pointer transition-colors',
        isHovered && 'bg-muted/50'
      )}
      onMouseEnter={onMouseEnter}
      onMouseLeave={onMouseLeave}
      onClick={onClick}
    >
      {visibleColumns.repository && (
        <TableCell className='py-3'>
          <Link
            href={`/${orgSlug}/p/${repository.id}`}
            className='flex items-center gap-2 hover:underline'
          >
            <div className='text-sm font-medium'>{displayName}</div>
          </Link>
        </TableCell>
      )}
      {visibleColumns.scanCount && (
        <TableCell className='py-3'>
          <div className='text-muted-foreground flex items-center gap-1 text-sm'>
            <IconGitPullRequest className='h-4 w-4' />
            <span>{repository.scanCount}</span>
          </div>
        </TableCell>
      )}
      {visibleColumns.addedAt && (
        <TableCell className='text-muted-foreground py-3 text-sm'>
          <div className='flex items-center gap-1'>
            <Clock className='h-4 w-4' />
            {new Date(repository.created_at).toLocaleDateString()}
          </div>
        </TableCell>
      )}
    </TableRow>
  );
}

export default function ProjectsPage() {
  const params = useParams();
  const orgSlug = params['org-slug'] as string;

  // Use our hooks
  const {
    data: repositories = [],
    isLoading,
    error
  } = useRepositories(orgSlug);

  const [searchTerm, setSearchTerm] = useState('');
  const [visibleColumns] = useState<VisibleColumns>({
    repository: true,
    scanCount: true,
    addedAt: true
  });
  const [hoveredRepository, setHoveredRepository] = useState<number | null>(
    null
  );

  const filteredRepositories = useMemo(() => {
    return repositories.filter((repository) => {
      const displayName = getRepositoryDisplayName(repository);
      const matchesSearch = [repository.name, displayName, repository.url].some(
        (text) => text?.toLowerCase().includes(searchTerm.toLowerCase())
      );
      return matchesSearch;
    });
  }, [searchTerm, repositories]);

  const handleRepositoryClick = (repositoryId: number) => {
    // Handle repository click if needed
  };

  // Loading states
  if (isLoading) {
    return (
      <PageContainer scrollable>
        <div className='flex items-center justify-center py-12'>
          <div className='text-muted-foreground text-sm'>
            Loading repositories...
          </div>
        </div>
      </PageContainer>
    );
  }

  // Error state
  if (error) {
    return (
      <PageContainer scrollable>
        <div className='flex items-center justify-center py-12'>
          <div className='text-red-500'>
            {error instanceof Error
              ? error.message
              : 'Failed to load repositories'}
          </div>
        </div>
      </PageContainer>
    );
  }

  // Empty state
  if (filteredRepositories.length === 0 && !searchTerm) {
    return (
      <PageContainer scrollable>
        <EmptyState orgSlug={orgSlug} />
      </PageContainer>
    );
  }

  return (
    <PageContainer scrollable>
      <div className='w-full space-y-6'>
        {/* Projects Table */}
        <div className='space-y-4'>
          <ProjectTableFilters
            searchTerm={searchTerm}
            onSearchChange={setSearchTerm}
          >
            <Button size='sm' className='h-7 text-sm'>
              <IconPlus className='mr-1 h-3 w-3' />
              Add Repository
            </Button>
          </ProjectTableFilters>

          {filteredRepositories.length === 0 ? (
            <div className='py-16 text-center'>
              <p className='text-muted-foreground'>
                No repositories found matching your search.
              </p>
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  {Object.entries(visibleColumns).map(
                    ([key, visible]) =>
                      visible && (
                        <TableHead key={key} className='text-sm font-medium'>
                          {COLUMN_LABELS[key as ColumnKey]}
                        </TableHead>
                      )
                  )}
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredRepositories.map((repository) => (
                  <ProjectRow
                    key={repository.id}
                    repository={repository}
                    orgSlug={orgSlug}
                    visibleColumns={visibleColumns}
                    isHovered={hoveredRepository === repository.id}
                    onMouseEnter={() => setHoveredRepository(repository.id)}
                    onMouseLeave={() => setHoveredRepository(null)}
                    onClick={() => handleRepositoryClick(repository.id)}
                  />
                ))}
              </TableBody>
            </Table>
          )}
        </div>
      </div>
    </PageContainer>
  );
}
