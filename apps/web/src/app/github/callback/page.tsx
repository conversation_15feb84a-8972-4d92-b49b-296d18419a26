'use client';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { CheckCircle, Copy } from 'lucide-react';
import { useSearchParams } from 'next/navigation';
import { useState } from 'react';

export default function GitHubCallbackPage() {
  const searchParams = useSearchParams();
  const installationId = searchParams.get('installation_id');
  const setupAction = searchParams.get('setup_action');
  const [copied, setCopied] = useState(false);

  const copyInstallationId = async () => {
    if (installationId) {
      await navigator.clipboard.writeText(installationId);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    }
  };

  return (
    <div className="container mx-auto p-8">
      <Card className="max-w-md mx-auto">
        <CardHeader>
          <div className="flex items-center gap-2">
            <CheckCircle className="h-6 w-6 text-green-500" />
            <CardTitle className="text-2xl font-bold">
              Installation Complete!
            </CardTitle>
          </div>
          <CardDescription>
            Backspace GitHub App has been successfully installed
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {installationId && (
            <div className="space-y-2">
              <label className="text-sm font-medium">Installation ID:</label>
              <div className="flex items-center gap-2">
                <code className="flex-1 p-2 bg-muted rounded text-sm">
                  {installationId}
                </code>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={copyInstallationId}
                >
                  {copied ? 'Copied!' : <Copy className="h-4 w-4" />}
                </Button>
              </div>
            </div>
          )}
          
          {setupAction && (
            <div className="space-y-2">
              <label className="text-sm font-medium">Action:</label>
              <code className="block p-2 bg-muted rounded text-sm">
                {setupAction}
              </code>
            </div>
          )}

          <div className="text-sm text-muted-foreground">
            <p>You can now close this tab and return to your dashboard.</p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}