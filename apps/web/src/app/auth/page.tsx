import { redirect } from 'next/navigation';
import { createClient } from '@/lib/supabase/server';

export default async function AuthPage() {
  const supabase = await createClient();
  const {
    data: { user }
  } = await supabase.auth.getUser();

  // If user is not logged in, redirect to login
  if (!user) {
    redirect('/auth/login');
  }

  // User is logged in, fetch their first organization
  const { data: orgUser, error: orgUserError } = await supabase
    .from('organization_users')
    .select('org_id')
    .eq('user_id', user.id)
    .eq('status', 'active')
    .single();

  if (orgUserError || !orgUser) {
    // If user doesn't belong to any organization or there's an error,
    // redirect to login page
    redirect('/auth/login');
  }

  // Fetch the organization details
  const { data: org, error: orgError } = await supabase
    .from('organizations')
    .select('slug')
    .eq('id', orgUser.org_id)
    .single();

  if (orgError || !org?.slug) {
    // If organization doesn't exist or has no slug, redirect to login
    redirect('/auth/login');
  }

  // Redirect to the first organization's inbox
  redirect(`/${org.slug}/inbox`);
}
