'use client';

import { useEffect, useState } from 'react';
import { useSearchParams, useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { createClient } from '@/lib/supabase/client';
import type { Tables } from '@/lib/supabase/schema';
import {
  CheckCircle2,
  Clock,
  AlertTriangle,
  Rocket,
  Sparkles,
  Shield,
  Zap,
  ArrowRight,
  RefreshCw,
  GitBranch
} from 'lucide-react';

export default function SetupPage() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const [status, setStatus] = useState<
    'loading' | 'polling' | 'error' | 'manual_setup'
  >('loading');
  const [installationId, setInstallationId] = useState<string | null>(null);
  const [organization, setOrganization] =
    useState<Tables<'organizations'> | null>(null);
  const [pollAttempts, setPollAttempts] = useState(0);
  const [errorMessage, setErrorMessage] = useState<string>('');
  const [progress, setProgress] = useState(0);

  useEffect(() => {
    const installation_id = searchParams.get('installation_id');
    const setup_action = searchParams.get('setup_action');

    console.log('Setup page loaded:', { installation_id, setup_action });

    if (installation_id) {
      setInstallationId(installation_id);
      checkForOrganization(installation_id);
    } else {
      setStatus('manual_setup');
    }
  }, [searchParams]);

  useEffect(() => {
    if (status === 'polling') {
      const progressValue = Math.min((pollAttempts / 10) * 100, 90);
      setProgress(progressValue);
    }
  }, [pollAttempts, status]);

  const checkForOrganization = async (
    installation_id: string,
    currentAttempt: number = 0
  ) => {
    try {
      console.log(
        `Checking for organization with installation_id: ${installation_id} (attempt ${currentAttempt + 1}/10)`
      );

      const { data, error } = await createClient()
        .from('organizations')
        .select('*')
        .eq('installation_id', installation_id)
        .single();

      if (error) {
        console.log('Organization not found yet:', error.message);

        if (currentAttempt < 9) {
          // 0-9 = 10 attempts
          setStatus('polling');
          setPollAttempts(currentAttempt + 1);
          setTimeout(
            () => checkForOrganization(installation_id, currentAttempt + 1),
            2000
          );
        } else {
          setStatus('error');
          setErrorMessage(
            'Setup is taking longer than expected. This could mean the GitHub webhook is still processing, or there was an issue with the installation.'
          );
        }
        return;
      }

      if (data) {
        console.log('Organization found:', data);
        setOrganization(data);
        setProgress(100);

        // Small delay to show completion
        setTimeout(() => {
          const orgSlug = data.slug;
          console.log('Redirecting to:', `/${orgSlug}/projects`);
          router.push(`/${orgSlug}/projects`);
        }, 1000);
      }
    } catch (error) {
      console.error('Error checking for organization:', error);
      setStatus('error');
      setErrorMessage(
        'There was an issue connecting to our servers. Please check your internet connection and try again.'
      );
    }
  };

  const features = [
    {
      icon: Shield,
      text: 'Enterprise-grade security',
      description:
        'Bank-level security with SOC 2 compliance and encrypted data processing.'
    },
    {
      icon: Zap,
      text: 'Lightning-fast code analysis',
      description:
        'Analyze entire codebases in seconds with our optimized scanning engine.'
    },
    {
      icon: Sparkles,
      text: 'AI-powered insights',
      description:
        'Get intelligent recommendations and automated code improvements.'
    }
  ];

  return (
    <div className='bg-background flex min-h-screen items-center justify-center'>
      <div className='space-y-8 text-center'>
        {/* Logo Only */}
        <div className='mx-auto h-24 w-24'>
          {/* Backspace Logo - White square with <_ characters */}
          <div className='flex h-24 w-24 items-center justify-center bg-white dark:bg-white'>
            <div className='flex items-center font-mono'>
              {/* < character */}
              <span className='text-5xl leading-none font-bold text-black'>
                &lt;
              </span>
              {/* _ character that blinks */}
              <span
                className={`text-5xl leading-none font-bold text-black ${
                  status === 'loading' || status === 'polling'
                    ? 'animate-[blink_1s_infinite]'
                    : ''
                }`}
              >
                _
              </span>
            </div>
          </div>
        </div>

        {/* Error State Only */}
        {status === 'error' && (
          <div className='animate-in fade-in mx-auto max-w-md space-y-6 duration-500'>
            <div className='space-y-3'>
              <div className='bg-destructive/10 mx-auto flex h-8 w-8 items-center justify-center rounded-full'>
                <AlertTriangle className='text-destructive h-4 w-4' />
              </div>
              <p className='text-muted-foreground text-sm'>
                Setup is taking longer than expected
              </p>
            </div>

            <div className='flex justify-center gap-3'>
              <Button
                onClick={() => window.location.reload()}
                variant='outline'
                size='sm'
                className='text-xs'
              >
                <RefreshCw className='mr-2 h-3 w-3' />
                Retry
              </Button>
              <Button
                onClick={() =>
                  window.open(
                    'https://github.com/apps/backspace-agent/installations/new',
                    '_blank'
                  )
                }
                variant='outline'
                size='sm'
                className='text-xs'
              >
                <GitBranch className='mr-2 h-3 w-3' />
                Reinstall
              </Button>
            </div>
          </div>
        )}

        {/* Manual Setup */}
        {status === 'manual_setup' && (
          <div className='animate-in fade-in space-y-6 duration-500'>
            <Button
              onClick={() =>
                window.open(
                  'https://github.com/apps/backspace-agent/installations/new',
                  '_blank'
                )
              }
              size='lg'
              className='px-8'
            >
              <GitBranch className='mr-2 h-4 w-4' />
              Install GitHub App
            </Button>
          </div>
        )}

        {/* Success State */}
        {organization && (
          <div className='animate-in fade-in space-y-4 duration-500'>
            <div className='mx-auto flex h-8 w-8 items-center justify-center rounded-full bg-green-100 dark:bg-green-900/20'>
              <CheckCircle2 className='h-4 w-4 text-green-600' />
            </div>
            <p className='text-muted-foreground text-sm'>
              Redirecting to your dashboard...
            </p>
          </div>
        )}
      </div>
    </div>
  );
}
