#!/usr/bin/env python3
"""Test script for debugging docker_builder issues."""

import asyncio
import logging
import sys
import os
from datetime import datetime

# Add the apps/agent/src directory to the path
sys.path.insert(0, 'apps/agent/src')

from utils.docker_builder import build_and_push_repo_image

# Set up detailed logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler(f'docker_build_test_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log')
    ]
)

logger = logging.getLogger(__name__)

async def test_build():
    """Test the build function with a simple repository."""
    
    # Test with a small, simple repository
    test_repo = "https://github.com/octocat/Hello-World"  # Very simple test repo
    test_installation_id = 71538268  # Your installation ID from the logs
    
    logger.info("=" * 60)
    logger.info("STARTING DOCKER BUILD TEST")
    logger.info("=" * 60)
    logger.info(f"Repository: {test_repo}")
    logger.info(f"Installation ID: {test_installation_id}")
    logger.info(f"Timestamp: {datetime.now()}")
    
    try:
        logger.info("Calling build_and_push_repo_image...")
        
        # Set a smaller resource allocation for testing
        os.environ["BASE_IMAGE"] = "alpine:latest"  # Use lighter image
        
        result = await build_and_push_repo_image(
            repo_url=test_repo,
            installation_id=test_installation_id,
            force_rebuild=True  # Force rebuild to see the full process
        )
        
        logger.info("=" * 60)
        logger.info("BUILD COMPLETED SUCCESSFULLY!")
        logger.info("=" * 60)
        logger.info(f"Result: {result}")
        
        return result
        
    except Exception as e:
        logger.error("=" * 60)
        logger.error("BUILD FAILED!")
        logger.error("=" * 60)
        logger.error(f"Error: {e}")
        logger.error(f"Error type: {type(e)}")
        import traceback
        logger.error(f"Traceback:\n{traceback.format_exc()}")
        raise

async def main():
    """Main test function."""
    logger.info("Starting Docker Builder Test...")
    
    try:
        result = await test_build()
        logger.info(f"Test completed successfully: {result}")
        return 0
    except Exception as e:
        logger.error(f"Test failed: {e}")
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code) 