#!/usr/bin/env python3
"""
Test the autonomous coding agent with the real repository
https://github.com/alhridoy/extream_events_analysis
"""

import asyncio
import logging
from src.sandbox.local_sandbox import LocalSandbox
from src.utils.config import get_settings
from src.agent.coding_agent import CodingAgent

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Your actual repository
REPO_URL = "https://github.com/alhridoy/extream_events_analysis"
TEST_PROMPT = "Add a simple health check endpoint to verify the API is running"
BRANCH_NAME = "feature/health-check-endpoint"

async def test_real_repo_integration():
    """Test with your actual repository"""
    
    print("🚀 Testing Autonomous Coding Agent with Real Repository")
    print("=" * 60)
    print(f"📁 Repository: {REPO_URL}")
    print(f"💭 Prompt: {TEST_PROMPT}")
    print(f"🌿 Branch: {BRANCH_NAME}")
    print("=" * 60)
    
    settings = get_settings()
    
    # Verify we have the required API keys
    if not settings.github_token:
        print("❌ GitHub token not found in environment")
        return False
    
    if not settings.anthropic_api_key:
        print("❌ Anthropic API key not found in environment")
        return False
    
    print(f"✅ GitHub token: {settings.github_token[:8]}...")
    print(f"✅ Anthropic API key: {settings.anthropic_api_key[:8]}...")
    
    # Initialize components
    sandbox = LocalSandbox()
    coding_agent = CodingAgent(
        anthropic_api_key=settings.anthropic_api_key,
        github_token=settings.github_token
    )
    
    session_id = None
    
    try:
        # Step 1: Create session
        print("\n1. 🏗️  Creating sandbox session...")
        session_id = await sandbox.create_session()
        print(f"   ✅ Session created: {session_id}")
        
        # Step 2: Clone repository
        print("\n2. 📥 Cloning repository...")
        repo_info = await sandbox.clone_repository(session_id, REPO_URL)
        
        if not repo_info.get("success"):
            print(f"   ❌ Failed to clone repository")
            return False
            
        print(f"   ✅ Repository cloned successfully")
        print(f"   📊 Found {repo_info.get('file_count', 0)} files")
        print(f"   📂 Local path: {repo_info.get('repo_dir', 'unknown')}")
        
        # Step 3: Test repository info parsing
        print("\n3. 🔍 Testing repository info parsing...")
        parsed_info = await sandbox.get_repo_info(session_id)
        
        if parsed_info.get("success"):
            print(f"   ✅ Owner: {parsed_info.get('owner')}")
            print(f"   ✅ Repo name: {parsed_info.get('repo_name')}")
            print(f"   ✅ Remote URL: {parsed_info.get('remote_url')}")
        else:
            print(f"   ❌ Failed to parse repo info: {parsed_info.get('error')}")
        
        # Step 4: Setup git authentication
        print("\n4. 🔐 Setting up git authentication...")
        auth_result = await sandbox.setup_git_auth(session_id, settings.github_token)
        
        if auth_result.get("success"):
            print("   ✅ Git authentication configured")
        else:
            print(f"   ❌ Git auth failed: {auth_result.get('error')}")
            return False
        
        # Step 5: Test basic file operations
        print("\n5. 📝 Testing file operations...")
        
        # Read a file to understand the project structure
        readme_result = await sandbox.read_file(session_id, "README.md")
        if readme_result.get("success"):
            content = readme_result.get("content", "")
            print(f"   ✅ Read README.md ({len(content)} characters)")
            print(f"   📄 Preview: {content[:100]}...")
        else:
            print("   ⚠️  No README.md found, will create one")
        
        # Step 6: Create a test branch
        print("\n6. 🌿 Creating test branch...")
        branch_result = await sandbox.execute_command(session_id, f"git checkout -b {BRANCH_NAME}")
        
        if branch_result.get("success"):
            print(f"   ✅ Created branch: {BRANCH_NAME}")
        else:
            print(f"   ❌ Failed to create branch: {branch_result.get('stderr')}")
            return False
        
        # Step 7: Make a simple test change
        print("\n7. ✏️  Making test changes...")
        
        # Create a simple health check file
        health_check_content = '''"""
Simple health check endpoint for the extreme events analysis API
"""

from datetime import datetime
from typing import Dict, Any

def get_health_status() -> Dict[str, Any]:
    """
    Get the health status of the application
    
    Returns:
        Dict containing health status information
    """
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "service": "extreme_events_analysis",
        "version": "1.0.0"
    }

if __name__ == "__main__":
    status = get_health_status()
    print(f"Health Status: {status}")
'''
        
        write_result = await sandbox.write_file(session_id, "health_check.py", health_check_content)
        
        if write_result.get("success"):
            print("   ✅ Created health_check.py")
        else:
            print(f"   ❌ Failed to write file: {write_result}")
            return False
        
        # Step 8: Commit the changes
        print("\n8. 💾 Committing changes...")
        
        # Check git status first
        status_check = await sandbox.execute_command(session_id, "git status")
        print(f"   📊 Git status: {status_check.get('stdout', '')[:200]}...")
        
        # Add files
        add_result = await sandbox.execute_command(session_id, "git add .")
        if not add_result.get("success"):
            print(f"   ❌ Failed to add files: {add_result.get('stderr')}")
            return False
        else:
            print(f"   ✅ Added files to staging")
        
        # Check what's staged
        staged_check = await sandbox.execute_command(session_id, "git diff --cached --name-only")
        if staged_check.get("success"):
            staged_files = staged_check.get("stdout", "").strip()
            if staged_files:
                print(f"   📝 Staged files: {staged_files}")
            else:
                print("   ⚠️  No files staged for commit")
        
        # Commit
        commit_message = "Add health check endpoint for API monitoring"
        commit_result = await sandbox.execute_command(
            session_id, 
            f'git commit -m "{commit_message}"'
        )
        
        if commit_result.get("success"):
            print(f"   ✅ Committed changes: {commit_message}")
            print(f"   📝 Commit output: {commit_result.get('stdout', '')}")
        else:
            print(f"   ❌ Failed to commit: {commit_result.get('stderr')}")
            return False
        
        # Step 9: Push the branch
        print("\n9. 🚀 Pushing branch to GitHub...")
        push_result = await sandbox.push_branch(session_id, BRANCH_NAME)
        
        if push_result.get("success"):
            print(f"   ✅ Successfully pushed branch: {BRANCH_NAME}")
            print(f"   📤 Push output: {push_result.get('stdout', '')[:100]}...")
        else:
            print(f"   ❌ Push failed: {push_result.get('stderr')}")
            print(f"   🔍 Error details: {push_result}")
            return False
        
        # Step 10: Create pull request
        print("\n10. 🔗 Creating pull request...")
        
        pr_title = "Add health check endpoint for API monitoring"
        pr_body = """## Summary
This PR adds a simple health check endpoint to monitor the API status.

## Changes Made
- Added `health_check.py` with health status functionality
- Provides timestamp, service name, and version information
- Can be used for monitoring and alerting

## Testing
Run the health check:
```python
python health_check.py
```

This change was generated automatically by the Autonomous Coding Agent."""
        
        pr_result = await sandbox.create_pull_request(
            session_id, pr_title, pr_body, BRANCH_NAME
        )
        
        if pr_result.get("success"):
            pr_url = pr_result.get("pr_url", pr_result.get("stdout", ""))
            method = pr_result.get("method", "unknown")
            print(f"   ✅ Pull request created!")
            print(f"   🔗 URL: {pr_url}")
            print(f"   🛠️  Method: {method}")
            
            if method == "manual":
                print(f"   📝 Please visit the URL above to complete PR creation")
        else:
            print(f"   ❌ PR creation failed: {pr_result.get('error')}")
            # This might not be a complete failure - branch could still be pushed
        
        print("\n" + "=" * 60)
        print("🎉 INTEGRATION TEST COMPLETED!")
        print("=" * 60)
        
        print("\n✅ What worked:")
        print("   - Repository cloning")
        print("   - Git authentication setup") 
        print("   - File creation and modification")
        print("   - Branch creation and commits")
        print("   - Branch pushing to GitHub")
        if pr_result.get("success"):
            print("   - Pull request creation")
        
        print(f"\n🔗 Check your repository: {REPO_URL}")
        print(f"🌿 Look for branch: {BRANCH_NAME}")
        
        return True
        
    except Exception as e:
        print(f"\n❌ Test failed with exception: {str(e)}")
        logger.exception("Full error details:")
        return False
        
    finally:
        # Cleanup
        if session_id:
            await sandbox.cleanup_session(session_id)
        await sandbox.cleanup_all()
        print(f"\n🧹 Cleaned up session: {session_id}")


async def test_full_agent_workflow():
    """Test the complete agent workflow with streaming"""
    
    print("\n" + "=" * 60)
    print("🤖 TESTING FULL AGENT WORKFLOW")
    print("=" * 60)
    
    settings = get_settings()
    sandbox = LocalSandbox()
    coding_agent = CodingAgent(
        anthropic_api_key=settings.anthropic_api_key,
        github_token=settings.github_token
    )
    
    session_id = await sandbox.create_session()
    
    try:
        # Clone the repository
        repo_info = await sandbox.clone_repository(session_id, REPO_URL)
        
        print("🔄 Starting AI coding process...")
        
        # Stream the coding process
        async for event in coding_agent.stream_coding_process(
            session_id=session_id,
            repo_info=repo_info,
            prompt=TEST_PROMPT,
            branch_name="feature/ai-health-check",
            pr_title="AI Generated: Add health check endpoint",
            sandbox_manager=sandbox
        ):
            event_type = event.type
            timestamp = getattr(event, 'timestamp', 'unknown')
            
            if event_type == "status":
                print(f"📊 {getattr(event, 'message', '')}")
            elif event_type == "ai_message":
                print(f"🤖 AI: {getattr(event, 'message', '')}")
            elif event_type == "tool_call":
                tool_name = getattr(event, 'tool_name', 'unknown')
                print(f"🔧 Tool: {tool_name}")
            elif event_type == "git_operation":
                command = getattr(event, 'command', '')
                success = getattr(event, 'success', False)
                status = "✅" if success else "❌"
                print(f"{status} Git: {command}")
            elif event_type == "pr_created":
                pr_url = getattr(event, 'pr_url', '')
                print(f"🎉 PR Created: {pr_url}")
            elif event_type == "error":
                error_msg = getattr(event, 'error_message', '')
                print(f"❌ Error: {error_msg}")
        
        print("✅ Full agent workflow completed!")
        
    except Exception as e:
        print(f"❌ Agent workflow failed: {str(e)}")
        
    finally:
        await sandbox.cleanup_session(session_id)
        await sandbox.cleanup_all()


if __name__ == "__main__":
    import sys
    
    if "--full" in sys.argv:
        # Test the full AI agent workflow
        asyncio.run(test_full_agent_workflow())
    else:
        # Test basic integration
        success = asyncio.run(test_real_repo_integration())
        
        if success:
            print("\n🚀 Ready to test full agent workflow:")
            print("   python test_real_repo.py --full")
        else:
            print("\n❌ Basic integration failed. Fix issues before testing full workflow.")