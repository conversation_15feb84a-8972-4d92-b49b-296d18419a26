#!/usr/bin/env python3
"""
Demo of the enhanced evaluation system features
"""

import json
import sys
import os
from pathlib import Path
from datetime import datetime
from dataclasses import dataclass, asdict
from typing import Dict, List, Any

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def demo_tiered_datasets():
    """Demonstrate tiered evaluation datasets"""
    print("🎯 Demo: Tiered Evaluation Datasets")
    print("=" * 50)
    
    # Simulate dataset loading
    datasets = {
        "quick": {
            "tasks": 5,
            "avg_time": "3 min",
            "use_case": "Fast validation, CI/CD pipeline"
        },
        "standard": {
            "tasks": 50,
            "avg_time": "10 min", 
            "use_case": "Regular benchmarking, development"
        },
        "comprehensive": {
            "tasks": 200,
            "avg_time": "25 min",
            "use_case": "Production readiness, releases"
        }
    }
    
    for tier, info in datasets.items():
        print(f"\n📊 {tier.upper()} Tier:")
        print(f"   Tasks: {info['tasks']}")
        print(f"   Avg Time: {info['avg_time']}")
        print(f"   Use Case: {info['use_case']}")
    
    print("\n✅ Tiered datasets provide flexible evaluation options")

def demo_pass_at_k_evaluation():
    """Demonstrate Pass@k reliability evaluation"""
    print("\n\n🔄 Demo: Pass@k Reliability Evaluation")
    print("=" * 50)
    
    # Simulate Pass@3 results
    task_results = [
        {"attempt": 1, "success": True, "score": 0.9, "time": 45.2},
        {"attempt": 2, "success": False, "score": 0.4, "time": 67.8},
        {"attempt": 3, "success": True, "score": 0.8, "time": 52.1}
    ]
    
    # Calculate metrics
    k = len(task_results)
    success_count = sum(1 for r in task_results if r["success"])
    success_rate = success_count / k
    
    scores = [r["score"] for r in task_results]
    avg_score = sum(scores) / len(scores)
    variance = sum((s - avg_score) ** 2 for s in scores) / len(scores)
    
    reliability_score = success_rate * (1 - min(variance, 0.5))
    
    print(f"\n📈 Pass@{k} Results:")
    print(f"   Success Rate: {success_rate:.1%}")
    print(f"   Average Score: {avg_score:.3f}")
    print(f"   Score Variance: {variance:.3f}")
    print(f"   Reliability Score: {reliability_score:.3f}")
    
    print("\n🔍 Individual Attempts:")
    for result in task_results:
        status = "✅ PASS" if result["success"] else "❌ FAIL"
        print(f"   Attempt {result['attempt']}: {status} (Score: {result['score']:.2f}, Time: {result['time']:.1f}s)")
    
    print("\n✅ Pass@k provides reliability measurement beyond single attempts")

def demo_trajectory_scoring():
    """Demonstrate trajectory-based evaluation"""
    print("\n\n🛤️  Demo: Trajectory-Based Evaluation")
    print("=" * 50)
    
    # Simulate agent trajectory
    trajectory = [
        {"step": 1, "action": "analyze_requirements", "success": True, "score": 0.9},
        {"step": 2, "action": "identify_files", "success": True, "score": 0.8},
        {"step": 3, "action": "implement_changes", "success": False, "score": 0.3},
        {"step": 4, "action": "fix_errors", "success": True, "score": 0.7},
        {"step": 5, "action": "create_pr", "success": True, "score": 0.9}
    ]
    
    # Calculate trajectory metrics
    total_steps = len(trajectory)
    successful_steps = sum(1 for step in trajectory if step["success"])
    step_scores = [step["score"] for step in trajectory]
    avg_step_score = sum(step_scores) / len(step_scores)
    
    # Final completion score (mock)
    final_completion = 0.8
    
    # Trajectory score: 40% step quality + 60% final outcome
    trajectory_score = (0.4 * avg_step_score) + (0.6 * final_completion)
    
    print(f"\n📊 Trajectory Analysis:")
    print(f"   Total Steps: {total_steps}")
    print(f"   Successful Steps: {successful_steps}")
    print(f"   Step Success Rate: {successful_steps/total_steps:.1%}")
    print(f"   Average Step Score: {avg_step_score:.3f}")
    print(f"   Final Completion: {final_completion:.3f}")
    print(f"   Trajectory Score: {trajectory_score:.3f}")
    
    print("\n🔍 Step-by-Step Breakdown:")
    for step in trajectory:
        status = "✅" if step["success"] else "❌"
        print(f"   Step {step['step']}: {status} {step['action']} (Score: {step['score']:.2f})")
    
    print("\n✅ Trajectory scoring provides partial credit for correct intermediate steps")

def demo_context_efficiency():
    """Demonstrate context efficiency evaluation"""
    print("\n\n🎯 Demo: Context Efficiency Evaluation")
    print("=" * 50)
    
    # Simulate file access patterns
    expected_files = {"models.py", "routes.py", "utils.py", "config.py"}
    accessed_files = {"models.py", "routes.py", "helpers.py", "tests.py", "README.md"}
    
    # Calculate metrics
    relevant_files = expected_files.intersection(accessed_files)
    precision = len(relevant_files) / len(accessed_files)
    recall = len(relevant_files) / len(expected_files)
    
    if precision + recall > 0:
        f1_score = 2 * (precision * recall) / (precision + recall)
    else:
        f1_score = 0.0
    
    context_efficiency = 0.6 * precision + 0.4 * recall
    
    print(f"\n📁 File Access Analysis:")
    print(f"   Expected Files: {len(expected_files)}")
    print(f"   Accessed Files: {len(accessed_files)}")
    print(f"   Relevant Files: {len(relevant_files)}")
    print(f"   Precision: {precision:.3f}")
    print(f"   Recall: {recall:.3f}")
    print(f"   F1 Score: {f1_score:.3f}")
    print(f"   Context Efficiency: {context_efficiency:.3f}")
    
    print(f"\n📂 File Breakdown:")
    print(f"   Expected: {', '.join(sorted(expected_files))}")
    print(f"   Accessed: {', '.join(sorted(accessed_files))}")
    print(f"   Relevant: {', '.join(sorted(relevant_files))}")
    print(f"   Missed: {', '.join(sorted(expected_files - accessed_files))}")
    print(f"   Unnecessary: {', '.join(sorted(accessed_files - expected_files))}")
    
    print("\n✅ Context efficiency measures how well the agent identifies relevant code")

def demo_regression_detection():
    """Demonstrate regression detection"""
    print("\n\n🔍 Demo: Regression Detection")
    print("=" * 50)
    
    # Simulate version comparison
    baseline_metrics = {
        "version": "v1.0",
        "success_rate": 0.85,
        "scores": {
            "correctness": 0.90,
            "adherence": 0.85,
            "quality": 0.80,
            "safety": 0.95
        }
    }
    
    current_metrics = {
        "version": "v1.1", 
        "success_rate": 0.78,  # Regression
        "scores": {
            "correctness": 0.85,  # Slight regression
            "adherence": 0.82,    # Slight regression
            "quality": 0.75,      # Regression
            "safety": 0.94        # Stable
        }
    }
    
    # Calculate deltas
    success_delta = current_metrics["success_rate"] - baseline_metrics["success_rate"]
    score_deltas = {}
    for metric in baseline_metrics["scores"]:
        score_deltas[metric] = current_metrics["scores"][metric] - baseline_metrics["scores"][metric]
    
    # Regression thresholds
    thresholds = {"critical": 0.1, "medium": 0.05, "low": 0.02}
    
    print(f"\n📊 Version Comparison:")
    print(f"   Baseline: {baseline_metrics['version']}")
    print(f"   Current: {current_metrics['version']}")
    print(f"   Success Rate: {baseline_metrics['success_rate']:.1%} → {current_metrics['success_rate']:.1%} ({success_delta:+.1%})")
    
    print(f"\n📈 Score Changes:")
    for metric, delta in score_deltas.items():
        status = "🔴" if delta < -thresholds["medium"] else "🟡" if delta < -thresholds["low"] else "🟢"
        print(f"   {metric}: {baseline_metrics['scores'][metric]:.3f} → {current_metrics['scores'][metric]:.3f} ({delta:+.3f}) {status}")
    
    # Overall regression assessment
    significant_regressions = sum(1 for delta in score_deltas.values() if delta < -thresholds["medium"])
    overall_regression = success_delta < -thresholds["low"] or significant_regressions > 0
    
    print(f"\n🚨 Regression Analysis:")
    print(f"   Overall Regression: {'Yes' if overall_regression else 'No'}")
    print(f"   Significant Regressions: {significant_regressions}")
    print(f"   Alert Level: {'HIGH' if overall_regression else 'LOW'}")
    
    print("\n✅ Regression detection automatically identifies performance degradations")

def demo_comprehensive_metrics():
    """Demonstrate comprehensive metrics calculation"""
    print("\n\n📊 Demo: Comprehensive Metrics")
    print("=" * 50)
    
    # Simulate comprehensive evaluation result
    task_result = {
        "task_id": "implement_auth_system",
        "success": True,
        "execution_time": 127.5,
        "pr_created": True,
        "pr_url": "https://github.com/test/repo/pull/42",
        "metrics": {
            "code_correctness": 0.85,
            "requirement_adherence": 0.90,
            "code_quality": 0.75,
            "safety_score": 0.95,
            "trajectory_score": 0.80,
            "context_efficiency": 0.70
        }
    }
    
    # Calculate overall score
    metric_scores = list(task_result["metrics"].values())
    overall_score = sum(metric_scores) / len(metric_scores)
    
    print(f"\n📋 Task: {task_result['task_id']}")
    print(f"   Status: {'✅ SUCCESS' if task_result['success'] else '❌ FAILED'}")
    print(f"   Execution Time: {task_result['execution_time']:.1f}s")
    print(f"   PR Created: {'Yes' if task_result['pr_created'] else 'No'}")
    print(f"   Overall Score: {overall_score:.3f}")
    
    print(f"\n📈 Detailed Metrics:")
    for metric, score in task_result["metrics"].items():
        bar_length = int(score * 20)
        bar = "█" * bar_length + "░" * (20 - bar_length)
        print(f"   {metric:<20}: {score:.3f} {bar}")
    
    # Score interpretation
    if overall_score >= 0.9:
        quality = "Excellent"
    elif overall_score >= 0.8:
        quality = "Good"
    elif overall_score >= 0.7:
        quality = "Acceptable"
    else:
        quality = "Needs Improvement"
    
    print(f"\n🏆 Quality Assessment: {quality}")
    print("\n✅ Six-dimensional metrics provide comprehensive evaluation")

def main():
    """Run all demos"""
    print("🚀 Enhanced Evaluation System Demo")
    print("=" * 60)
    print("Demonstrating advanced evaluation capabilities for autonomous coding agents")
    
    demos = [
        demo_tiered_datasets,
        demo_pass_at_k_evaluation,
        demo_trajectory_scoring,
        demo_context_efficiency,
        demo_regression_detection,
        demo_comprehensive_metrics
    ]
    
    for demo in demos:
        demo()
    
    print("\n\n🎉 Demo Complete!")
    print("=" * 60)
    print("The enhanced evaluation system provides:")
    print("✅ Multi-tier evaluation datasets for different use cases")
    print("✅ Pass@k reliability testing for consistency measurement")
    print("✅ Trajectory-based scoring with partial credit")
    print("✅ Context efficiency metrics for code understanding")
    print("✅ Automated regression detection between versions")
    print("✅ Comprehensive six-dimensional evaluation metrics")
    print("✅ Production-ready CLI interface and reporting")
    print("\n🚀 Ready for deployment in your autonomous coding agent!")

if __name__ == "__main__":
    main()