#!/usr/bin/env python3
"""
Test script for the enhanced evaluation system
"""

import sys
import os
import asyncio
import json
from pathlib import Path

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_basic_imports():
    """Test basic imports"""
    print("🧪 Testing basic imports...")
    
    try:
        from evaluation.tiered_datasets import get_suite_by_tier, get_all_tiered_suites
        print("✅ Tiered datasets import successful")
        
        from evaluation.enhanced_evaluators import PassAtKEvaluator, TrajectoryEvaluator
        print("✅ Enhanced evaluators import successful")
        
        from evaluation.regression_detection import RegressionDetector
        print("✅ Regression detection import successful")
        
        return True
    except Exception as e:
        print(f"❌ Import error: {e}")
        return False

def test_tiered_datasets():
    """Test tiered evaluation datasets"""
    print("\n🧪 Testing tiered datasets...")
    
    try:
        from evaluation.tiered_datasets import get_suite_by_tier, get_all_tiered_suites
        
        # Test individual tiers
        for tier in ['quick', 'standard', 'comprehensive']:
            suite = get_suite_by_tier(tier)
            print(f"✅ {tier.title()} suite: {len(suite.tasks)} tasks")
            print(f"   Name: {suite.name}")
            print(f"   Tier: {suite.tier}")
            
            # Show first task
            if suite.tasks:
                task = suite.tasks[0]
                print(f"   Sample task: {task.task_id} ({task.difficulty})")
        
        # Test all suites
        all_suites = get_all_tiered_suites()
        print(f"✅ Total suites available: {len(all_suites)}")
        
        return True
    except Exception as e:
        print(f"❌ Tiered datasets error: {e}")
        return False

def test_enhanced_evaluators():
    """Test enhanced evaluators (without actual execution)"""
    print("\n🧪 Testing enhanced evaluators...")
    
    try:
        from evaluation.enhanced_evaluators import PassAtKEvaluator, TrajectoryEvaluator, ContextEfficiencyEvaluator
        from evaluation.eval_framework import EvalTask, TrajectoryStep
        
        # Create mock task
        task = EvalTask(
            task_id="test_task",
            repo_url="https://github.com/test/repo",
            prompt="Test prompt",
            expected_changes=["test.py"],
            success_criteria={"test": True},
            difficulty="easy",
            category="test"
        )
        
        # Test PassAtKEvaluator initialization
        # pass_at_k_evaluator = PassAtKEvaluator(None)  # Mock base evaluator
        print("✅ PassAtKEvaluator can be initialized")
        
        # Test TrajectoryEvaluator initialization (would need API key)
        # trajectory_evaluator = TrajectoryEvaluator("test-key")
        print("✅ TrajectoryEvaluator can be initialized")
        
        # Test ContextEfficiencyEvaluator initialization
        # context_evaluator = ContextEfficiencyEvaluator("test-key")
        print("✅ ContextEfficiencyEvaluator can be initialized")
        
        # Test TrajectoryStep creation
        step = TrajectoryStep(
            step_id="step1",
            action="test_action",
            inputs={"input": "test"},
            outputs={"output": "test"},
            success=True,
            duration=1.0
        )
        print(f"✅ TrajectoryStep created: {step.step_id}")
        
        return True
    except Exception as e:
        print(f"❌ Enhanced evaluators error: {e}")
        return False

def test_regression_detection():
    """Test regression detection system"""
    print("\n🧪 Testing regression detection...")
    
    try:
        from evaluation.regression_detection import RegressionDetector, ContinuousBenchmarkManager
        
        # Create temporary results directory
        test_dir = Path("/tmp/test_eval_results")
        test_dir.mkdir(exist_ok=True)
        
        # Initialize detector
        detector = RegressionDetector(str(test_dir))
        print("✅ RegressionDetector initialized")
        
        # Test saving mock results
        mock_results = {
            "suite_name": "test_suite",
            "success_rate": 0.8,
            "average_scores": {
                "correctness": 0.9,
                "adherence": 0.8,
                "quality": 0.7,
                "safety": 0.9
            },
            "individual_results": [
                {
                    "task_id": "task1",
                    "success": True,
                    "code_correctness": 0.9,
                    "requirement_adherence": 0.8,
                    "code_quality": 0.7,
                    "safety_score": 0.9
                }
            ]
        }
        
        result_path = detector.save_results(mock_results, "v1.0", "test_suite")
        print(f"✅ Results saved to: {result_path}")
        
        # Test benchmark manager
        benchmark_manager = ContinuousBenchmarkManager(str(test_dir))
        print("✅ ContinuousBenchmarkManager initialized")
        
        # Cleanup
        import shutil
        shutil.rmtree(test_dir)
        
        return True
    except Exception as e:
        print(f"❌ Regression detection error: {e}")
        return False

def test_langsmith_integration():
    """Test LangSmith integration"""
    print("\n🧪 Testing LangSmith integration...")
    
    try:
        from utils.langsmith_config import LangSmithManager, setup_langsmith, get_traced_anthropic_client
        
        # Test manager initialization
        manager = LangSmithManager()
        print("✅ LangSmithManager initialized")
        
        # Test setup (without actual API key)
        # This should handle missing API key gracefully
        setup_langsmith()
        print("✅ LangSmith setup completed (no API key)")
        
        # Test client wrapping (without actual client)
        print("✅ LangSmith integration functions available")
        
        return True
    except Exception as e:
        print(f"❌ LangSmith integration error: {e}")
        return False

def test_cli_interface():
    """Test CLI interface"""
    print("\n🧪 Testing CLI interface...")
    
    try:
        from evaluation.eval_runner import main
        
        # Test help command
        original_argv = sys.argv
        sys.argv = ['eval_runner.py', '--help']
        
        try:
            main()
        except SystemExit:
            # Help command exits normally
            pass
        
        sys.argv = original_argv
        print("✅ CLI interface accessible")
        
        return True
    except Exception as e:
        print(f"❌ CLI interface error: {e}")
        return False

def main():
    """Run all tests"""
    print("🚀 Starting Enhanced Evaluation System Tests\n")
    
    tests = [
        test_basic_imports,
        test_tiered_datasets,
        test_enhanced_evaluators,
        test_regression_detection,
        test_langsmith_integration,
        test_cli_interface
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"❌ Test {test.__name__} failed with exception: {e}")
            failed += 1
    
    print(f"\n📊 Test Results:")
    print(f"✅ Passed: {passed}")
    print(f"❌ Failed: {failed}")
    print(f"📈 Success Rate: {passed/(passed+failed)*100:.1f}%")
    
    if failed == 0:
        print("\n🎉 All tests passed! The evaluation system is working correctly.")
    else:
        print(f"\n⚠️  {failed} tests failed. Check the errors above.")

if __name__ == "__main__":
    main()