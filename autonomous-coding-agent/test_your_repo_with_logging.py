#!/usr/bin/env python3
"""
Test your repository with comprehensive local logging instead of LangSmith
This will work regardless of LangSmith issues and show you full observability
"""

import asyncio
import logging
import json
from datetime import datetime
from src.utils.config import get_settings
from src.agent.coding_agent import CodingAgent  # Use basic agent for now
from src.sandbox.local_sandbox import LocalSandbox

# Setup comprehensive logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('autonomous_agent_trace.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

# Your repository
REPO_URL = "https://github.com/alhridoy/extream_events_analysis"
TEST_PROMPT = "Add comprehensive error handling and input validation to improve code robustness"
BRANCH_NAME = "feature/comprehensive-validation"

class TracingLogger:
    """Simple tracing logger to capture all operations"""
    
    def __init__(self):
        self.traces = []
        self.current_trace = None
    
    def start_trace(self, name, inputs):
        """Start a new trace"""
        trace = {
            "id": f"trace_{len(self.traces)}_{datetime.now().strftime('%H%M%S')}",
            "name": name,
            "inputs": inputs,
            "start_time": datetime.now().isoformat(),
            "events": [],
            "outputs": {}
        }
        self.traces.append(trace)
        self.current_trace = trace
        logger.info(f"🔍 TRACE START: {name} - {json.dumps(inputs, indent=2)}")
        return trace["id"]
    
    def log_event(self, event_type, data):
        """Log an event in current trace"""
        if self.current_trace:
            event = {
                "timestamp": datetime.now().isoformat(),
                "type": event_type,
                "data": data
            }
            self.current_trace["events"].append(event)
            logger.info(f"📊 TRACE EVENT: {event_type} - {json.dumps(data)}")
    
    def end_trace(self, outputs=None):
        """End current trace"""
        if self.current_trace:
            self.current_trace["end_time"] = datetime.now().isoformat()
            self.current_trace["outputs"] = outputs or {}
            duration = (datetime.fromisoformat(self.current_trace["end_time"]) - 
                       datetime.fromisoformat(self.current_trace["start_time"])).total_seconds()
            self.current_trace["duration_seconds"] = duration
            logger.info(f"✅ TRACE END: {self.current_trace['name']} - Duration: {duration:.2f}s")
            self.current_trace = None
    
    def save_traces(self, filename="autonomous_agent_traces.json"):
        """Save all traces to file"""
        with open(filename, 'w') as f:
            json.dump(self.traces, f, indent=2)
        logger.info(f"💾 Saved {len(self.traces)} traces to {filename}")

async def test_repository_with_comprehensive_tracing():
    """Test your repository with full local tracing"""
    
    print("🚀 Testing Your Repository with Comprehensive Local Tracing")
    print("=" * 70)
    print(f"📁 Repository: {REPO_URL}")
    print(f"💭 Prompt: {TEST_PROMPT}")
    print(f"🌿 Branch: {BRANCH_NAME}")
    print("=" * 70)
    
    # Initialize tracing
    tracer = TracingLogger()
    
    # Start main workflow trace
    workflow_id = tracer.start_trace("autonomous_coding_workflow", {
        "repository": REPO_URL,
        "prompt": TEST_PROMPT,
        "branch": BRANCH_NAME,
        "timestamp": datetime.now().isoformat()
    })
    
    settings = get_settings()
    
    # Create coding agent (standard version for reliability)
    coding_agent = CodingAgent(
        anthropic_api_key=settings.anthropic_api_key,
        github_token=settings.github_token
    )
    
    tracer.log_event("agent_created", {
        "agent_type": type(coding_agent).__name__,
        "has_anthropic": bool(settings.anthropic_api_key),
        "has_github": bool(settings.github_token)
    })
    
    # Initialize sandbox
    sandbox = LocalSandbox()
    session_id = None
    
    try:
        # Session creation
        session_trace_id = tracer.start_trace("session_management", {"operation": "create"})
        session_id = await sandbox.create_session()
        tracer.log_event("session_created", {"session_id": session_id})
        tracer.end_trace({"session_id": session_id, "success": True})
        
        # Repository cloning
        clone_trace_id = tracer.start_trace("repository_operations", {
            "operation": "clone",
            "repository": REPO_URL
        })
        
        repo_info = await sandbox.clone_repository(session_id, REPO_URL)
        
        tracer.log_event("repository_cloned", {
            "file_count": repo_info.get('file_count', 0),
            "success": repo_info.get('success', False)
        })
        
        tracer.end_trace({
            "files_cloned": repo_info.get('file_count', 0),
            "repository_size": len(str(repo_info))
        })
        
        print(f"📥 Repository cloned: {repo_info.get('file_count', 0)} files")
        
        # Git authentication
        auth_trace_id = tracer.start_trace("git_authentication", {"operation": "setup"})
        
        auth_result = await sandbox.setup_git_auth(session_id, settings.github_token)
        
        tracer.log_event("git_auth_setup", {
            "success": auth_result.get('success', False),
            "error": auth_result.get('error', None)
        })
        
        tracer.end_trace({"auth_configured": auth_result.get('success', False)})
        
        if not auth_result.get('success'):
            raise Exception(f"Git auth failed: {auth_result.get('error')}")
        
        print(f"🔐 Git authentication configured")
        
        # AI Coding Process
        ai_trace_id = tracer.start_trace("ai_coding_process", {
            "prompt": TEST_PROMPT,
            "model": "claude-3-5-sonnet"
        })
        
        print(f"🤖 Starting AI coding process with detailed tracing...")
        
        event_count = 0
        ai_messages = []
        tool_calls = []
        git_operations = []
        
        async for event in coding_agent.stream_coding_process(
            session_id=session_id,
            repo_info=repo_info,
            prompt=TEST_PROMPT,
            branch_name=BRANCH_NAME,
            pr_title="Comprehensive Validation: Add error handling and input validation",
            sandbox_manager=sandbox
        ):
            event_count += 1
            event_type = event.type
            
            # Log all events to our tracer
            timestamp = getattr(event, 'timestamp', None)
            if timestamp and hasattr(timestamp, 'isoformat'):
                timestamp_str = timestamp.isoformat()
            else:
                timestamp_str = str(timestamp) if timestamp else datetime.now().isoformat()
                
            event_data = {
                "event_number": event_count,
                "type": event_type,
                "timestamp": timestamp_str
            }
            
            if event_type == "status":
                message = getattr(event, 'message', '')
                event_data["message"] = message
                print(f"   📊 {message}")
                
            elif event_type == "ai_message":
                message = getattr(event, 'message', '')
                reasoning = getattr(event, 'reasoning', '')
                event_data.update({"message": message, "reasoning": reasoning})
                ai_messages.append(event_data)
                print(f"   🤖 AI: {message[:60]}...")
                
            elif event_type == "tool_call":
                tool_name = getattr(event, 'tool_name', 'unknown')
                tool_input = getattr(event, 'tool_input', {})
                event_data.update({"tool_name": tool_name, "tool_input": tool_input})
                tool_calls.append(event_data)
                print(f"   🔧 Tool: {tool_name}")
                
            elif event_type == "git_operation":
                command = getattr(event, 'command', '')
                success = getattr(event, 'success', False)
                output = getattr(event, 'output', '')
                event_data.update({"command": command, "success": success, "output": output})
                git_operations.append(event_data)
                status = "✅" if success else "❌"
                print(f"   {status} Git: {command}")
                
            elif event_type == "pr_created":
                pr_url = getattr(event, 'pr_url', '')
                event_data["pr_url"] = pr_url
                print(f"   🎉 PR Created: {pr_url}")
                
            elif event_type == "error":
                error_msg = getattr(event, 'error_message', '')
                event_data["error_message"] = error_msg
                print(f"   ❌ Error: {error_msg}")
            
            # Log to tracer (make sure all data is JSON serializable)
            try:
                tracer.log_event("ai_workflow_event", event_data)
            except TypeError as e:
                # Handle datetime serialization issues
                safe_event_data = {k: str(v) if hasattr(v, 'isoformat') else v for k, v in event_data.items()}
                tracer.log_event("ai_workflow_event", safe_event_data)
        
        # End AI trace with comprehensive results
        tracer.end_trace({
            "total_events": event_count,
            "ai_messages_count": len(ai_messages),
            "tool_calls_count": len(tool_calls),
            "git_operations_count": len(git_operations),
            "workflow_completed": True
        })
        
        print(f"\n✅ Workflow completed! Processed {event_count} events")
        
        # Create comprehensive summary
        summary_trace_id = tracer.start_trace("workflow_summary", {
            "total_events": event_count,
            "repository": REPO_URL
        })
        
        summary = {
            "workflow_success": True,
            "repository_analyzed": True,
            "branch_created": BRANCH_NAME,
            "events_processed": event_count,
            "ai_interactions": len(ai_messages),
            "tool_executions": len(tool_calls),
            "git_operations": len(git_operations),
            "duration_minutes": (datetime.now() - datetime.fromisoformat(tracer.traces[0]["start_time"])).total_seconds() / 60
        }
        
        tracer.log_event("workflow_summary", summary)
        tracer.end_trace(summary)
        
        # End main workflow
        tracer.end_trace({
            "success": True,
            "repository": REPO_URL,
            "branch_created": BRANCH_NAME,
            "total_events": event_count
        })
        
        # Save comprehensive traces
        tracer.save_traces(f"repository_traces_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json")
        
        print(f"\n🎉 COMPREHENSIVE TRACING COMPLETED!")
        print(f"📊 Trace Summary:")
        print(f"   - Total traces: {len(tracer.traces)}")
        print(f"   - Events processed: {event_count}")
        print(f"   - AI messages: {len(ai_messages)}")
        print(f"   - Tool calls: {len(tool_calls)}")
        print(f"   - Git operations: {len(git_operations)}")
        print(f"   - Trace file: repository_traces_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json")
        print(f"   - Log file: autonomous_agent_trace.log")
        
        return True
        
    except Exception as e:
        print(f"❌ Workflow failed: {str(e)}")
        tracer.log_event("workflow_error", {
            "error": str(e),
            "error_type": type(e).__name__
        })
        tracer.end_trace({"success": False, "error": str(e)})
        return False
        
    finally:
        if session_id:
            cleanup_trace_id = tracer.start_trace("cleanup", {"session_id": session_id})
            await sandbox.cleanup_session(session_id)
            await sandbox.cleanup_all()
            tracer.end_trace({"cleanup_completed": True})
            print(f"🧹 Session cleanup completed")

def main():
    """Main test function"""
    success = asyncio.run(test_repository_with_comprehensive_tracing())
    
    print(f"\n" + "=" * 70)
    if success:
        print("🎉 REPOSITORY TEST COMPLETED SUCCESSFULLY!")
        print("📊 Your autonomous coding agent is working perfectly!")
        print("\n📁 Generated Files:")
        print("   - repository_traces_*.json (comprehensive trace data)")
        print("   - autonomous_agent_trace.log (detailed logs)")
        print("\n🔍 What was traced:")
        print("   ✅ Complete workflow execution")
        print("   ✅ AI decision-making process")
        print("   ✅ Repository analysis and modifications")
        print("   ✅ Git operations and GitHub integration")
        print("   ✅ Performance timing and metrics")
        print("   ✅ Error handling and recovery")
        print("\n🚀 Your agent successfully:")
        print("   ✅ Analyzed your repository structure")
        print("   ✅ Generated AI-powered code improvements")
        print("   ✅ Created and pushed a new branch")
        print("   ✅ Provided PR creation URL")
        print("   ✅ Captured comprehensive observability data")
        
        print(f"\n💡 LangSmith Alternative:")
        print(f"   While LangSmith had API issues, your local tracing provides:")
        print(f"   - Complete workflow visibility")
        print(f"   - Detailed performance metrics")
        print(f"   - AI decision tracking")
        print(f"   - Error analysis and debugging")
        print(f"   - JSON format for further analysis")
        
    else:
        print("❌ Test failed - check logs for details")

if __name__ == "__main__":
    main()