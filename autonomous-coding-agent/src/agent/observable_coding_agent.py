"""
Enhanced Coding Agent with full observability integration
"""

import asyncio
import logging
from typing import AsyncGenerator, Dict, Any, Optional, List
from datetime import datetime

from ..api.models import (
    StreamEvent, StatusEvent, ToolCallEvent, AIMessageEvent,
    GitOperationEvent, PRCreatedEvent, ErrorEvent
)
from ..utils.config import get_settings
from ..utils.observability_strategy import ObservabilityManager
from .code_analyzer import CodeAnalyzer, RepositoryAnalysis
from .code_modifier import CodeModifier, ModificationPlan
from .coding_agent import CodingAgent

logger = logging.getLogger(__name__)


class ObservableCodingAgent(CodingAgent):
    """Coding Agent with comprehensive observability and monitoring"""
    
    def __init__(self, anthropic_api_key: str, github_token: str, langsmith_api_key: str = None):
        # Initialize the base coding agent
        super().__init__(anthropic_api_key, github_token)
        
        # Add observability manager
        self.observability = ObservabilityManager(langsmith_api_key=langsmith_api_key)
        
        logger.info("🔍 ObservableCodingAgent initialized with full monitoring")
    
    async def stream_coding_process(
        self,
        session_id: str,
        repo_info: Dict[str, Any],
        prompt: str,
        branch_name: str = None,
        pr_title: str = None,
        sandbox_manager = None
    ) -> AsyncGenerator[StreamEvent, None]:
        """Main coding process with comprehensive observability"""
        
        # Start comprehensive observability tracking
        async with self.observability.observe_coding_process(session_id, prompt) as obs:
            otel_span = obs["otel_span"]
            langsmith_active = obs.get("langsmith_active", False)
            
            try:
                # Track repository metadata
                if otel_span:
                    self.observability.track_system_metrics(otel_span, {
                        "repo_files": repo_info.get("file_count", 0),
                        "repo_size": len(str(repo_info)),
                        "session_id": session_id,
                        "prompt_length": len(prompt)
                    })
                
                # Initialize analyzers and modifiers
                analyzer = CodeAnalyzer()
                modifier = CodeModifier()
                
                # Generate branch name if not provided
                if not branch_name:
                    branch_name = f"feature/{self._generate_branch_name(prompt)}"
                
                # Generate PR title if not provided
                if not pr_title:
                    pr_title = f"Implement: {prompt[:50]}..."
                
                # Step 1: Comprehensive repository analysis with AI observability
                yield StatusEvent(message="Performing deep repository analysis...")
                logger.info("🔍 AI ANALYSIS: Starting deep repository analysis...")
                
                async with self.observability.observe_ai_call(
                    "repository_analyzer",
                    f"Analyze repository structure for: {prompt}",
                    {"repo_info": repo_info, "file_count": repo_info.get("file_count", 0)}
                ) as analysis_obs:
                    
                    # Emit Tool: Read events for key files being analyzed
                    key_files = ["app.py", "main.py", "requirements.txt", "package.json", "README.md"]
                    for file in key_files:
                        file_result = await sandbox_manager.read_file(session_id, file)
                        if file_result.get('success'):
                            yield ToolCallEvent(
                                tool_name="read_file",
                                tool_input={"file": file},
                                tool_output=f"Read {len(file_result.get('content', ''))} characters"
                            )
                    
                    repo_analysis = await analyzer.analyze_repository(session_id, sandbox_manager)
                    
                    # Track analysis results in observability
                    if analysis_obs.get("langsmith_active"):
                        self.observability.create_langsmith_trace(
                            "repository_analysis_complete",
                            {"total_files": repo_analysis.total_files, "languages": len(repo_analysis.languages)},
                            {
                                "analysis_completed": True,
                                "files_analyzed": repo_analysis.total_files,
                                "languages_detected": list(repo_analysis.languages.keys()),
                                "code_quality_score": repo_analysis.code_quality_score,
                                "confidence": 0.95
                            }
                        )
                
                logger.info(f"🧠 AI THINKING: Found {repo_analysis.total_files} files across {len(repo_analysis.languages)} languages")
                logger.info(f"🎯 AI REASONING: Architecture patterns: {', '.join(repo_analysis.architecture_patterns)}")
                logger.info(f"📊 AI ASSESSMENT: Code quality score: {repo_analysis.code_quality_score:.1f}/100")
                
                # AI reasoning message matching Backspace spec format
                languages_str = ', '.join([f"/{k}" for k in repo_analysis.languages.keys()])
                endpoints_found = "POST endpoints" if "API" in repo_analysis.architecture_patterns else "main files"
                
                yield AIMessageEvent(
                    message=f"Found {len(repo_analysis.languages)} file types: {languages_str}. Analyzing {endpoints_found} for implementation opportunities.",
                    reasoning=f"Architecture patterns detected: {', '.join(repo_analysis.architecture_patterns)}. Code quality score: {repo_analysis.code_quality_score:.1f}/100"
                )
                
                # Step 2: AI-powered implementation planning with observability
                yield StatusEvent(message="Creating intelligent implementation plan...")
                
                async with self.observability.observe_ai_call(
                    "claude-3-5-sonnet",
                    f"Create implementation plan: {prompt}",
                    {"repo_analysis": repo_analysis.__dict__, "complexity": repo_analysis.code_quality_score}
                ) as planning_obs:
                    
                    modification_plan = await modifier.create_modification_plan(
                        prompt, repo_analysis, session_id, sandbox_manager
                    )
                    
                    # Track planning results
                    if planning_obs.get("langsmith_active"):
                        self.observability.create_langsmith_trace(
                            "implementation_plan_complete",
                            {"changes_planned": len(modification_plan.changes), "risk_level": modification_plan.risk_level},
                            {
                                "planning_completed": True,
                                "changes_count": len(modification_plan.changes),
                                "risk_level": modification_plan.risk_level,
                                "estimated_time": getattr(modification_plan, 'estimated_time', 'unknown'),
                                "confidence": 0.90
                            }
                        )
                
                yield AIMessageEvent(
                    message=f"Implementation plan created with {len(modification_plan.changes)} changes",
                    reasoning=f"Risk level: {modification_plan.risk_level}, Estimated time: {modification_plan.estimated_time} minutes"
                )
                
                # Step 3: Execute the implementation plan with monitoring
                yield StatusEvent(message="Implementing changes with AI guidance...")
                
                # Track implementation performance
                implementation_start = asyncio.get_event_loop().time()
                
                results = await modifier.apply_modification_plan(
                    modification_plan, session_id, sandbox_manager
                )
                
                implementation_time = asyncio.get_event_loop().time() - implementation_start
                
                # Track implementation metrics
                if otel_span:
                    self.observability.track_system_metrics(otel_span, {
                        "implementation_time": implementation_time,
                        "changes_planned": len(modification_plan.changes),
                        "changes_successful": sum(1 for r in results if r['success']),
                        "risk_level": modification_plan.risk_level
                    })
                
                # Stream implementation results
                successful_changes = 0
                for result in results:
                    change = result['change']
                    success = result['success']
                    
                    # Emit detailed Tool: Edit event if available
                    if success and 'tool_event' in result:
                        tool_event = result['tool_event']
                        yield ToolCallEvent(
                            tool_name=tool_event['tool_name'],
                            tool_input=tool_event['tool_input'],
                            tool_output="File modified successfully"
                        )
                    else:
                        yield ToolCallEvent(
                            tool_name="apply_change",
                            tool_input={"file": change.file_path, "type": change.change_type},
                            tool_output=f"{'✅ Success' if success else '❌ Failed'}: {result.get('details', '')}"
                        )
                    
                    if success:
                        successful_changes += 1
                
                yield AIMessageEvent(
                    message=f"Applied {successful_changes}/{len(results)} changes successfully",
                    reasoning=f"Implementation {'completed' if successful_changes == len(results) else 'partially completed'}"
                )
                
                # Step 4: Git operations with observability
                yield StatusEvent(message="Setting up git configuration...")
                
                git_start = asyncio.get_event_loop().time()
                
                # Setup git authentication for pushing
                auth_result = await sandbox_manager.setup_git_auth(session_id, self.github_token)
                if not auth_result.get('success', False):
                    raise Exception(f"Failed to setup git auth: {auth_result.get('error', 'Unknown error')}")
                
                # Create branch
                yield StatusEvent(message="Creating git branch...")
                
                branch_result = await sandbox_manager.execute_command(
                    session_id, f"git checkout -b {branch_name}"
                )
                
                yield GitOperationEvent(
                    command=f"git checkout -b {branch_name}",
                    output=branch_result["stdout"],
                    success=branch_result["success"]
                )
                
                if not branch_result["success"]:
                    raise Exception(f"Failed to create branch: {branch_result['stderr']}")
                
                # Commit changes with detailed message
                yield StatusEvent(message="Committing changes...")
                
                add_result = await sandbox_manager.execute_command(session_id, "git add .")
                yield GitOperationEvent(
                    command="git add .",
                    output=add_result["stdout"],
                    success=add_result["success"]
                )
                
                commit_message = self._create_detailed_commit_message(prompt, modification_plan, results)
                
                commit_result = await sandbox_manager.execute_command(
                    session_id, f'git commit -m "{commit_message}"'
                )
                yield GitOperationEvent(
                    command=f'git commit -m "{commit_message[:50]}..."',
                    output=commit_result["stdout"],
                    success=commit_result["success"]
                )
                
                # Push branch to GitHub
                yield StatusEvent(message="Pushing branch to GitHub...")
                push_result = await sandbox_manager.push_branch(session_id, branch_name)
                
                yield GitOperationEvent(
                    command=f"git push origin {branch_name}",
                    output=push_result.get('stdout', ''),
                    success=push_result.get('success', False)
                )
                
                if not push_result.get('success', False):
                    raise Exception(f"Failed to push branch: {push_result.get('stderr', 'Unknown error')}")
                
                git_time = asyncio.get_event_loop().time() - git_start
                
                # Track git performance
                if otel_span:
                    self.observability.track_system_metrics(otel_span, {
                        "git_operations_time": git_time,
                        "branch_created": True,
                        "changes_committed": successful_changes
                    })
                
                # Step 5: Create comprehensive PR
                yield StatusEvent(message="Creating detailed pull request...")
                
                pr_body = self._create_detailed_pr_body(prompt, repo_analysis, modification_plan, results)
                
                # Create PR using GitHub CLI or get manual URL
                pr_result = await sandbox_manager.create_pull_request(
                    session_id, pr_title, pr_body, branch_name
                )
                
                if pr_result.get('success'):
                    pr_url = pr_result.get('pr_url', pr_result.get('stdout', '')).strip()
                    method = pr_result.get('method', 'unknown')
                    
                    # Log the PR creation method for debugging
                    logger.info(f"PR creation method: {method}, URL: {pr_url}")
                    
                    # Track PR creation in observability
                    if otel_span:
                        self.observability.track_system_metrics(otel_span, {
                            "pr_created": True,
                            "pr_method": method,
                            "total_process_time": asyncio.get_event_loop().time() - implementation_start
                        })
                    
                    yield PRCreatedEvent(
                        pr_url=pr_url,
                        pr_number=self._extract_pr_number(pr_url),
                        title=pr_title,
                        body=pr_body
                    )
                else:
                    # PR creation failed, but provide fallback info
                    error_msg = pr_result.get('error', 'Unknown error during PR creation')
                    logger.error(f"PR creation failed: {error_msg}")
                    
                    yield ErrorEvent(
                        error_type="PRCreationError",
                        error_message=f"Failed to create PR: {error_msg}"
                    )
                
                # Log successful completion
                self.observability.create_custom_event("coding_process_completed", {
                    "session_id": session_id,
                    "successful_changes": successful_changes,
                    "total_changes": len(results),
                    "implementation_time": implementation_time,
                    "git_time": git_time,
                    "pr_created": pr_result.get('success', False)
                })
                
            except Exception as e:
                logger.error(f"Error in observable coding process: {str(e)}", exc_info=True)
                
                # Track error in observability systems
                if otel_span:
                    otel_span.record_exception(e)
                    self.observability.track_system_metrics(otel_span, {
                        "error_occurred": True,
                        "error_type": type(e).__name__
                    })
                
                yield ErrorEvent(
                    error_type=type(e).__name__,
                    error_message=str(e)
                )
                
                raise


# Factory function to create the appropriate agent
def create_coding_agent(anthropic_api_key: str, github_token: str, langsmith_api_key: str = None):
    """Create a coding agent with optional observability"""
    
    if langsmith_api_key:
        logger.info("🔍 Creating ObservableCodingAgent with full monitoring")
        return ObservableCodingAgent(
            anthropic_api_key=anthropic_api_key,
            github_token=github_token,
            langsmith_api_key=langsmith_api_key
        )
    else:
        logger.info("🤖 Creating standard CodingAgent")
        return CodingAgent(
            anthropic_api_key=anthropic_api_key,
            github_token=github_token
        )