{% extends "base.html" %}

{% block title %}Interactive Demo - Autonomous Coding Agent{% endblock %}

{% block content %}
<div class="space-y-8">
    <!-- Header -->
    <div class="text-center">
        <h1 class="text-3xl font-bold text-gray-900 mb-2">
            <i class="fas fa-play-circle text-primary mr-3"></i>
            Interactive Demo
        </h1>
        <p class="text-lg text-gray-600">
            Experience the power of autonomous coding in real-time
        </p>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <!-- Input Form -->
        <div class="bg-white rounded-lg shadow-lg border border-gray-200 p-6">
            <h2 class="text-xl font-semibold text-gray-900 mb-6">
                <i class="fas fa-cog mr-2 text-primary"></i>
                Configuration
            </h2>
            
            <form id="coding-form" class="space-y-6">
                <div>
                    <label for="repo-url" class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fab fa-github mr-1"></i>
                        Repository URL
                    </label>
                    <input type="url" 
                           id="repo-url" 
                           name="repo_url"
                           placeholder="https://github.com/username/repository"
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                           required>
                    <p class="text-sm text-gray-500 mt-1">
                        Enter a public GitHub repository URL
                    </p>
                </div>

                <div>
                    <label for="prompt" class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-comment-alt mr-1"></i>
                        Coding Prompt
                    </label>
                    <textarea id="prompt" 
                              name="prompt"
                              rows="4"
                              placeholder="Describe what you want to implement (e.g., 'Add a health check endpoint that returns API version and status')"
                              class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                              required></textarea>
                    <p class="text-sm text-gray-500 mt-1">
                        Be specific about what you want to implement
                    </p>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label for="branch-name" class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fas fa-code-branch mr-1"></i>
                            Branch Name (Optional)
                        </label>
                        <input type="text" 
                               id="branch-name" 
                               name="branch_name"
                               placeholder="feature/my-feature"
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
                    </div>

                    <div>
                        <label for="pr-title" class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fas fa-tag mr-1"></i>
                            PR Title (Optional)
                        </label>
                        <input type="text" 
                               id="pr-title" 
                               name="pr_title"
                               placeholder="Implement new feature"
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
                    </div>
                </div>

                <div class="flex space-x-4">
                    <button type="submit" 
                            id="start-btn"
                            class="flex-1 bg-primary hover:bg-blue-600 text-white font-semibold py-3 px-6 rounded-lg transition-colors">
                        <i class="fas fa-rocket mr-2"></i>
                        Start Coding Process
                    </button>
                    
                    <button type="button" 
                            id="stop-btn"
                            class="bg-red-600 hover:bg-red-700 text-white font-semibold py-3 px-6 rounded-lg transition-colors hidden">
                        <i class="fas fa-stop mr-2"></i>
                        Stop
                    </button>
                </div>
            </form>

            <!-- Quick Examples -->
            <div class="mt-8 pt-6 border-t border-gray-200">
                <h3 class="text-sm font-medium text-gray-700 mb-3">
                    <i class="fas fa-lightbulb mr-1 text-yellow-500"></i>
                    Quick Examples
                </h3>
                <div class="space-y-2">
                    <button type="button" 
                            class="example-btn w-full text-left p-3 bg-gray-50 hover:bg-gray-100 rounded-lg text-sm transition-colors"
                            data-repo="https://github.com/example/fastapi-demo"
                            data-prompt="Add a health check endpoint that returns API version and status">
                        <i class="fas fa-heartbeat mr-2 text-green-600"></i>
                        Add Health Check Endpoint
                    </button>
                    
                    <button type="button" 
                            class="example-btn w-full text-left p-3 bg-gray-50 hover:bg-gray-100 rounded-lg text-sm transition-colors"
                            data-repo="https://github.com/example/user-api"
                            data-prompt="Add input validation to all POST endpoints using Pydantic models">
                        <i class="fas fa-shield-alt mr-2 text-blue-600"></i>
                        Add Input Validation
                    </button>
                    
                    <button type="button" 
                            class="example-btn w-full text-left p-3 bg-gray-50 hover:bg-gray-100 rounded-lg text-sm transition-colors"
                            data-repo="https://github.com/example/web-service"
                            data-prompt="Implement rate limiting middleware (100 requests per minute per IP)">
                        <i class="fas fa-tachometer-alt mr-2 text-purple-600"></i>
                        Add Rate Limiting
                    </button>
                </div>
            </div>
        </div>

        <!-- Output Console -->
        <div class="bg-gray-900 rounded-lg shadow-lg border border-gray-700 p-6 h-fit">
            <div class="flex items-center justify-between mb-4">
                <h2 class="text-xl font-semibold text-white">
                    <i class="fas fa-terminal mr-2 text-green-400"></i>
                    Live Console
                </h2>
                <div class="flex items-center space-x-2">
                    <div id="status-indicator" class="w-3 h-3 bg-gray-500 rounded-full"></div>
                    <span id="status-text" class="text-sm text-gray-400">Idle</span>
                </div>
            </div>
            
            <div id="console" class="bg-black rounded-lg p-4 h-96 overflow-y-auto font-mono text-sm">
                <div class="text-green-400 mb-2">
                    <i class="fas fa-robot mr-2"></i>
                    Autonomous Coding Agent Console v1.0.0
                </div>
                <div class="text-gray-400 mb-4">
                    Ready to process your coding requests...
                </div>
                <div id="console-content" class="space-y-1">
                    <!-- Dynamic content will be added here -->
                </div>
                <div id="console-cursor" class="inline-block w-2 h-4 bg-green-400 animate-pulse"></div>
            </div>

            <!-- Progress Bar -->
            <div class="mt-4 hidden" id="progress-container">
                <div class="flex items-center justify-between text-sm text-gray-400 mb-2">
                    <span>Progress</span>
                    <span id="progress-text">0%</span>
                </div>
                <div class="w-full bg-gray-700 rounded-full h-2">
                    <div id="progress-bar" class="bg-primary h-2 rounded-full transition-all duration-300" style="width: 0%"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Results Section -->
    <div id="results-section" class="hidden">
        <div class="bg-white rounded-lg shadow-lg border border-gray-200 p-6">
            <h2 class="text-xl font-semibold text-gray-900 mb-6">
                <i class="fas fa-check-circle mr-2 text-green-600"></i>
                Results
            </h2>
            
            <div id="results-content" class="space-y-4">
                <!-- Results will be populated here -->
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="/static/js/demo.js"></script>
{% endblock %}
