{% extends "base.html" %}

{% block title %}Evaluation Dashboard - Autonomous Coding Agent{% endblock %}

{% block content %}
<div class="space-y-8">
    <!-- Header -->
    <div class="text-center">
        <h1 class="text-3xl font-bold text-gray-900 mb-2">
            <i class="fas fa-chart-line text-primary mr-3"></i>
            Evaluation Dashboard
        </h1>
        <p class="text-lg text-gray-600">
            Comprehensive evaluation metrics for the autonomous coding agent
        </p>
    </div>

    <!-- Quick Stats -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div class="bg-white rounded-lg shadow-lg p-6 border border-gray-200">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                    <i class="fas fa-check-circle text-green-600 text-xl"></i>
                </div>
                <div class="ml-4">
                    <div class="text-2xl font-bold text-gray-900" id="success-rate">87%</div>
                    <div class="text-sm text-gray-600">Success Rate</div>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-lg p-6 border border-gray-200">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                    <i class="fas fa-code text-blue-600 text-xl"></i>
                </div>
                <div class="ml-4">
                    <div class="text-2xl font-bold text-gray-900" id="code-quality">8.4</div>
                    <div class="text-sm text-gray-600">Avg Quality Score</div>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-lg p-6 border border-gray-200">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                    <i class="fas fa-clock text-purple-600 text-xl"></i>
                </div>
                <div class="ml-4">
                    <div class="text-2xl font-bold text-gray-900" id="avg-time">45s</div>
                    <div class="text-sm text-gray-600">Avg Execution</div>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-lg p-6 border border-gray-200">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
                    <i class="fas fa-shield-alt text-yellow-600 text-xl"></i>
                </div>
                <div class="ml-4">
                    <div class="text-2xl font-bold text-gray-900" id="safety-score">9.2</div>
                    <div class="text-sm text-gray-600">Safety Score</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Evaluation Controls -->
    <div class="bg-white rounded-lg shadow-lg border border-gray-200 p-6">
        <h2 class="text-xl font-semibold text-gray-900 mb-6">
            <i class="fas fa-play mr-2 text-primary"></i>
            Run Evaluations
        </h2>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="border border-gray-200 rounded-lg p-4">
                <h3 class="text-lg font-medium text-gray-900 mb-2">Basic Evaluation Suite</h3>
                <p class="text-sm text-gray-600 mb-4">
                    Fundamental tests for autonomous coding capabilities including API endpoints, validation, and basic features.
                </p>
                <div class="flex items-center justify-between">
                    <div class="text-sm text-gray-500">
                        <i class="fas fa-tasks mr-1"></i>
                        3 tasks • ~5 min
                    </div>
                    <button class="eval-btn bg-primary hover:bg-blue-600 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors"
                            data-suite="basic">
                        <i class="fas fa-play mr-1"></i>
                        Run Basic
                    </button>
                </div>
            </div>

            <div class="border border-gray-200 rounded-lg p-4">
                <h3 class="text-lg font-medium text-gray-900 mb-2">SWE-Bench Style Suite</h3>
                <p class="text-sm text-gray-600 mb-4">
                    Real-world software engineering tasks including bug fixes, feature implementation, and refactoring.
                </p>
                <div class="flex items-center justify-between">
                    <div class="text-sm text-gray-500">
                        <i class="fas fa-tasks mr-1"></i>
                        3 tasks • ~15 min
                    </div>
                    <button class="eval-btn bg-secondary hover:bg-purple-600 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors"
                            data-suite="swe-bench">
                        <i class="fas fa-play mr-1"></i>
                        Run SWE-Bench
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Results Section -->
    <div id="results-section" class="hidden">
        <div class="bg-white rounded-lg shadow-lg border border-gray-200">
            <div class="p-6 border-b border-gray-200">
                <h2 class="text-xl font-semibold text-gray-900">
                    <i class="fas fa-chart-bar mr-2 text-primary"></i>
                    Evaluation Results
                </h2>
            </div>
            <div class="p-6">
                <div id="results-content">
                    <!-- Results will be populated here -->
                </div>
            </div>
        </div>
    </div>

    <!-- Historical Results -->
    <div class="bg-white rounded-lg shadow-lg border border-gray-200">
        <div class="p-6 border-b border-gray-200">
            <h2 class="text-xl font-semibold text-gray-900">
                <i class="fas fa-history mr-2 text-primary"></i>
                Recent Evaluations
            </h2>
        </div>
        <div class="p-6">
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Suite
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Success Rate
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Avg Score
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Execution Time
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Date
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Actions
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200" id="eval-history">
                        <!-- Sample data -->
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                Basic Suite
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                    100%
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                8.7/10
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                42s
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                2024-01-15 14:30
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <a href="#" class="text-primary hover:underline">View Details</a>
                            </td>
                        </tr>
                        
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                SWE-Bench Style
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                    67%
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                7.2/10
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                156s
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                2024-01-15 13:45
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <a href="#" class="text-primary hover:underline">View Details</a>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Metrics Breakdown -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <!-- Score Distribution -->
        <div class="bg-white rounded-lg shadow-lg border border-gray-200 p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">
                <i class="fas fa-chart-pie mr-2 text-primary"></i>
                Score Distribution
            </h3>
            <div class="space-y-4">
                <div>
                    <div class="flex justify-between text-sm font-medium text-gray-700">
                        <span>Code Correctness</span>
                        <span>8.9/10</span>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-2">
                        <div class="bg-green-600 h-2 rounded-full" style="width: 89%"></div>
                    </div>
                </div>
                
                <div>
                    <div class="flex justify-between text-sm font-medium text-gray-700">
                        <span>Requirement Adherence</span>
                        <span>8.1/10</span>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-2">
                        <div class="bg-blue-600 h-2 rounded-full" style="width: 81%"></div>
                    </div>
                </div>
                
                <div>
                    <div class="flex justify-between text-sm font-medium text-gray-700">
                        <span>Code Quality</span>
                        <span>8.4/10</span>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-2">
                        <div class="bg-purple-600 h-2 rounded-full" style="width: 84%"></div>
                    </div>
                </div>
                
                <div>
                    <div class="flex justify-between text-sm font-medium text-gray-700">
                        <span>Safety Score</span>
                        <span>9.2/10</span>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-2">
                        <div class="bg-yellow-600 h-2 rounded-full" style="width: 92%"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Task Categories -->
        <div class="bg-white rounded-lg shadow-lg border border-gray-200 p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">
                <i class="fas fa-tags mr-2 text-primary"></i>
                Performance by Category
            </h3>
            <div class="space-y-4">
                <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div class="flex items-center">
                        <i class="fas fa-globe text-green-600 mr-3"></i>
                        <span class="font-medium">API Development</span>
                    </div>
                    <span class="text-green-600 font-bold">95%</span>
                </div>
                
                <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div class="flex items-center">
                        <i class="fas fa-shield-alt text-blue-600 mr-3"></i>
                        <span class="font-medium">Security</span>
                    </div>
                    <span class="text-blue-600 font-bold">87%</span>
                </div>
                
                <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div class="flex items-center">
                        <i class="fas fa-bug text-yellow-600 mr-3"></i>
                        <span class="font-medium">Bug Fixes</span>
                    </div>
                    <span class="text-yellow-600 font-bold">78%</span>
                </div>
                
                <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div class="flex items-center">
                        <i class="fas fa-cogs text-purple-600 mr-3"></i>
                        <span class="font-medium">Refactoring</span>
                    </div>
                    <span class="text-purple-600 font-bold">65%</span>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
class EvaluationDashboard {
    constructor() {
        this.bindEvents();
    }
    
    bindEvents() {
        document.querySelectorAll('.eval-btn').forEach(btn => {
            btn.addEventListener('click', (e) => this.runEvaluation(e));
        });
    }
    
    async runEvaluation(e) {
        const btn = e.target.closest('.eval-btn');
        const suite = btn.dataset.suite;
        
        // Disable button and show loading
        btn.disabled = true;
        btn.innerHTML = '<i class="fas fa-spinner fa-spin mr-1"></i> Running...';
        
        try {
            // In a real implementation, this would call the evaluation API
            await this.simulateEvaluation(suite);
            
            // Show results
            this.showResults(suite);
            
        } catch (error) {
            console.error('Evaluation failed:', error);
            alert('Evaluation failed: ' + error.message);
        } finally {
            // Re-enable button
            btn.disabled = false;
            btn.innerHTML = btn.dataset.suite === 'basic' ? 
                '<i class="fas fa-play mr-1"></i> Run Basic' : 
                '<i class="fas fa-play mr-1"></i> Run SWE-Bench';
        }
    }
    
    async simulateEvaluation(suite) {
        // Simulate evaluation process
        return new Promise(resolve => {
            setTimeout(resolve, 3000); // 3 second delay
        });
    }
    
    showResults(suite) {
        const resultsSection = document.getElementById('results-section');
        const resultsContent = document.getElementById('results-content');
        
        // Sample results based on suite
        const results = suite === 'basic' ? {
            success_rate: 100,
            tasks: [
                { name: 'Health Check Endpoint', status: 'pass', score: 9.2 },
                { name: 'Input Validation', status: 'pass', score: 8.7 },
                { name: 'Rate Limiting', status: 'pass', score: 8.9 }
            ]
        } : {
            success_rate: 67,
            tasks: [
                { name: 'Auth Bug Fix', status: 'pass', score: 8.1 },
                { name: 'User Profile Feature', status: 'fail', score: 6.3 },
                { name: 'Database Refactor', status: 'pass', score: 7.8 }
            ]
        };
        
        resultsContent.innerHTML = `
            <div class="mb-6">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold text-gray-900">${suite === 'basic' ? 'Basic' : 'SWE-Bench'} Evaluation Results</h3>
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${results.success_rate >= 80 ? 'bg-green-100 text-green-800' : results.success_rate >= 60 ? 'bg-yellow-100 text-yellow-800' : 'bg-red-100 text-red-800'}">
                        ${results.success_rate}% Success Rate
                    </span>
                </div>
                
                <div class="space-y-3">
                    ${results.tasks.map(task => `
                        <div class="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
                            <div class="flex items-center">
                                <i class="fas fa-${task.status === 'pass' ? 'check-circle text-green-600' : 'times-circle text-red-600'} mr-3"></i>
                                <span class="font-medium">${task.name}</span>
                            </div>
                            <div class="flex items-center space-x-4">
                                <span class="text-sm text-gray-600">Score: ${task.score}/10</span>
                                <span class="inline-flex items-center px-2 py-1 rounded text-xs font-medium ${task.status === 'pass' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}">
                                    ${task.status.toUpperCase()}
                                </span>
                            </div>
                        </div>
                    `).join('')}
                </div>
            </div>
        `;
        
        resultsSection.classList.remove('hidden');
    }
}

// Initialize dashboard
document.addEventListener('DOMContentLoaded', () => {
    new EvaluationDashboard();
});
</script>
{% endblock %}
