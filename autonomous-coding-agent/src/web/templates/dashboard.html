{% extends "base.html" %}

{% block title %}Dashboard - Autonomous Coding Agent{% endblock %}

{% block content %}
<div class="space-y-8">
    <!-- Hero Section -->
    <div class="bg-gradient-to-r from-primary to-secondary rounded-lg p-8 text-white">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-4xl font-bold mb-2">
                    <i class="fas fa-robot mr-3"></i>
                    Autonomous Coding Agent
                </h1>
                <p class="text-xl text-blue-100 mb-4">
                    Transform natural language into production-ready code with AI-powered automation
                </p>
                <div class="flex space-x-4">
                    <a href="/demo" class="bg-white text-primary px-6 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors">
                        <i class="fas fa-play mr-2"></i>Try Demo
                    </a>
                    <a href="/docs-ui" class="border border-white text-white px-6 py-3 rounded-lg font-semibold hover:bg-white hover:text-primary transition-colors">
                        <i class="fas fa-book mr-2"></i>View Docs
                    </a>
                </div>
            </div>
            <div class="hidden lg:block">
                <div class="w-64 h-48 bg-white bg-opacity-20 rounded-lg flex items-center justify-center">
                    <i class="fas fa-code text-6xl text-white opacity-60"></i>
                </div>
            </div>
        </div>
    </div>

    <!-- Features Grid -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <div class="bg-white rounded-lg shadow-lg p-6 border border-gray-200 hover:shadow-xl transition-shadow">
            <div class="flex items-center mb-4">
                <div class="w-12 h-12 bg-primary bg-opacity-10 rounded-lg flex items-center justify-center">
                    <i class="fas fa-stream text-primary text-xl"></i>
                </div>
                <h3 class="text-lg font-semibold ml-3">Real-time Streaming</h3>
            </div>
            <p class="text-gray-600">
                Watch your code come to life with live progress updates via Server-Sent Events.
            </p>
        </div>

        <div class="bg-white rounded-lg shadow-lg p-6 border border-gray-200 hover:shadow-xl transition-shadow">
            <div class="flex items-center mb-4">
                <div class="w-12 h-12 bg-accent bg-opacity-10 rounded-lg flex items-center justify-center">
                    <i class="fas fa-shield-alt text-accent text-xl"></i>
                </div>
                <h3 class="text-lg font-semibold ml-3">Secure Sandboxing</h3>
            </div>
            <p class="text-gray-600">
                Isolated execution environment using Modal for safe code modification.
            </p>
        </div>

        <div class="bg-white rounded-lg shadow-lg p-6 border border-gray-200 hover:shadow-xl transition-shadow">
            <div class="flex items-center mb-4">
                <div class="w-12 h-12 bg-secondary bg-opacity-10 rounded-lg flex items-center justify-center">
                    <i class="fas fa-brain text-secondary text-xl"></i>
                </div>
                <h3 class="text-lg font-semibold ml-3">AI-Powered Analysis</h3>
            </div>
            <p class="text-gray-600">
                Advanced code analysis using Claude AI and AST parsing for intelligent changes.
            </p>
        </div>

        <div class="bg-white rounded-lg shadow-lg p-6 border border-gray-200 hover:shadow-xl transition-shadow">
            <div class="flex items-center mb-4">
                <div class="w-12 h-12 bg-yellow-500 bg-opacity-10 rounded-lg flex items-center justify-center">
                    <i class="fab fa-github text-yellow-600 text-xl"></i>
                </div>
                <h3 class="text-lg font-semibold ml-3">GitHub Integration</h3>
            </div>
            <p class="text-gray-600">
                Automatic PR creation with detailed descriptions and change summaries.
            </p>
        </div>

        <div class="bg-white rounded-lg shadow-lg p-6 border border-gray-200 hover:shadow-xl transition-shadow">
            <div class="flex items-center mb-4">
                <div class="w-12 h-12 bg-purple-500 bg-opacity-10 rounded-lg flex items-center justify-center">
                    <i class="fas fa-chart-line text-purple-600 text-xl"></i>
                </div>
                <h3 class="text-lg font-semibold ml-3">Full Observability</h3>
            </div>
            <p class="text-gray-600">
                OpenTelemetry and LangSmith integration for comprehensive monitoring.
            </p>
        </div>

        <div class="bg-white rounded-lg shadow-lg p-6 border border-gray-200 hover:shadow-xl transition-shadow">
            <div class="flex items-center mb-4">
                <div class="w-12 h-12 bg-green-500 bg-opacity-10 rounded-lg flex items-center justify-center">
                    <i class="fas fa-rocket text-green-600 text-xl"></i>
                </div>
                <h3 class="text-lg font-semibold ml-3">Production Ready</h3>
            </div>
            <p class="text-gray-600">
                Built with FastAPI, Docker support, and comprehensive error handling.
            </p>
        </div>
    </div>

    <!-- Stats Section -->
    <div class="bg-white rounded-lg shadow-lg p-8 border border-gray-200">
        <h2 class="text-2xl font-bold text-gray-900 mb-6 text-center">
            <i class="fas fa-chart-bar mr-2 text-primary"></i>
            System Overview
        </h2>
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div class="text-center">
                <div class="text-3xl font-bold text-primary" id="total-requests">0</div>
                <div class="text-sm text-gray-600">Total Requests</div>
            </div>
            <div class="text-center">
                <div class="text-3xl font-bold text-accent" id="successful-prs">0</div>
                <div class="text-sm text-gray-600">Successful PRs</div>
            </div>
            <div class="text-center">
                <div class="text-3xl font-bold text-secondary" id="avg-time">0s</div>
                <div class="text-sm text-gray-600">Avg. Processing Time</div>
            </div>
            <div class="text-center">
                <div class="text-3xl font-bold text-yellow-600" id="uptime">99.9%</div>
                <div class="text-sm text-gray-600">Uptime</div>
            </div>
        </div>
    </div>

    <!-- Recent Activity -->
    <div class="bg-white rounded-lg shadow-lg border border-gray-200">
        <div class="p-6 border-b border-gray-200">
            <h2 class="text-xl font-semibold text-gray-900">
                <i class="fas fa-history mr-2 text-primary"></i>
                Recent Activity
            </h2>
        </div>
        <div class="p-6">
            <div class="space-y-4" id="recent-activity">
                <div class="flex items-center space-x-4 p-4 bg-gray-50 rounded-lg">
                    <div class="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                        <i class="fas fa-check text-green-600"></i>
                    </div>
                    <div class="flex-1">
                        <div class="text-sm font-medium text-gray-900">
                            Added health check endpoint to fastapi-demo
                        </div>
                        <div class="text-sm text-gray-600">
                            PR #123 created • 2 minutes ago
                        </div>
                    </div>
                    <div class="text-sm text-gray-500">
                        <a href="#" class="text-primary hover:underline">View PR</a>
                    </div>
                </div>

                <div class="flex items-center space-x-4 p-4 bg-gray-50 rounded-lg">
                    <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                        <i class="fas fa-code text-blue-600"></i>
                    </div>
                    <div class="flex-1">
                        <div class="text-sm font-medium text-gray-900">
                            Implemented input validation for user-service
                        </div>
                        <div class="text-sm text-gray-600">
                            PR #87 created • 15 minutes ago
                        </div>
                    </div>
                    <div class="text-sm text-gray-500">
                        <a href="#" class="text-primary hover:underline">View PR</a>
                    </div>
                </div>

                <div class="flex items-center space-x-4 p-4 bg-gray-50 rounded-lg">
                    <div class="w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center">
                        <i class="fas fa-shield-alt text-purple-600"></i>
                    </div>
                    <div class="flex-1">
                        <div class="text-sm font-medium text-gray-900">
                            Added JWT authentication to web-app
                        </div>
                        <div class="text-sm text-gray-600">
                            PR #45 created • 1 hour ago
                        </div>
                    </div>
                    <div class="text-sm text-gray-500">
                        <a href="#" class="text-primary hover:underline">View PR</a>
                    </div>
                </div>
            </div>
            
            <div class="mt-6 text-center">
                <a href="#" class="text-primary hover:underline text-sm font-medium">
                    View all activity →
                </a>
            </div>
        </div>
    </div>

    <!-- Quick Start -->
    <div class="bg-gradient-to-r from-gray-900 to-gray-800 rounded-lg p-8 text-white">
        <h2 class="text-2xl font-bold mb-4">
            <i class="fas fa-rocket mr-2"></i>
            Quick Start
        </h2>
        <p class="text-gray-300 mb-6">
            Get started with the Autonomous Coding Agent in just a few steps:
        </p>
        
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div class="flex items-start space-x-3">
                <div class="w-8 h-8 bg-primary rounded-full flex items-center justify-center text-white font-bold text-sm">
                    1
                </div>
                <div>
                    <h3 class="font-semibold mb-1">Provide Repository</h3>
                    <p class="text-sm text-gray-300">Enter your public GitHub repository URL</p>
                </div>
            </div>
            
            <div class="flex items-start space-x-3">
                <div class="w-8 h-8 bg-primary rounded-full flex items-center justify-center text-white font-bold text-sm">
                    2
                </div>
                <div>
                    <h3 class="font-semibold mb-1">Describe Changes</h3>
                    <p class="text-sm text-gray-300">Write what you want to implement in natural language</p>
                </div>
            </div>
            
            <div class="flex items-start space-x-3">
                <div class="w-8 h-8 bg-primary rounded-full flex items-center justify-center text-white font-bold text-sm">
                    3
                </div>
                <div>
                    <h3 class="font-semibold mb-1">Watch Magic Happen</h3>
                    <p class="text-sm text-gray-300">See real-time progress and get your PR automatically</p>
                </div>
            </div>
        </div>
        
        <div class="mt-8">
            <a href="/demo" class="bg-primary hover:bg-blue-600 text-white px-8 py-3 rounded-lg font-semibold transition-colors">
                <i class="fas fa-play mr-2"></i>
                Try It Now
            </a>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    // Animate counters
    function animateCounter(id, target) {
        const element = document.getElementById(id);
        let current = 0;
        const increment = target / 100;
        const timer = setInterval(() => {
            current += increment;
            if (current >= target) {
                current = target;
                clearInterval(timer);
            }
            element.textContent = Math.floor(current);
        }, 20);
    }

    // Simulate stats (in real app, these would come from API)
    document.addEventListener('DOMContentLoaded', () => {
        animateCounter('total-requests', 1247);
        animateCounter('successful-prs', 1189);
        
        // Simulate average time
        setTimeout(() => {
            document.getElementById('avg-time').textContent = '45s';
        }, 1000);
    });
</script>
{% endblock %}
