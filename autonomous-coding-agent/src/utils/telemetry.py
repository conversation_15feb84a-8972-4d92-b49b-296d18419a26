"""OpenTelemetry configuration for observability"""

import logging
from typing import Optional
from opentelemetry import trace
from opentelemetry.exporter.otlp.proto.grpc.trace_exporter import OTLPSpanExporter
from opentelemetry.sdk.trace import TracerProvider
from opentelemetry.sdk.trace.export import BatchSpanProcessor
from opentelemetry.sdk.resources import Resource
from opentelemetry.instrumentation.fastapi import FastAPIInstrumentor
from opentelemetry.instrumentation.httpx import HTTPXClientInstrumentor
from opentelemetry.instrumentation.logging import LoggingInstrumentor

logger = logging.getLogger(__name__)


class TelemetryManager:
    """Manages OpenTelemetry configuration and tracing"""
    
    def __init__(self, service_name: str = "autonomous-coding-agent"):
        self.service_name = service_name
        self.tracer_provider: Optional[TracerProvider] = None
        self.tracer: Optional[trace.Tracer] = None
        
    def setup_telemetry(self, otlp_endpoint: str = None, enable_console: bool = True):
        """Setup OpenTelemetry tracing"""
        
        # Create resource
        resource = Resource.create({
            "service.name": self.service_name,
            "service.version": "0.1.0"
        })
        
        # Create tracer provider
        self.tracer_provider = TracerProvider(resource=resource)
        trace.set_tracer_provider(self.tracer_provider)
        
        # Add exporters
        if otlp_endpoint:
            # OTLP exporter for production
            otlp_exporter = OTLPSpanExporter(endpoint=otlp_endpoint)
            self.tracer_provider.add_span_processor(
                BatchSpanProcessor(otlp_exporter)
            )
            logger.info(f"Added OTLP exporter: {otlp_endpoint}")
        
        if enable_console:
            # Console exporter for development
            from opentelemetry.exporter.console import ConsoleSpanExporter
            console_exporter = ConsoleSpanExporter()
            self.tracer_provider.add_span_processor(
                BatchSpanProcessor(console_exporter)
            )
            logger.info("Added console exporter")
        
        # Get tracer
        self.tracer = trace.get_tracer(__name__)
        
        # Instrument libraries
        self._instrument_libraries()
        
        logger.info("OpenTelemetry setup complete")
    
    def _instrument_libraries(self):
        """Auto-instrument common libraries"""
        try:
            # Instrument FastAPI
            FastAPIInstrumentor.instrument()
            
            # Instrument HTTPX (for API calls)
            HTTPXClientInstrumentor.instrument()
            
            # Instrument logging
            LoggingInstrumentor().instrument()
            
            logger.info("Auto-instrumentation complete")
            
        except Exception as e:
            logger.warning(f"Failed to setup auto-instrumentation: {str(e)}")
    
    def get_tracer(self) -> Optional[trace.Tracer]:
        """Get the configured tracer"""
        return self.tracer
    
    def create_span(self, name: str, attributes: dict = None):
        """Create a new span with optional attributes"""
        if not self.tracer:
            return trace.NoOpTracer().start_span(name)
        
        span = self.tracer.start_span(name)
        
        if attributes:
            for key, value in attributes.items():
                span.set_attribute(key, value)
        
        return span


# Global telemetry manager instance
telemetry_manager = TelemetryManager()


def setup_telemetry(otlp_endpoint: str = None, enable_console: bool = True):
    """Setup global telemetry configuration"""
    telemetry_manager.setup_telemetry(otlp_endpoint, enable_console)


def get_tracer() -> Optional[trace.Tracer]:
    """Get the global tracer instance"""
    return telemetry_manager.get_tracer()


def trace_function(name: str = None, attributes: dict = None):
    """Decorator to trace function execution"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            span_name = name or f"{func.__module__}.{func.__name__}"
            
            with telemetry_manager.create_span(span_name, attributes) as span:
                try:
                    result = func(*args, **kwargs)
                    span.set_attribute("function.success", True)
                    return result
                except Exception as e:
                    span.set_attribute("function.success", False)
                    span.set_attribute("function.error", str(e))
                    raise
        
        return wrapper
    return decorator


def trace_async_function(name: str = None, attributes: dict = None):
    """Decorator to trace async function execution"""
    def decorator(func):
        async def wrapper(*args, **kwargs):
            span_name = name or f"{func.__module__}.{func.__name__}"
            
            with telemetry_manager.create_span(span_name, attributes) as span:
                try:
                    result = await func(*args, **kwargs)
                    span.set_attribute("function.success", True)
                    return result
                except Exception as e:
                    span.set_attribute("function.success", False)
                    span.set_attribute("function.error", str(e))
                    raise
        
        return wrapper
    return decorator
