"""
Observability strategy combining OpenTelemetry and LangSmith
"""

from opentelemetry import trace
from langsmith import Client, traceable, trace as langsmith_trace
import logging
import time
from typing import Dict, Any, Optional
from contextlib import asynccontextmanager
from datetime import datetime

logger = logging.getLogger(__name__)


class ObservabilityManager:
    """Manages both OpenTelemetry and LangSmith observability"""
    
    def __init__(self, langsmith_api_key: str = None):
        # OpenTelemetry tracer for system observability
        self.tracer = trace.get_tracer(__name__)
        
        # LangSmith client for AI observability with correct headers
        if langsmith_api_key:
            try:
                self.langsmith_client = Client(
                    api_key=langsmith_api_key,
                    api_url="https://api.smith.langchain.com"
                )
                logger.info("LangSmith client initialized successfully")
            except Exception as e:
                logger.error(f"Failed to initialize LangSmith client: {e}")
                self.langsmith_client = None
        else:
            self.langsmith_client = None
        
    @asynccontextmanager
    async def observe_coding_process(self, session_id: str, prompt: str):
        """Observe the entire coding process with both systems"""
        
        # OpenTelemetry: Track the overall system operation
        with self.tracer.start_as_current_span("coding_process") as span:
            span.set_attribute("session_id", session_id)
            span.set_attribute("prompt_length", len(prompt))
            
            # LangSmith: Track the AI reasoning chain using traceable approach
            langsmith_active = self.langsmith_client is not None
            if langsmith_active:
                logger.info("LangSmith coding session trace started")
                
                # Create initial trace using traceable decorator
                self.create_langsmith_trace(
                    "autonomous_coding_session_start",
                    {"prompt": prompt, "session_id": session_id, "timestamp": datetime.now().isoformat()},
                    {"status": "session_started", "prompt_length": len(prompt)}
                )
            
            try:
                yield {
                    "otel_span": span,
                    "langsmith_active": langsmith_active
                }
                
                # Mark success
                span.set_status(trace.StatusCode.OK)
                if langsmith_active:
                    # Create completion trace
                    self.create_langsmith_trace(
                        "autonomous_coding_session_complete",
                        {"session_id": session_id},
                        {"status": "completed", "completion_time": datetime.now().isoformat()}
                    )
                    logger.info("LangSmith coding session completed successfully")
                    
            except Exception as e:
                # Track errors in both systems
                span.set_status(trace.StatusCode.ERROR, str(e))
                span.record_exception(e)
                
                if langsmith_active:
                    # Create error trace  
                    self.create_langsmith_trace(
                        "autonomous_coding_session_error",
                        {"session_id": session_id, "error": str(e)},
                        {"status": "error", "error_type": type(e).__name__, "error_time": datetime.now().isoformat()}
                    )
                    logger.error(f"LangSmith coding session failed: {str(e)}")
                raise
    
    @asynccontextmanager
    async def observe_ai_call(self, model_name: str, prompt: str, context: Dict[str, Any]):
        """Specifically observe AI/LLM calls"""
        
        # OpenTelemetry: Track the API call performance
        with self.tracer.start_as_current_span("ai_model_call") as span:
            span.set_attribute("model_name", model_name)
            span.set_attribute("prompt_tokens", len(prompt.split()))
            
            # LangSmith: Track AI-specific metrics using traceable approach
            langsmith_active = self.langsmith_client is not None
            if langsmith_active:
                logger.info(f"LangSmith LLM trace started for {model_name}")
                
                # Create initial AI call trace
                self.create_langsmith_trace(
                    f"llm_call_{model_name}_start",
                    {
                        "prompt": prompt[:500] + "..." if len(prompt) > 500 else prompt,  # Truncate long prompts
                        "model": model_name,
                        "context": context,
                        "timestamp": datetime.now().isoformat()
                    },
                    {"status": "ai_call_started", "model": model_name}
                )
            
            start_time = time.time()
            
            try:
                yield {
                    "otel_span": span,
                    "langsmith_active": langsmith_active
                }
                
                execution_time = time.time() - start_time
                span.set_attribute("execution_time", execution_time)
                span.set_status(trace.StatusCode.OK)
                
                if langsmith_active:
                    # Create completion trace
                    self.create_langsmith_trace(
                        f"llm_call_{model_name}_complete",
                        {"model": model_name, "execution_time": execution_time},
                        {
                            "status": "ai_call_completed",
                            "execution_time": execution_time,
                            "model": model_name,
                            "completion_time": datetime.now().isoformat()
                        }
                    )
                    logger.info(f"LangSmith LLM trace completed for {model_name}")
                
            except Exception as e:
                span.set_status(trace.StatusCode.ERROR, str(e))
                span.record_exception(e)
                
                if langsmith_active:
                    # Create error trace
                    self.create_langsmith_trace(
                        f"llm_call_{model_name}_error",
                        {"model": model_name, "error": str(e)},
                        {
                            "status": "ai_call_failed",
                            "error": str(e),
                            "model": model_name,
                            "error_time": datetime.now().isoformat()
                        }
                    )
                    logger.error(f"LangSmith LLM trace failed for {model_name}: {str(e)}")
                raise
    
    def track_ai_response(self, langsmith_run_id: str, response: str, 
                         token_usage: Dict[str, int], confidence: float = None):
        """Track AI response details in LangSmith"""
        if not self.langsmith_client or not langsmith_run_id:
            return
            
        outputs = {
            "response": response,
            "token_usage": token_usage,
            "response_length": len(response)
        }
        
        if confidence is not None:
            outputs["confidence"] = confidence
            
        self.langsmith_client.update_run(
            langsmith_run_id,
            outputs=outputs,
            end_time=None
        )
    
    def track_system_metrics(self, span: trace.Span, metrics: Dict[str, Any]):
        """Track system-level metrics in OpenTelemetry"""
        for key, value in metrics.items():
            if isinstance(value, (int, float, str, bool)):
                span.set_attribute(f"system.{key}", value)
    
    def create_custom_event(self, name: str, attributes: Dict[str, Any]):
        """Create custom events for specific business logic"""
        with self.tracer.start_as_current_span(name) as span:
            for key, value in attributes.items():
                span.set_attribute(key, value)
    
    def create_langsmith_trace(self, name: str, inputs: Dict[str, Any], outputs: Dict[str, Any] = None):
        """Create a simple LangSmith trace using the traceable decorator approach"""
        if not self.langsmith_client:
            return
            
        try:
            @traceable(name=name, project_name="project-tracing")
            def traced_operation(operation_inputs):
                return outputs or {"status": "completed", "timestamp": datetime.now().isoformat()}
            
            result = traced_operation(inputs)
            logger.info(f"LangSmith trace created: {name}")
            return result
            
        except Exception as e:
            logger.error(f"Failed to create LangSmith trace {name}: {e}")
            return None


# Usage example in the coding agent
class ObservableCodingAgent:
    """Coding agent with comprehensive observability"""
    
    def __init__(self, anthropic_api_key: str, langsmith_api_key: str = None):
        self.anthropic_api_key = anthropic_api_key
        self.observability = ObservabilityManager(langsmith_api_key)
    
    async def stream_coding_process(self, session_id: str, prompt: str, repo_info: Dict[str, Any]):
        """Main coding process with full observability"""
        
        async with self.observability.observe_coding_process(session_id, prompt) as obs:
            otel_span = obs["otel_span"]
            langsmith_run = obs["langsmith_run"]
            
            # Track repository info
            self.observability.track_system_metrics(otel_span, {
                "repo_files": repo_info.get("file_count", 0),
                "repo_size": len(str(repo_info))
            })
            
            # Repository analysis with observability
            async with self.observability.observe_ai_call(
                "claude-3-5-sonnet", 
                f"Analyze repository: {prompt}",
                {"repo_info": repo_info}
            ) as ai_obs:
                
                # Call Claude for analysis
                analysis = await self._call_claude_for_analysis(prompt, repo_info)
                
                # Track AI response in LangSmith
                if ai_obs["langsmith_run"]:
                    self.observability.track_ai_response(
                        ai_obs["langsmith_run"].id,
                        analysis["response"],
                        analysis["token_usage"],
                        analysis.get("confidence")
                    )
            
            # Track custom business events
            self.observability.create_custom_event("code_modification_started", {
                "session_id": session_id,
                "modification_type": "endpoint_addition",
                "estimated_complexity": "medium"
            })
            
            # Continue with the rest of the coding process...
            yield {"type": "status", "message": "Analysis complete"}


# Real-world debugging scenarios
"""
Scenario 1: Slow Performance
- OpenTelemetry: "Modal sandbox creation takes 15 seconds"
- LangSmith: "Claude is using 100k tokens per request (too much context)"
- Action: Optimize prompt length and sandbox warmup

Scenario 2: High Error Rate  
- OpenTelemetry: "GitHub API returning 403 errors"
- LangSmith: "Model confidence is low (0.2) on complex tasks"
- Action: Fix GitHub auth and improve prompts for complex scenarios

Scenario 3: Cost Optimization
- OpenTelemetry: "Average request time is acceptable (30s)"
- LangSmith: "Token usage is 5x higher than expected"
- Action: Optimize prompts to reduce token consumption

Scenario 4: Quality Issues
- OpenTelemetry: "Requests completing successfully"
- LangSmith: "Model reasoning shows misunderstanding of requirements"
- Action: Improve prompt engineering and add examples
"""
