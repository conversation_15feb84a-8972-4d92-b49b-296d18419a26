"""LangSmith configuration for AI tracing"""

import logging
from typing import Optional
from langsmith import Client
from langsmith.wrappers import wrap_openai
from .config import get_settings

try:
    from langsmith.wrappers import wrap_anthropic
    ANTHROPIC_WRAPPER_AVAILABLE = True
except ImportError:
    ANTHROPIC_WRAPPER_AVAILABLE = False
    logger = logging.getLogger(__name__)
    logger.warning("Anthropic wrapper not available in LangSmith")

logger = logging.getLogger(__name__)


class LangSmithManager:
    """Manages LangSmith tracing configuration"""
    
    def __init__(self):
        self.client: Optional[Client] = None
        self.enabled = False
        
    def setup_langsmith(self, api_key: str = None, project: str = None):
        """Setup LangSmith tracing"""
        
        settings = get_settings()
        api_key = api_key or settings.langsmith_api_key
        project = project or settings.langsmith_project
        
        if not api_key:
            logger.info("LangSmith API key not provided, skipping setup")
            return
        
        try:
            # Initialize LangSmith client
            self.client = Client(api_key=api_key)
            
            # Set environment variables for automatic tracing
            import os
            os.environ["LANGCHAIN_TRACING_V2"] = "true"
            os.environ["LANGCHAIN_API_KEY"] = api_key
            os.environ["LANGCHAIN_PROJECT"] = project
            
            self.enabled = True
            logger.info(f"LangSmith tracing enabled for project: {project}")
            
        except Exception as e:
            logger.error(f"Failed to setup LangSmith: {str(e)}")
    
    def wrap_anthropic_client(self, client):
        """Wrap Anthropic client with LangSmith tracing"""
        if not self.enabled:
            logger.warning("LangSmith not enabled, returning unwrapped client")
            return client
        
        if not ANTHROPIC_WRAPPER_AVAILABLE:
            logger.warning("Anthropic wrapper not available, returning unwrapped client")
            return client
        
        try:
            return wrap_anthropic(client)
        except Exception as e:
            logger.error(f"Failed to wrap Anthropic client: {str(e)}")
            return client
    
    def trace_run(self, name: str, inputs: dict, run_type: str = "llm"):
        """Create a new run trace"""
        if not self.enabled or not self.client:
            return None
        
        try:
            return self.client.create_run(
                name=name,
                inputs=inputs,
                run_type=run_type
            )
        except Exception as e:
            logger.error(f"Failed to create LangSmith run: {str(e)}")
            return None
    
    def end_run(self, run_id: str, outputs: dict = None, error: str = None):
        """End a run trace"""
        if not self.enabled or not self.client:
            return
        
        try:
            self.client.update_run(
                run_id=run_id,
                outputs=outputs,
                error=error,
                end_time=None  # Will use current time
            )
        except Exception as e:
            logger.error(f"Failed to end LangSmith run: {str(e)}")
    
    def log_feedback(self, run_id: str, score: float, comment: str = None):
        """Log feedback for a run"""
        if not self.enabled or not self.client:
            return
        
        try:
            self.client.create_feedback(
                run_id=run_id,
                score=score,
                comment=comment
            )
        except Exception as e:
            logger.error(f"Failed to log feedback: {str(e)}")


# Global LangSmith manager instance
langsmith_manager = LangSmithManager()


def setup_langsmith(api_key: str = None, project: str = None):
    """Setup global LangSmith configuration"""
    langsmith_manager.setup_langsmith(api_key, project)


def get_traced_anthropic_client(client):
    """Get Anthropic client with LangSmith tracing"""
    return langsmith_manager.wrap_anthropic_client(client)


def trace_llm_call(name: str, inputs: dict):
    """Context manager for tracing LLM calls"""
    class LLMTraceContext:
        def __init__(self, name: str, inputs: dict):
            self.name = name
            self.inputs = inputs
            self.run_id = None
            
        def __enter__(self):
            run = langsmith_manager.trace_run(self.name, self.inputs, "llm")
            self.run_id = run.id if run else None
            return self
            
        def __exit__(self, exc_type, exc_val, exc_tb):
            if self.run_id:
                if exc_type:
                    langsmith_manager.end_run(self.run_id, error=str(exc_val))
                else:
                    langsmith_manager.end_run(self.run_id)
        
        def set_outputs(self, outputs: dict):
            if self.run_id:
                langsmith_manager.end_run(self.run_id, outputs=outputs)
    
    return LLMTraceContext(name, inputs)


def trace_agent_step(name: str, inputs: dict):
    """Context manager for tracing agent steps"""
    class AgentTraceContext:
        def __init__(self, name: str, inputs: dict):
            self.name = name
            self.inputs = inputs
            self.run_id = None
            
        def __enter__(self):
            run = langsmith_manager.trace_run(self.name, self.inputs, "chain")
            self.run_id = run.id if run else None
            return self
            
        def __exit__(self, exc_type, exc_val, exc_tb):
            if self.run_id:
                if exc_type:
                    langsmith_manager.end_run(self.run_id, error=str(exc_val))
                else:
                    langsmith_manager.end_run(self.run_id)
        
        def set_outputs(self, outputs: dict):
            if self.run_id:
                langsmith_manager.end_run(self.run_id, outputs=outputs)
    
    return AgentTraceContext(name, inputs)
