"""
Simple E2B sandbox implementation for testing
"""

import asyncio
import logging
import uuid
from typing import Dict, Any, Optional
from urllib.parse import urlparse

from e2b_code_interpreter import Sandbox

logger = logging.getLogger(__name__)


class E2BSandbox:
    """Simple E2B-based sandbox for secure code execution"""
    
    def __init__(self, e2b_api_key: str = None):
        self.e2b_api_key = e2b_api_key
        self.active_sessions: Dict[str, Any] = {}
    
    async def create_session(self) -> str:
        """Create a new sandbox session"""
        session_id = str(uuid.uuid4())
        
        try:
            # Create E2B sandbox (synchronous call wrapped in executor)
            def create_sandbox():
                return Sandbox(
                    # Use default template (base Ubuntu)
                    api_key=self.e2b_api_key,
                    timeout=1800  # 30 minutes
                )
            
            sandbox = await asyncio.get_event_loop().run_in_executor(None, create_sandbox)
            
            self.active_sessions[session_id] = {
                "sandbox": sandbox,
                "working_dir": "/tmp",
                "created_at": asyncio.get_event_loop().time()
            }
            
            logger.info(f"Created E2B session: {session_id}")
            return session_id
            
        except Exception as e:
            logger.error(f"Failed to create E2B session: {e}")
            raise Exception(f"Failed to create sandbox session: {str(e)}")
    
    async def clone_repository(self, session_id: str, repo_url: str) -> Dict[str, Any]:
        """Clone a repository in the sandbox"""
        if session_id not in self.active_sessions:
            raise Exception(f"Session {session_id} not found")
        
        sandbox = self.active_sessions[session_id]["sandbox"]
        
        # Parse repository URL to get repo name
        parsed_url = urlparse(repo_url)
        repo_name = parsed_url.path.rstrip('/').split('/')[-1]
        if repo_name.endswith('.git'):
            repo_name = repo_name[:-4]
        
        try:
            # Clone repository using git command
            def run_clone():
                result = sandbox.run_code(f"import subprocess; result = subprocess.run(['git', 'clone', '{repo_url}', '/tmp/{repo_name}'], capture_output=True, text=True); print('EXIT_CODE:', result.returncode); print('STDOUT:', result.stdout); print('STDERR:', result.stderr)")
                return result
            
            clone_result = await asyncio.get_event_loop().run_in_executor(None, run_clone)
            
            if clone_result.exit_code != 0:
                raise Exception(f"Git clone failed: {clone_result.stderr}")
            
            # Get file count and info
            def get_file_info():
                # Count files
                count_result = sandbox.run(f"find /tmp/{repo_name} -type f | wc -l")
                file_count = int(count_result.stdout.strip()) if count_result.exit_code == 0 else 0
                
                # Get key files
                files_result = sandbox.run(f"find /tmp/{repo_name} -name '*.py' -o -name '*.js' -o -name '*.ts' -o -name '*.md' | head -10")
                key_files = files_result.stdout.strip().split('\n') if files_result.exit_code == 0 and files_result.stdout.strip() else []
                
                return file_count, key_files
            
            file_count, key_files = await asyncio.get_event_loop().run_in_executor(None, get_file_info)
            
            # Update session info
            self.active_sessions[session_id].update({
                "repo_name": repo_name,
                "repo_dir": f"/tmp/{repo_name}",
                "repo_url": repo_url
            })
            
            return {
                "repo_name": repo_name,
                "repo_dir": f"/tmp/{repo_name}",
                "file_count": file_count,
                "key_files": key_files,
                "clone_output": clone_result.stdout
            }
            
        except Exception as e:
            logger.error(f"Error cloning repository: {e}")
            raise Exception(f"Failed to clone repository: {str(e)}")
    
    async def execute_command(self, session_id: str, command: str) -> Dict[str, Any]:
        """Execute a command in the sandbox"""
        if session_id not in self.active_sessions:
            raise Exception(f"Session {session_id} not found")
        
        sandbox = self.active_sessions[session_id]["sandbox"]
        session = self.active_sessions[session_id]
        repo_dir = session.get("repo_dir", "/tmp")
        
        try:
            # Execute command in the sandbox
            def run_command():
                return sandbox.run(f"cd {repo_dir} && {command}")
            
            result = await asyncio.get_event_loop().run_in_executor(None, run_command)
            
            success = result.exit_code == 0
            
            logger.info(f"Session {session_id} executed: {command}")
            logger.info(f"Result: success={success}, stdout={result.stdout[:200]}...")
            
            return {
                "success": success,
                "stdout": result.stdout,
                "stderr": result.stderr,
                "returncode": result.exit_code
            }
            
        except Exception as e:
            logger.error(f"Error executing command: {e}")
            return {
                "success": False,
                "stdout": "",
                "stderr": str(e),
                "returncode": -1
            }
    
    async def read_file(self, session_id: str, file_path: str) -> Dict[str, Any]:
        """Read a file from the sandbox"""
        if session_id not in self.active_sessions:
            raise Exception(f"Session {session_id} not found")
        
        sandbox = self.active_sessions[session_id]["sandbox"]
        session = self.active_sessions[session_id]
        repo_dir = session.get("repo_dir", "/tmp")
        
        try:
            def read_file_content():
                full_path = f"{repo_dir}/{file_path}" if not file_path.startswith('/') else file_path
                result = sandbox.run(f"cat {full_path}")
                return result
            
            result = await asyncio.get_event_loop().run_in_executor(None, read_file_content)
            
            if result.exit_code == 0:
                return {
                    "success": True,
                    "content": result.stdout,
                    "error": None
                }
            else:
                return {
                    "success": False,
                    "content": "",
                    "error": f"File not found or read error: {result.stderr}"
                }
                
        except Exception as e:
            logger.error(f"Error reading file: {e}")
            return {
                "success": False,
                "content": "",
                "error": str(e)
            }
    
    async def write_file(self, session_id: str, file_path: str, content: str) -> Dict[str, Any]:
        """Write a file to the sandbox"""
        if session_id not in self.active_sessions:
            raise Exception(f"Session {session_id} not found")
        
        sandbox = self.active_sessions[session_id]["sandbox"]
        session = self.active_sessions[session_id]
        repo_dir = session.get("repo_dir", "/tmp")
        
        try:
            def write_file_content():
                full_path = f"{repo_dir}/{file_path}" if not file_path.startswith('/') else file_path
                
                # Create directory if needed
                dir_result = sandbox.run(f"mkdir -p $(dirname {full_path})")
                
                # Write file using cat with heredoc
                escaped_content = content.replace("'", "'\"'\"'")  # Escape single quotes
                result = sandbox.run(f"cat > {full_path} << 'EOF'\n{escaped_content}\nEOF")
                return result
            
            result = await asyncio.get_event_loop().run_in_executor(None, write_file_content)
            
            if result.exit_code == 0:
                return {
                    "success": True,
                    "error": None
                }
            else:
                return {
                    "success": False,
                    "error": f"Write failed: {result.stderr}"
                }
                
        except Exception as e:
            logger.error(f"Error writing file: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def cleanup_session(self, session_id: str):
        """Clean up a sandbox session"""
        if session_id in self.active_sessions:
            try:
                sandbox = self.active_sessions[session_id]["sandbox"]
                await asyncio.get_event_loop().run_in_executor(None, sandbox.kill)
            except Exception as e:
                logger.error(f"Error closing E2B sandbox: {e}")
            
            del self.active_sessions[session_id]
            logger.info(f"Cleaned up session: {session_id}")
    
    async def cleanup_all(self):
        """Clean up all active sessions"""
        for session_id in list(self.active_sessions.keys()):
            await self.cleanup_session(session_id)
    
    def get_session_info(self, session_id: str) -> Optional[Dict[str, Any]]:
        """Get session information"""
        return self.active_sessions.get(session_id)
