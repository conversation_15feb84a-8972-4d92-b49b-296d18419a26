"""
E2B Sandbox Integration for Autonomous Coding Agent
Provides secure, isolated environment for code execution
"""

import logging
import uuid
from typing import Dict, Any
from e2b_code_interpreter import Sandbox
from ..utils.config import get_settings

logger = logging.getLogger(__name__)


class E2BSandbox:
    """E2B-based sandbox manager for secure code execution"""
    
    def __init__(self):
        self.settings = get_settings()
        self.active_sessions: Dict[str, Sandbox] = {}
        
    async def create_session(self, session_id: str = None) -> str:
        """Create a new E2B sandbox session"""
        if session_id is None:
            session_id = str(uuid.uuid4())
        
        try:
            # Create E2B sandbox instance
            # E2B timeout is in seconds and max is 1 hour (3600 seconds)
            timeout_seconds = min(self.settings.sandbox_timeout_minutes * 60, 3600)
            logger.info(f"Creating E2B sandbox with timeout: {timeout_seconds} seconds")
            
            sandbox = Sandbox(
                api_key=self.settings.e2b_api_key,
                timeout=timeout_seconds
            )
            
            # Store the session
            self.active_sessions[session_id] = sandbox
            
            # Initialize basic setup
            await self._initialize_sandbox(sandbox)
            
            logger.info(f"Created E2B sandbox session: {session_id}")
            return session_id
            
        except Exception as e:
            logger.error(f"Failed to create E2B sandbox session: {e}")
            # Try with default timeout if custom timeout fails
            try:
                logger.info("Retrying with default timeout (600 seconds)")
                sandbox = Sandbox(api_key=self.settings.e2b_api_key, timeout=600)
                self.active_sessions[session_id] = sandbox
                await self._initialize_sandbox(sandbox)
                logger.info(f"Created E2B sandbox session with default timeout: {session_id}")
                return session_id
            except Exception as e2:
                logger.error(f"Failed to create E2B sandbox with default timeout: {e2}")
                raise
    
    async def _initialize_sandbox(self, sandbox: Sandbox):
        """Initialize sandbox with basic tools"""
        try:
            # Install common dependencies
            result = sandbox.run_code("!pip install requests aiohttp")
            if result.error:
                logger.warning(f"Failed to install basic dependencies: {result.error}")
            
            # Set up git configuration
            sandbox.run_code("!git config --global user.email '<EMAIL>'")
            sandbox.run_code("!git config --global user.name 'Backspace Agent'")
            
        except Exception as e:
            logger.warning(f"Failed to initialize sandbox: {e}")
    
    async def execute_command(self, session_id: str, command: str, 
                            working_dir: str = None) -> Dict[str, Any]:
        """Execute a command in the sandbox"""
        if session_id not in self.active_sessions:
            return {"success": False, "error": "Session not found"}
        
        sandbox = self.active_sessions[session_id]
        
        try:
            # Change directory if specified
            if working_dir:
                cd_result = sandbox.run_code(f"!cd {working_dir}")
                if cd_result.error:
                    return {
                        "success": False,
                        "error": f"Failed to change directory: {cd_result.error}"
                    }
            
            # Execute the command using ! prefix for shell commands
            if not command.startswith("!"):
                command = "!" + command
            
            result = sandbox.run_code(command)
            
            # Handle different result types from E2B
            if hasattr(result, 'stdout'):
                # Standard execution result
                return {
                    "success": not result.error,
                    "stdout": result.stdout if result.stdout else "",
                    "stderr": result.stderr if result.stderr else result.error if result.error else "",
                    "exit_code": 0 if not result.error else 1
                }
            elif hasattr(result, 'logs'):
                # For some E2B responses, logs contain stdout
                try:
                    stdout_logs = [log.line for log in result.logs.stdout if hasattr(log, 'line')]
                    stderr_logs = [log.line for log in result.logs.stderr if hasattr(log, 'line')]
                except AttributeError:
                    # Try direct logs access
                    stdout_logs = []
                    stderr_logs = []
                    if hasattr(result.logs, 'stdout'):
                        stdout_logs = [result.logs.stdout] if result.logs.stdout else []
                    if hasattr(result.logs, 'stderr'):
                        stderr_logs = [result.logs.stderr] if result.logs.stderr else []
                
                return {
                    "success": not result.error,
                    "stdout": '\n'.join(stdout_logs),
                    "stderr": '\n'.join(stderr_logs) if stderr_logs else (result.error if result.error else ""),
                    "exit_code": 0 if not result.error else 1
                }
            else:
                # Fallback for unknown result structure
                return {
                    "success": not result.error,
                    "stdout": str(result) if not result.error else "",
                    "stderr": result.error if result.error else "",
                    "exit_code": 0 if not result.error else 1
                }
            
        except Exception as e:
            logger.error(f"Command execution failed: {e}")
            return {"success": False, "error": str(e)}
    
    async def read_file(self, session_id: str, file_path: str) -> Dict[str, Any]:
        """Read a file from the sandbox"""
        if session_id not in self.active_sessions:
            return {"success": False, "error": "Session not found"}
        
        sandbox = self.active_sessions[session_id]
        
        try:
            # Check if file exists
            check_result = sandbox.run_code(f"!test -f {file_path}")
            if check_result.error:
                return {"success": False, "error": "File not found"}
            
            # Read file content
            result = sandbox.run_code(f"!cat {file_path}")
            if not result.error:
                # Handle different result types from E2B
                if hasattr(result, 'stdout'):
                    content = result.stdout
                elif hasattr(result, 'logs'):
                    try:
                        stdout_logs = [log.line for log in result.logs.stdout if hasattr(log, 'line')]
                        content = '\n'.join(stdout_logs)
                    except AttributeError:
                        content = str(result.logs.stdout) if hasattr(result.logs, 'stdout') else str(result.logs)
                else:
                    content = str(result)
                return {"success": True, "content": content}
            else:
                return {"success": False, "error": result.error}
                
        except Exception as e:
            logger.error(f"File read failed: {e}")
            return {"success": False, "error": str(e)}
    
    async def write_file(self, session_id: str, file_path: str, content: str) -> Dict[str, Any]:
        """Write content to a file in the sandbox"""
        if session_id not in self.active_sessions:
            return {"success": False, "error": "Session not found"}
        
        sandbox = self.active_sessions[session_id]
        
        try:
            # Create directory if needed
            dir_path = "/".join(file_path.split("/")[:-1])
            if dir_path:
                sandbox.run_code(f"!mkdir -p {dir_path}")
            
            # Write file using cat with heredoc to handle special characters
            escaped_content = content.replace("'", "'\"'\"'")  # Escape single quotes
            result = sandbox.run_code(f"!cat > {file_path} << 'EOF'\n{escaped_content}\nEOF")
            
            if not result.error:
                logger.info(f"Successfully wrote to file: {file_path}")
                return {"success": True}
            else:
                return {"success": False, "error": result.error}
                
        except Exception as e:
            logger.error(f"File write failed: {e}")
            return {"success": False, "error": str(e)}
    
    async def clone_repository(self, session_id: str, repo_url: str, 
                             branch: str = "main") -> Dict[str, Any]:
        """Clone a repository in the sandbox"""
        if session_id not in self.active_sessions:
            return {"success": False, "error": "Session not found"}
        
        try:
            # Extract repo name from URL
            repo_name = repo_url.split('/')[-1].replace('.git', '')
            
            # Clone the repository
            clone_result = await self.execute_command(
                session_id, 
                f"git clone {repo_url} {repo_name}",
                timeout=120
            )
            
            if not clone_result["success"]:
                return clone_result
            
            # Change to the cloned directory
            await self.execute_command(session_id, f"cd {repo_name}")
            
            # Checkout the specified branch
            if branch != "main":
                await self.execute_command(session_id, f"cd {repo_name} && git checkout {branch}")
            
            # Get file count for reporting
            file_count_result = await self.execute_command(session_id, f"find {repo_name} -type f | wc -l")
            try:
                file_count_str = file_count_result.get("stdout", "0").strip()
                file_count = int(file_count_str) if file_count_str and file_count_str.isdigit() else 0
            except ValueError:
                file_count = 0
            
            return {
                "success": True,
                "repo_name": repo_name,
                "branch": branch,
                "file_count": file_count
            }
            
        except Exception as e:
            logger.error(f"Repository clone failed: {e}")
            return {"success": False, "error": str(e)}
    
    async def setup_git_auth(self, session_id: str, github_token: str) -> Dict[str, Any]:
        """Setup Git authentication for GitHub operations"""
        if session_id not in self.active_sessions:
            return {"success": False, "error": "Session not found"}
        
        try:
            # Setup git credential helper for GitHub
            result = await self.execute_command(
                session_id, 
                f"git config --global credential.helper 'store --file ~/.git-credentials'"
            )
            
            if result["success"]:
                # Store GitHub token
                cred_content = f"https://{github_token}:<EMAIL>"
                write_result = await self.execute_command(
                    session_id,
                    f"echo '{cred_content}' > ~/.git-credentials"
                )
                
                if write_result["success"]:
                    logger.info("Git authentication setup successful")
                    return {"success": True}
                else:
                    return {"success": False, "error": "Failed to store credentials"}
            else:
                return {"success": False, "error": "Failed to setup credential helper"}
                
        except Exception as e:
            logger.error(f"Git auth setup failed: {e}")
            return {"success": False, "error": str(e)}
    
    async def push_branch(self, session_id: str, branch_name: str) -> Dict[str, Any]:
        """Push a branch to the remote repository"""
        if session_id not in self.active_sessions:
            return {"success": False, "error": "Session not found"}
        
        try:
            # Push the branch to origin
            result = await self.execute_command(
                session_id, 
                f"git push -u origin {branch_name}",
                timeout=120
            )
            
            if result["success"]:
                logger.info(f"Successfully pushed branch: {branch_name}")
                return {"success": True, "branch": branch_name}
            else:
                logger.error(f"Failed to push branch: {result.get('stderr', result.get('error'))}")
                return {"success": False, "error": result.get('stderr', result.get('error'))}
                
        except Exception as e:
            logger.error(f"Branch push failed: {e}")
            return {"success": False, "error": str(e)}
    
    async def create_pull_request(self, session_id: str, title: str, 
                                body: str, branch_name: str) -> Dict[str, Any]:
        """Create a pull request using GitHub API"""
        if session_id not in self.active_sessions:
            return {"success": False, "error": "Session not found"}
        
        try:
            # Get GitHub token from settings
            github_token = self.settings.github_token
            
            # Get current directory (should be the repo directory)
            pwd_result = await self.execute_command(session_id, "pwd")
            if not pwd_result["success"]:
                return {"success": False, "error": "Failed to get current directory"}
            
            # Extract owner and repo from remote URL
            get_remote_cmd = "git remote get-url origin"
            remote_result = await self.execute_command(session_id, get_remote_cmd)
            
            if not remote_result["success"]:
                return {"success": False, "error": "Failed to get remote URL"}
            
            remote_url = remote_result["stdout"].strip()
            
            # Parse owner and repo name from URL
            if "github.com" in remote_url:
                parts = remote_url.replace("https://github.com/", "").replace("**************:", "").replace(".git", "").split("/")
                if len(parts) >= 2:
                    owner = parts[0]
                    repo = parts[1]
                else:
                    return {"success": False, "error": "Invalid GitHub URL format"}
            else:
                return {"success": False, "error": "Not a GitHub repository"}
            
            # Escape quotes in JSON data
            escaped_title = title.replace('"', '\\"')
            escaped_body = body.replace('"', '\\"').replace('\n', '\\n')
            
            # Use curl to create PR
            curl_cmd = f"""curl -X POST \
                -H "Authorization: token {github_token}" \
                -H "Accept: application/vnd.github.v3+json" \
                -H "Content-Type: application/json" \
                -d '{{"title": "{escaped_title}", "body": "{escaped_body}", "head": "{branch_name}", "base": "main"}}' \
                https://api.github.com/repos/{owner}/{repo}/pulls"""
            
            pr_result = await self.execute_command(session_id, curl_cmd)
            
            if pr_result["success"]:
                return {
                    "success": True,
                    "pr_url": f"https://github.com/{owner}/{repo}/pull",
                    "response": pr_result["stdout"]
                }
            else:
                return {"success": False, "error": pr_result["stderr"]}
                
        except Exception as e:
            logger.error(f"Pull request creation failed: {e}")
            return {"success": False, "error": str(e)}
    
    async def cleanup_session(self, session_id: str):
        """Clean up and close a sandbox session"""
        if session_id in self.active_sessions:
            try:
                sandbox = self.active_sessions[session_id]
                sandbox.kill()  # Use kill() instead of close() for E2B
                del self.active_sessions[session_id]
                logger.info(f"Cleaned up E2B sandbox session: {session_id}")
            except Exception as e:
                logger.error(f"Failed to cleanup session {session_id}: {e}")
    
    async def list_files(self, session_id: str, directory: str = ".") -> Dict[str, Any]:
        """List files in a directory"""
        return await self.execute_command(session_id, f"find {directory} -type f | head -20")
    
    async def get_file_content(self, session_id: str, file_path: str, 
                             max_lines: int = 100) -> Dict[str, Any]:
        """Get file content with optional line limit"""
        if max_lines:
            return await self.execute_command(session_id, f"head -n {max_lines} {file_path}")
        else:
            return await self.read_file(session_id, file_path)
    
    def __del__(self):
        """Cleanup all sessions on destruction"""
        for session_id in list(self.active_sessions.keys()):
            try:
                self.active_sessions[session_id].kill()
            except:
                pass