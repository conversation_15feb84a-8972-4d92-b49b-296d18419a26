"""
E2B-based sandbox for secure code execution
"""

import asyncio
import logging
from typing import Dict, Any, Optional
from urllib.parse import urlparse
import json
import uuid

from e2b_code_interpreter import Sandbox

logger = logging.getLogger(__name__)


class E2BSandbox:
    """E2B-based sandbox for secure code execution"""
    
    def __init__(self, e2b_api_key: str = None):
        self.e2b_api_key = e2b_api_key
        self.active_sessions: Dict[str, Any] = {}
    
    async def create_session(self) -> str:
        """Create a new sandbox session"""
        session_id = str(uuid.uuid4())
        
        try:
            # Create E2B sandbox
            interpreter = Sandbox(api_key=self.e2b_api_key)
            
            self.active_sessions[session_id] = {
                "interpreter": interpreter,
                "working_dir": "/tmp",
                "created_at": asyncio.get_event_loop().time()
            }
            
            logger.info(f"Created E2B session: {session_id}")
            return session_id
            
        except Exception as e:
            logger.error(f"Failed to create E2B session: {e}")
            raise Exception(f"Failed to create sandbox session: {str(e)}")
    
    async def clone_repository(self, session_id: str, repo_url: str) -> Dict[str, Any]:
        """Clone a repository in the sandbox"""
        if session_id not in self.active_sessions:
            raise Exception(f"Session {session_id} not found")
        
        interpreter = self.active_sessions[session_id]["interpreter"]
        
        # Parse repository URL to get repo name
        parsed_url = urlparse(repo_url)
        repo_name = parsed_url.path.rstrip('/').split('/')[-1]
        if repo_name.endswith('.git'):
            repo_name = repo_name[:-4]
        
        try:
            # Clone repository using git
            clone_result = interpreter.notebook.exec_cell(f"""
import subprocess
import os

# Clone the repository
result = subprocess.run(['git', 'clone', '{repo_url}', '{repo_name}'], 
                       capture_output=True, text=True, cwd='/tmp')

print("STDOUT:", result.stdout)
print("STDERR:", result.stderr)
print("Return code:", result.returncode)

# Change to repo directory and get info
if result.returncode == 0:
    os.chdir(f'/tmp/{repo_name}')
    
    # Count files
    file_count_result = subprocess.run(['find', '.', '-type', 'f'], 
                                     capture_output=True, text=True)
    file_count = len(file_count_result.stdout.strip().split('\\n')) if file_count_result.stdout.strip() else 0
    
    # Get key files
    key_files_result = subprocess.run(['find', '.', '-name', '*.py', '-o', '-name', '*.js', '-o', '-name', '*.ts', '-o', '-name', '*.md'], 
                                    capture_output=True, text=True)
    key_files = key_files_result.stdout.strip().split('\\n')[:20] if key_files_result.stdout.strip() else []
    
    print("FILE_COUNT:", file_count)
    print("KEY_FILES:", key_files)
else:
    print("Clone failed")
""")
            
            if "Clone failed" in clone_result.text:
                raise Exception("Failed to clone repository")
            
            # Extract file count and key files from output
            output_lines = clone_result.text.split('\n')
            file_count = 0
            key_files = []
            
            for line in output_lines:
                if line.startswith("FILE_COUNT:"):
                    try:
                        file_count = int(line.split(":", 1)[1].strip())
                    except:
                        pass
                elif line.startswith("KEY_FILES:"):
                    try:
                        files_str = line.split(":", 1)[1].strip()
                        key_files = eval(files_str)  # Safe in sandbox environment
                    except:
                        pass
            
            # Update session info
            self.active_sessions[session_id].update({
                "repo_name": repo_name,
                "repo_dir": f"/tmp/{repo_name}",
                "repo_url": repo_url
            })
            
            return {
                "repo_name": repo_name,
                "repo_dir": f"/tmp/{repo_name}",
                "file_count": file_count,
                "key_files": key_files,
                "clone_output": clone_result.text
            }
            
        except Exception as e:
            logger.error(f"Error cloning repository: {e}")
            raise Exception(f"Failed to clone repository: {str(e)}")
    
    async def execute_command(self, session_id: str, command: str) -> Dict[str, Any]:
        """Execute a command in the sandbox"""
        if session_id not in self.active_sessions:
            raise Exception(f"Session {session_id} not found")
        
        interpreter = self.active_sessions[session_id]["interpreter"]
        session = self.active_sessions[session_id]
        repo_dir = session.get("repo_dir", "/tmp")
        
        try:
            # Execute command in the sandbox
            result = interpreter.notebook.exec_cell(f"""
import subprocess
import os

# Change to working directory
os.chdir('{repo_dir}')

# Execute the command
result = subprocess.run('{command}', shell=True, capture_output=True, text=True)

print("STDOUT:", result.stdout)
print("STDERR:", result.stderr)  
print("RETURN_CODE:", result.returncode)
""")
            
            # Parse output
            output_lines = result.text.split('\n')
            stdout = ""
            stderr = ""
            returncode = 0
            
            for line in output_lines:
                if line.startswith("STDOUT:"):
                    stdout = line.split(":", 1)[1].strip()
                elif line.startswith("STDERR:"):
                    stderr = line.split(":", 1)[1].strip()
                elif line.startswith("RETURN_CODE:"):
                    try:
                        returncode = int(line.split(":", 1)[1].strip())
                    except:
                        pass
            
            success = returncode == 0
            
            logger.info(f"Session {session_id} executed: {command}")
            logger.info(f"Result: success={success}, stdout={stdout[:200]}...")
            
            return {
                "success": success,
                "stdout": stdout,
                "stderr": stderr,
                "returncode": returncode
            }
            
        except Exception as e:
            logger.error(f"Error executing command: {e}")
            return {
                "success": False,
                "stdout": "",
                "stderr": str(e),
                "returncode": -1
            }
    
    async def read_file(self, session_id: str, file_path: str) -> Dict[str, Any]:
        """Read a file from the sandbox"""
        if session_id not in self.active_sessions:
            raise Exception(f"Session {session_id} not found")
        
        interpreter = self.active_sessions[session_id]["interpreter"]
        session = self.active_sessions[session_id]
        repo_dir = session.get("repo_dir", "/tmp")
        
        try:
            result = interpreter.notebook.exec_cell(f"""
import os

# Change to working directory
os.chdir('{repo_dir}')

try:
    with open('{file_path}', 'r', encoding='utf-8') as f:
        content = f.read()
    print("SUCCESS")
    print("CONTENT:", repr(content))
except FileNotFoundError:
    print("FILE_NOT_FOUND")
except Exception as e:
    print("ERROR:", str(e))
""")
            
            if "SUCCESS" in result.text:
                # Extract content
                lines = result.text.split('\n')
                for line in lines:
                    if line.startswith("CONTENT:"):
                        try:
                            content = eval(line.split(":", 1)[1].strip())  # Safe in sandbox
                            return {
                                "success": True,
                                "content": content,
                                "error": None
                            }
                        except:
                            pass
                
                return {
                    "success": False,
                    "content": "",
                    "error": "Failed to parse file content"
                }
            
            elif "FILE_NOT_FOUND" in result.text:
                return {
                    "success": False,
                    "content": "",
                    "error": f"File not found: {file_path}"
                }
            
            else:
                error_msg = "Unknown error"
                lines = result.text.split('\n')
                for line in lines:
                    if line.startswith("ERROR:"):
                        error_msg = line.split(":", 1)[1].strip()
                        break
                
                return {
                    "success": False,
                    "content": "",
                    "error": error_msg
                }
                
        except Exception as e:
            logger.error(f"Error reading file: {e}")
            return {
                "success": False,
                "content": "",
                "error": str(e)
            }
    
    async def write_file(self, session_id: str, file_path: str, content: str) -> Dict[str, Any]:
        """Write a file to the sandbox"""
        if session_id not in self.active_sessions:
            raise Exception(f"Session {session_id} not found")
        
        interpreter = self.active_sessions[session_id]["interpreter"]
        session = self.active_sessions[session_id]
        repo_dir = session.get("repo_dir", "/tmp")
        
        try:
            # Escape content for safe transmission
            escaped_content = repr(content)
            
            result = interpreter.notebook.exec_cell(f"""
import os

# Change to working directory
os.chdir('{repo_dir}')

try:
    # Create directory if it doesn't exist
    os.makedirs(os.path.dirname('{file_path}'), exist_ok=True)
    
    with open('{file_path}', 'w', encoding='utf-8') as f:
        f.write({escaped_content})
    print("SUCCESS")
except Exception as e:
    print("ERROR:", str(e))
""")
            
            if "SUCCESS" in result.text:
                return {
                    "success": True,
                    "error": None
                }
            else:
                error_msg = "Unknown error"
                lines = result.text.split('\n')
                for line in lines:
                    if line.startswith("ERROR:"):
                        error_msg = line.split(":", 1)[1].strip()
                        break
                
                return {
                    "success": False,
                    "error": error_msg
                }
                
        except Exception as e:
            logger.error(f"Error writing file: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def cleanup_session(self, session_id: str):
        """Clean up a sandbox session"""
        if session_id in self.active_sessions:
            try:
                interpreter = self.active_sessions[session_id]["interpreter"]
                await asyncio.get_event_loop().run_in_executor(None, interpreter.close)
            except Exception as e:
                logger.error(f"Error closing E2B interpreter: {e}")
            
            del self.active_sessions[session_id]
            logger.info(f"Cleaned up session: {session_id}")
    
    async def cleanup_all(self):
        """Clean up all active sessions"""
        for session_id in list(self.active_sessions.keys()):
            await self.cleanup_session(session_id)
    
    def get_session_info(self, session_id: str) -> Optional[Dict[str, Any]]:
        """Get session information"""
        return self.active_sessions.get(session_id)
