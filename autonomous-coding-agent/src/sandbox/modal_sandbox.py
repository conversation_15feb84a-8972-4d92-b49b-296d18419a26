import modal
import asyncio
import logging
from typing import Dict, Any, Optional
from urllib.parse import urlparse
import json
import uuid

logger = logging.getLogger(__name__)


class ModalSandbox:
    """Modal-based sandbox for secure code execution"""
    
    def __init__(self, modal_token_id: str = None, modal_token_secret: str = None):
        self.modal_token_id = modal_token_id
        self.modal_token_secret = modal_token_secret
        self.active_sessions: Dict[str, Any] = {}
        self._setup_modal()
    
    def _setup_modal(self):
        """Initialize Modal client and define sandbox image"""
        
        # Define the sandbox image with necessary tools
        self.sandbox_image = modal.Image.debian_slim(python_version="3.11").pip_install([
            "git",
            "gh",  # GitHub CLI
            "requests",
            "aiofiles",
            "pygithub",
        ]).apt_install([
            "git",
            "curl",
            "vim",
            "wget"
        ])
        
        # Create Modal app
        self.app = modal.App("autonomous-coding-agent")
        
        # Define the sandbox function
        @self.app.function(
            image=self.sandbox_image,
            timeout=1800,  # 30 minutes
            memory=1024,   # 1GB RAM
            cpu=1.0,
            secrets=[modal.Secret.from_name("github-token")] if modal_token_id else []
        )
        def sandbox_execute(command: str, working_dir: str = "/tmp") -> Dict[str, Any]:
            """Execute command in sandbox"""
            import subprocess
            import os
            
            try:
                # Change to working directory
                os.chdir(working_dir)
                
                # Execute command
                result = subprocess.run(
                    command,
                    shell=True,
                    capture_output=True,
                    text=True,
                    timeout=300  # 5 minutes per command
                )
                
                return {
                    "success": result.returncode == 0,
                    "stdout": result.stdout,
                    "stderr": result.stderr,
                    "returncode": result.returncode
                }
                
            except subprocess.TimeoutExpired:
                return {
                    "success": False,
                    "stdout": "",
                    "stderr": "Command timed out",
                    "returncode": -1
                }
            except Exception as e:
                return {
                    "success": False,
                    "stdout": "",
                    "stderr": str(e),
                    "returncode": -1
                }
        
        self.sandbox_execute = sandbox_execute
        
        # Define file operations
        @self.app.function(
            image=self.sandbox_image,
            timeout=300,
            memory=512
        )
        def sandbox_read_file(file_path: str, working_dir: str = "/tmp") -> Dict[str, Any]:
            """Read file from sandbox"""
            import os
            
            try:
                full_path = os.path.join(working_dir, file_path)
                
                if not os.path.exists(full_path):
                    return {
                        "success": False,
                        "content": "",
                        "error": f"File not found: {file_path}"
                    }
                
                with open(full_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                return {
                    "success": True,
                    "content": content,
                    "error": None
                }
                
            except Exception as e:
                return {
                    "success": False,
                    "content": "",
                    "error": str(e)
                }
        
        self.sandbox_read_file = sandbox_read_file
        
        @self.app.function(
            image=self.sandbox_image,
            timeout=300,
            memory=512
        )
        def sandbox_write_file(file_path: str, content: str, working_dir: str = "/tmp") -> Dict[str, Any]:
            """Write file to sandbox"""
            import os
            
            try:
                full_path = os.path.join(working_dir, file_path)
                
                # Create directory if it doesn't exist
                os.makedirs(os.path.dirname(full_path), exist_ok=True)
                
                with open(full_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                
                return {
                    "success": True,
                    "error": None
                }
                
            except Exception as e:
                return {
                    "success": False,
                    "error": str(e)
                }
        
        self.sandbox_write_file = sandbox_write_file
    
    async def create_session(self) -> str:
        """Create a new sandbox session"""
        session_id = str(uuid.uuid4())
        
        # Initialize session workspace
        working_dir = f"/tmp/session_{session_id}"
        
        # Create workspace directory
        result = await self.sandbox_execute.remote.aio(
            f"mkdir -p {working_dir}",
            working_dir="/tmp"
        )
        
        if not result["success"]:
            raise Exception(f"Failed to create session workspace: {result['stderr']}")
        
        self.active_sessions[session_id] = {
            "working_dir": working_dir,
            "created_at": asyncio.get_event_loop().time()
        }
        
        logger.info(f"Created sandbox session: {session_id}")
        return session_id
    
    async def clone_repository(self, session_id: str, repo_url: str) -> Dict[str, Any]:
        """Clone a repository in the sandbox"""
        if session_id not in self.active_sessions:
            raise Exception(f"Session {session_id} not found")
        
        working_dir = self.active_sessions[session_id]["working_dir"]
        
        # Parse repository URL to get repo name
        parsed_url = urlparse(repo_url)
        repo_name = parsed_url.path.rstrip('/').split('/')[-1]
        if repo_name.endswith('.git'):
            repo_name = repo_name[:-4]
        
        # Clone repository
        clone_command = f"git clone {repo_url} {repo_name}"
        result = await self.sandbox_execute.remote.aio(clone_command, working_dir)
        
        if not result["success"]:
            raise Exception(f"Failed to clone repository: {result['stderr']}")
        
        # Get repository info
        repo_dir = f"{working_dir}/{repo_name}"
        
        # Count files
        count_result = await self.sandbox_execute.remote.aio(
            f"find {repo_name} -type f | wc -l",
            working_dir
        )
        
        file_count = int(count_result["stdout"].strip()) if count_result["success"] else 0
        
        # Get repository structure
        tree_result = await self.sandbox_execute.remote.aio(
            f"find {repo_name} -type f -name '*.py' -o -name '*.js' -o -name '*.ts' -o -name '*.md' | head -20",
            working_dir
        )
        
        key_files = tree_result["stdout"].strip().split('\n') if tree_result["success"] else []
        
        # Update session info
        self.active_sessions[session_id].update({
            "repo_name": repo_name,
            "repo_dir": repo_dir,
            "repo_url": repo_url
        })
        
        return {
            "repo_name": repo_name,
            "repo_dir": repo_dir,
            "file_count": file_count,
            "key_files": key_files,
            "clone_output": result["stdout"]
        }
    
    async def execute_command(self, session_id: str, command: str) -> Dict[str, Any]:
        """Execute a command in the sandbox"""
        if session_id not in self.active_sessions:
            raise Exception(f"Session {session_id} not found")
        
        session = self.active_sessions[session_id]
        repo_dir = session.get("repo_dir", session["working_dir"])
        
        result = await self.sandbox_execute.remote.aio(command, repo_dir)
        
        logger.info(f"Session {session_id} executed: {command}")
        logger.info(f"Result: success={result['success']}, stdout={result['stdout'][:200]}...")
        
        return result
    
    async def read_file(self, session_id: str, file_path: str) -> Dict[str, Any]:
        """Read a file from the sandbox"""
        if session_id not in self.active_sessions:
            raise Exception(f"Session {session_id} not found")
        
        session = self.active_sessions[session_id]
        repo_dir = session.get("repo_dir", session["working_dir"])
        
        return await self.sandbox_read_file.remote.aio(file_path, repo_dir)
    
    async def write_file(self, session_id: str, file_path: str, content: str) -> Dict[str, Any]:
        """Write a file to the sandbox"""
        if session_id not in self.active_sessions:
            raise Exception(f"Session {session_id} not found")
        
        session = self.active_sessions[session_id]
        repo_dir = session.get("repo_dir", session["working_dir"])
        
        return await self.sandbox_write_file.remote.aio(file_path, content, repo_dir)
    
    async def cleanup_session(self, session_id: str):
        """Clean up a sandbox session"""
        if session_id in self.active_sessions:
            working_dir = self.active_sessions[session_id]["working_dir"]
            
            # Clean up workspace
            await self.sandbox_execute.remote.aio(f"rm -rf {working_dir}", "/tmp")
            
            del self.active_sessions[session_id]
            logger.info(f"Cleaned up session: {session_id}")
    
    async def cleanup_all(self):
        """Clean up all active sessions"""
        for session_id in list(self.active_sessions.keys()):
            await self.cleanup_session(session_id)
    
    def get_session_info(self, session_id: str) -> Optional[Dict[str, Any]]:
        """Get session information"""
        return self.active_sessions.get(session_id)
