"""
Comprehensive evaluation framework for the autonomous coding agent
"""

import asyncio
import json
import logging
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from pathlib import Path
from datetime import datetime
import subprocess
import tempfile
import shutil
import statistics
from collections import defaultdict

from ..agent.coding_agent import CodingAgent
from ..sandbox.e2b_sandbox import E2BSandbox

logger = logging.getLogger(__name__)


@dataclass
class EvalTask:
    """Represents a single evaluation task"""
    task_id: str
    repo_url: str
    prompt: str
    expected_changes: List[str]  # Files that should be modified
    success_criteria: Dict[str, Any]  # What constitutes success
    difficulty: str  # 'easy', 'medium', 'hard'
    category: str  # 'api', 'frontend', 'backend', 'security', etc.
    timeout_minutes: int = 30


@dataclass
class EvalResult:
    """Results of evaluating a single task"""
    task_id: str
    success: bool
    execution_time: float
    pr_created: bool
    pr_url: Optional[str]
    code_correctness: float  # 0-1 score
    requirement_adherence: float  # 0-1 score  
    code_quality: float  # 0-1 score
    safety_score: float  # 0-1 score
    trajectory_score: float  # 0-1 score for partial credit
    context_efficiency: float  # 0-1 score for file relevance
    error_message: Optional[str]
    detailed_metrics: Dict[str, Any]
    attempt_number: int = 1  # For Pass@k evaluation


@dataclass
class EvalSuite:
    """Collection of evaluation tasks"""
    name: str
    description: str
    tasks: List[EvalTask]
    version: str = "1.0"
    tier: str = "standard"  # 'quick', 'standard', 'comprehensive'


@dataclass
class PassAtKResult:
    """Results of Pass@k evaluation"""
    task_id: str
    k: int
    success_rate: float
    attempts: List[EvalResult]
    reliability_score: float
    variance: float


@dataclass
class TrajectoryStep:
    """Individual step in agent trajectory"""
    step_id: str
    action: str
    inputs: Dict[str, Any]
    outputs: Dict[str, Any]
    success: bool
    duration: float
    error_message: Optional[str] = None


class CodeCorrectnessEvaluator:
    """Evaluates if the generated code is syntactically and functionally correct"""
    
    async def evaluate(self, session_id: str, sandbox_manager: E2BSandbox, 
                      task: EvalTask) -> Tuple[float, Dict[str, Any]]:
        """Evaluate code correctness"""
        metrics = {
            "syntax_errors": 0,
            "runtime_errors": 0,
            "test_failures": 0,
            "compilation_success": False
        }
        
        try:
            # Check for syntax errors in Python files
            python_files = await self._get_python_files(session_id, sandbox_manager)
            
            for file_path in python_files:
                result = await sandbox_manager.execute_command(
                    session_id, f"python -m py_compile {file_path}"
                )
                if not result["success"]:
                    metrics["syntax_errors"] += 1
            
            # Try to run existing tests
            test_result = await sandbox_manager.execute_command(
                session_id, "python -m pytest -v || python -m unittest discover || echo 'No tests found'"
            )
            
            if "FAILED" in test_result.get("stdout", ""):
                # Count test failures
                failures = test_result["stdout"].count("FAILED")
                metrics["test_failures"] = failures
            
            # Check if main application can start
            start_result = await sandbox_manager.execute_command(
                session_id, "timeout 10s python app.py || timeout 10s python main.py || echo 'No main app'"
            )
            
            metrics["compilation_success"] = start_result["returncode"] == 0
            
            # Calculate score based on metrics
            score = 1.0
            if metrics["syntax_errors"] > 0:
                score -= 0.4
            if metrics["runtime_errors"] > 0:
                score -= 0.3
            if metrics["test_failures"] > 0:
                score -= 0.2
            if not metrics["compilation_success"]:
                score -= 0.1
                
            return max(0.0, score), metrics
            
        except Exception as e:
            logger.error(f"Error evaluating code correctness: {e}")
            return 0.0, {"error": str(e)}
    
    async def _get_python_files(self, session_id: str, sandbox_manager: E2BSandbox) -> List[str]:
        """Get list of Python files in the repository"""
        result = await sandbox_manager.execute_command(
            session_id, "find . -name '*.py' -not -path './.*' | head -20"
        )
        
        if result["success"]:
            return [f.strip() for f in result["stdout"].split('\n') if f.strip()]
        return []


class RequirementAdherenceEvaluator:
    """Evaluates how well the implementation matches the requirements"""
    
    def __init__(self, anthropic_api_key: str):
        import anthropic
        self.anthropic_client = anthropic.Anthropic(api_key=anthropic_api_key)
    
    async def evaluate(self, session_id: str, sandbox_manager: E2BSandbox, 
                      task: EvalTask, changes_made: List[str]) -> Tuple[float, Dict[str, Any]]:
        """Evaluate requirement adherence using AI"""
        
        # Get the actual changes made
        diff_result = await sandbox_manager.execute_command(
            session_id, "git diff HEAD~1 HEAD || echo 'No changes'"
        )
        
        actual_diff = diff_result.get("stdout", "")
        
        # Use Claude to evaluate adherence
        evaluation_prompt = f"""
Evaluate how well the following code changes satisfy the given requirements:

ORIGINAL PROMPT: {task.prompt}

ACTUAL CHANGES MADE:
{actual_diff}

EXPECTED FILES TO CHANGE: {task.expected_changes}

Rate the adherence on a scale of 0.0 to 1.0 considering:
1. Does it implement what was requested?
2. Are the changes in the right files?
3. Is the implementation complete?
4. Does it follow the requirements accurately?

Respond with ONLY a JSON object:
{{
    "score": 0.85,
    "reasoning": "Brief explanation of the score",
    "missing_requirements": ["list of unimplemented requirements"],
    "extra_changes": ["list of unexpected changes"]
}}
"""
        
        try:
            response = await asyncio.get_event_loop().run_in_executor(
                None,
                lambda: self.anthropic_client.messages.create(
                    model="claude-sonnet-4-20250514",
                    max_tokens=1000,
                    messages=[{"role": "user", "content": evaluation_prompt}]
                )
            )
            
            result = json.loads(response.content[0].text)
            return result["score"], result
            
        except Exception as e:
            logger.error(f"Error evaluating requirement adherence: {e}")
            return 0.5, {"error": str(e), "reasoning": "Failed to evaluate"}


class CodeQualityEvaluator:
    """Evaluates code quality metrics"""
    
    async def evaluate(self, session_id: str, sandbox_manager: E2BSandbox, 
                      task: EvalTask) -> Tuple[float, Dict[str, Any]]:
        """Evaluate code quality"""
        metrics = {
            "lint_issues": 0,
            "complexity_issues": 0,
            "security_issues": 0,
            "formatting_issues": 0
        }
        
        try:
            # Run linting tools
            lint_result = await sandbox_manager.execute_command(
                session_id, "ruff check . --output-format=json || echo '[]'"
            )
            
            if lint_result["success"]:
                try:
                    lint_data = json.loads(lint_result["stdout"])
                    metrics["lint_issues"] = len(lint_data)
                except json.JSONDecodeError:
                    pass
            
            # Check formatting
            format_result = await sandbox_manager.execute_command(
                session_id, "black --check . || echo 'formatting issues'"
            )
            
            if "would reformat" in format_result.get("stderr", ""):
                metrics["formatting_issues"] = 1
            
            # Security check
            security_result = await sandbox_manager.execute_command(
                session_id, "bandit -r . -f json || echo '{}'"
            )
            
            if security_result["success"]:
                try:
                    security_data = json.loads(security_result["stdout"])
                    metrics["security_issues"] = len(security_data.get("results", []))
                except json.JSONDecodeError:
                    pass
            
            # Calculate quality score
            total_issues = sum(metrics.values())
            if total_issues == 0:
                score = 1.0
            elif total_issues <= 5:
                score = 0.8
            elif total_issues <= 10:
                score = 0.6
            elif total_issues <= 20:
                score = 0.4
            else:
                score = 0.2
                
            return score, metrics
            
        except Exception as e:
            logger.error(f"Error evaluating code quality: {e}")
            return 0.5, {"error": str(e)}


class SafetyEvaluator:
    """Evaluates safety aspects of the generated code"""
    
    async def evaluate(self, session_id: str, sandbox_manager: E2BSandbox, 
                      task: EvalTask) -> Tuple[float, Dict[str, Any]]:
        """Evaluate safety"""
        safety_issues = []
        
        try:
            # Check for dangerous patterns
            dangerous_patterns = [
                "eval(",
                "exec(",
                "os.system(",
                "subprocess.call(",
                "__import__",
                "pickle.loads",
            ]
            
            # Get all modified files
            files_result = await sandbox_manager.execute_command(
                session_id, "git diff --name-only HEAD~1 HEAD"
            )
            
            if files_result["success"]:
                modified_files = files_result["stdout"].strip().split('\n')
                
                for file_path in modified_files:
                    if file_path.endswith('.py'):
                        file_result = await sandbox_manager.read_file(session_id, file_path)
                        
                        if file_result["success"]:
                            content = file_result["content"]
                            
                            for pattern in dangerous_patterns:
                                if pattern in content:
                                    safety_issues.append(f"Dangerous pattern '{pattern}' in {file_path}")
            
            # Calculate safety score
            if len(safety_issues) == 0:
                score = 1.0
            elif len(safety_issues) <= 2:
                score = 0.7
            elif len(safety_issues) <= 5:
                score = 0.4
            else:
                score = 0.0
            
            return score, {"safety_issues": safety_issues}
            
        except Exception as e:
            logger.error(f"Error evaluating safety: {e}")
            return 0.8, {"error": str(e)}


class AutonomousCodingAgentEvaluator:
    """Main evaluator for the autonomous coding agent"""
    
    def __init__(self, anthropic_api_key: str, github_token: str):
        self.coding_agent = CodingAgent(anthropic_api_key, github_token)
        self.sandbox_manager = E2BSandbox()
        
        # Initialize sub-evaluators
        self.correctness_evaluator = CodeCorrectnessEvaluator()
        self.adherence_evaluator = RequirementAdherenceEvaluator(anthropic_api_key)
        self.quality_evaluator = CodeQualityEvaluator()
        self.safety_evaluator = SafetyEvaluator()
        
        # Initialize enhanced evaluators
        from .enhanced_evaluators import TrajectoryEvaluator, ContextEfficiencyEvaluator
        self.trajectory_evaluator = TrajectoryEvaluator(anthropic_api_key)
        self.context_evaluator = ContextEfficiencyEvaluator(anthropic_api_key)
    
    async def evaluate_task(self, task: EvalTask) -> EvalResult:
        """Evaluate a single task"""
        start_time = asyncio.get_event_loop().time()
        session_id = None
        
        try:
            logger.info(f"Starting evaluation of task {task.task_id}")
            
            # Create sandbox session
            session_id = await self.sandbox_manager.create_session()
            
            # Clone repository
            repo_info = await self.sandbox_manager.clone_repository(session_id, task.repo_url)
            
            # Track changes made
            changes_made = []
            pr_url = None
            pr_created = False
            
            # Run the coding agent
            async for event in self.coding_agent.stream_coding_process(
                session_id=session_id,
                repo_info=repo_info,
                prompt=task.prompt,
                sandbox_manager=self.sandbox_manager
            ):
                if event.type == "pr_created":
                    pr_created = True
                    pr_url = event.data.get("pr_url")
                elif event.type == "tool_call" and event.data.get("tool_name") == "apply_change":
                    changes_made.append(event.data.get("tool_input", {}).get("file", ""))
            
            # Run evaluations
            correctness_score, correctness_metrics = await self.correctness_evaluator.evaluate(
                session_id, self.sandbox_manager, task
            )
            
            adherence_score, adherence_metrics = await self.adherence_evaluator.evaluate(
                session_id, self.sandbox_manager, task, changes_made
            )
            
            quality_score, quality_metrics = await self.quality_evaluator.evaluate(
                session_id, self.sandbox_manager, task
            )
            
            safety_score, safety_metrics = await self.safety_evaluator.evaluate(
                session_id, self.sandbox_manager, task
            )
            
            # Enhanced evaluations
            trajectory_score = 0.0
            context_efficiency = 0.0
            trajectory_metrics = {}
            context_metrics = {}
            
            # Mock trajectory for now - in real implementation, collect from agent
            mock_trajectory = []
            if changes_made:
                mock_trajectory = [TrajectoryStep(
                    step_id=f"step_{i}",
                    action=f"modify_file",
                    inputs={"file": file},
                    outputs={"success": True},
                    success=True,
                    duration=1.0
                ) for i, file in enumerate(changes_made)]
            
            if mock_trajectory:
                trajectory_score, trajectory_metrics = await self.trajectory_evaluator.evaluate_trajectory(
                    session_id, self.sandbox_manager, task, mock_trajectory
                )
            
            # Mock accessed files - in real implementation, collect from agent
            accessed_files = changes_made + task.expected_changes
            if accessed_files:
                context_efficiency, context_metrics = await self.context_evaluator.evaluate_context_efficiency(
                    session_id, self.sandbox_manager, task, accessed_files
                )
            
            # Calculate overall success with enhanced metrics
            overall_score = (correctness_score + adherence_score + quality_score + safety_score + trajectory_score + context_efficiency) / 6
            success = overall_score >= 0.7 and pr_created
            
            execution_time = asyncio.get_event_loop().time() - start_time
            
            return EvalResult(
                task_id=task.task_id,
                success=success,
                execution_time=execution_time,
                pr_created=pr_created,
                pr_url=pr_url,
                code_correctness=correctness_score,
                requirement_adherence=adherence_score,
                code_quality=quality_score,
                safety_score=safety_score,
                trajectory_score=trajectory_score,
                context_efficiency=context_efficiency,
                error_message=None,
                detailed_metrics={
                    "correctness": correctness_metrics,
                    "adherence": adherence_metrics,
                    "quality": quality_metrics,
                    "safety": safety_metrics,
                    "trajectory": trajectory_metrics,
                    "context": context_metrics,
                    "changes_made": changes_made
                }
            )
            
        except Exception as e:
            execution_time = asyncio.get_event_loop().time() - start_time
            logger.error(f"Error evaluating task {task.task_id}: {e}")
            
            return EvalResult(
                task_id=task.task_id,
                success=False,
                execution_time=execution_time,
                pr_created=False,
                pr_url=None,
                code_correctness=0.0,
                requirement_adherence=0.0,
                code_quality=0.0,
                safety_score=0.0,
                trajectory_score=0.0,
                context_efficiency=0.0,
                error_message=str(e),
                detailed_metrics={}
            )
            
        finally:
            # Cleanup
            if session_id:
                await self.sandbox_manager.cleanup_session(session_id)
    
    async def evaluate_suite(self, eval_suite: EvalSuite) -> Dict[str, Any]:
        """Evaluate an entire suite of tasks"""
        logger.info(f"Starting evaluation of suite: {eval_suite.name}")
        
        results = []
        
        for task in eval_suite.tasks:
            result = await self.evaluate_task(task)
            results.append(result)
            
            logger.info(f"Task {task.task_id}: {'✅ PASS' if result.success else '❌ FAIL'} "
                       f"(Score: {(result.code_correctness + result.requirement_adherence + result.code_quality + result.safety_score) / 4:.2f})")
        
        # Calculate aggregate metrics
        total_tasks = len(results)
        successful_tasks = sum(1 for r in results if r.success)
        success_rate = successful_tasks / total_tasks if total_tasks > 0 else 0
        
        avg_correctness = sum(r.code_correctness for r in results) / total_tasks
        avg_adherence = sum(r.requirement_adherence for r in results) / total_tasks
        avg_quality = sum(r.code_quality for r in results) / total_tasks
        avg_safety = sum(r.safety_score for r in results) / total_tasks
        avg_trajectory = sum(r.trajectory_score for r in results) / total_tasks
        avg_context = sum(r.context_efficiency for r in results) / total_tasks
        avg_execution_time = sum(r.execution_time for r in results) / total_tasks
        
        return {
            "suite_name": eval_suite.name,
            "suite_version": eval_suite.version,
            "total_tasks": total_tasks,
            "successful_tasks": successful_tasks,
            "success_rate": success_rate,
            "average_scores": {
                "correctness": avg_correctness,
                "adherence": avg_adherence,
                "quality": avg_quality,
                "safety": avg_safety,
                "trajectory": avg_trajectory,
                "context_efficiency": avg_context
            },
            "average_execution_time": avg_execution_time,
            "individual_results": [asdict(r) for r in results],
            "timestamp": datetime.now().isoformat()
        }


# Pre-defined evaluation suites
def create_basic_eval_suite() -> EvalSuite:
    """Create a basic evaluation suite"""
    tasks = [
        EvalTask(
            task_id="health_check_endpoint",
            repo_url="https://github.com/example/fastapi-demo",
            prompt="Add a health check endpoint that returns API version and status",
            expected_changes=["app.py"],
            success_criteria={"endpoint_created": True, "returns_json": True},
            difficulty="easy",
            category="api"
        ),
        EvalTask(
            task_id="input_validation",
            repo_url="https://github.com/example/user-service",
            prompt="Add input validation to all POST endpoints using Pydantic models",
            expected_changes=["app.py", "models.py"],
            success_criteria={"validation_added": True, "pydantic_used": True},
            difficulty="medium",
            category="security"
        ),
        EvalTask(
            task_id="rate_limiting",
            repo_url="https://github.com/example/public-api",
            prompt="Implement rate limiting middleware (100 requests per minute per IP)",
            expected_changes=["app.py", "middleware.py"],
            success_criteria={"rate_limiting_implemented": True, "configurable": True},
            difficulty="hard",
            category="performance"
        )
    ]
    
    return EvalSuite(
        name="Basic Coding Agent Evaluation",
        description="Fundamental tests for autonomous coding capabilities",
        tasks=tasks
    )


def create_swe_bench_style_suite() -> EvalSuite:
    """Create an SWE-bench style evaluation suite"""
    tasks = [
        EvalTask(
            task_id="bug_fix_auth",
            repo_url="https://github.com/example/auth-service",
            prompt="Fix the authentication bug where expired tokens are not properly rejected",
            expected_changes=["auth.py", "middleware.py"],
            success_criteria={"bug_fixed": True, "tests_pass": True},
            difficulty="medium",
            category="bug_fix"
        ),
        EvalTask(
            task_id="feature_user_profile",
            repo_url="https://github.com/example/user-app",
            prompt="Add user profile management with CRUD operations and profile picture upload",
            expected_changes=["models.py", "views.py", "urls.py"],
            success_criteria={"crud_implemented": True, "file_upload": True},
            difficulty="hard",
            category="feature"
        ),
        EvalTask(
            task_id="refactor_database",
            repo_url="https://github.com/example/legacy-app",
            prompt="Refactor the database queries to use async SQLAlchemy instead of synchronous queries",
            expected_changes=["database.py", "models.py", "app.py"],
            success_criteria={"async_implemented": True, "performance_improved": True},
            difficulty="hard",
            category="refactor"
        )
    ]
    
    return EvalSuite(
        name="SWE-Bench Style Evaluation",
        description="Real-world software engineering tasks similar to SWE-bench",
        tasks=tasks
    )
