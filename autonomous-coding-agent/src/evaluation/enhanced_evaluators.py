"""
Enhanced evaluation components with Pass@k, trajectory scoring, and context efficiency
"""

import asyncio
import json
import logging
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from datetime import datetime
import statistics
from collections import defaultdict

from .eval_framework import EvalTask, <PERSON>lResult, PassAtKResult, TrajectoryStep
from ..sandbox.modal_sandbox import ModalSandbox

logger = logging.getLogger(__name__)


class PassAtKEvaluator:
    """Evaluates agent reliability using Pass@k methodology"""
    
    def __init__(self, base_evaluator):
        self.base_evaluator = base_evaluator
    
    async def evaluate_pass_at_k(self, task: EvalTask, k: int = 3) -> PassAtKResult:
        """Run the same task k times and measure reliability"""
        logger.info(f"Running Pass@{k} evaluation for task {task.task_id}")
        
        attempts = []
        success_count = 0
        
        for attempt in range(k):
            logger.info(f"Attempt {attempt + 1}/{k} for task {task.task_id}")
            
            try:
                result = await self.base_evaluator.evaluate_task(task)
                result.attempt_number = attempt + 1
                attempts.append(result)
                
                if result.success:
                    success_count += 1
                    
            except Exception as e:
                logger.error(f"Error in attempt {attempt + 1}: {e}")
                # Create failed result
                failed_result = EvalResult(
                    task_id=task.task_id,
                    success=False,
                    execution_time=0.0,
                    pr_created=False,
                    pr_url=None,
                    code_correctness=0.0,
                    requirement_adherence=0.0,
                    code_quality=0.0,
                    safety_score=0.0,
                    trajectory_score=0.0,
                    context_efficiency=0.0,
                    error_message=str(e),
                    detailed_metrics={},
                    attempt_number=attempt + 1
                )
                attempts.append(failed_result)
        
        success_rate = success_count / k
        
        # Calculate reliability score (penalize high variance)
        if len(attempts) > 1:
            scores = [(r.code_correctness + r.requirement_adherence + r.code_quality + r.safety_score) / 4 
                     for r in attempts]
            variance = statistics.variance(scores) if len(scores) > 1 else 0
            reliability_score = success_rate * (1 - min(variance, 0.5))  # Cap penalty at 0.5
        else:
            variance = 0
            reliability_score = success_rate
        
        return PassAtKResult(
            task_id=task.task_id,
            k=k,
            success_rate=success_rate,
            attempts=attempts,
            reliability_score=reliability_score,
            variance=variance
        )


class TrajectoryEvaluator:
    """Evaluates agent trajectory with partial credit for correct steps"""
    
    def __init__(self, anthropic_api_key: str):
        import anthropic
        self.anthropic_client = anthropic.Anthropic(api_key=anthropic_api_key)
    
    async def evaluate_trajectory(self, session_id: str, sandbox_manager: ModalSandbox, 
                                 task: EvalTask, trajectory: List[TrajectoryStep]) -> Tuple[float, Dict[str, Any]]:
        """Evaluate trajectory with partial credit scoring"""
        
        if not trajectory:
            return 0.0, {"error": "No trajectory provided"}
        
        # Analyze trajectory steps
        trajectory_analysis = await self._analyze_trajectory_steps(trajectory, task)
        
        # Get final state assessment
        final_state = await self._assess_final_state(session_id, sandbox_manager, task)
        
        # Calculate trajectory score
        step_scores = []
        for step_analysis in trajectory_analysis["steps"]:
            step_scores.append(step_analysis["score"])
        
        # Weighted scoring: 40% step quality, 60% final outcome
        avg_step_score = sum(step_scores) / len(step_scores) if step_scores else 0
        trajectory_score = (0.4 * avg_step_score) + (0.6 * final_state["completion_score"])
        
        metrics = {
            "total_steps": len(trajectory),
            "successful_steps": sum(1 for step in trajectory if step.success),
            "avg_step_score": avg_step_score,
            "final_completion_score": final_state["completion_score"],
            "trajectory_analysis": trajectory_analysis,
            "final_state": final_state
        }
        
        return trajectory_score, metrics
    
    async def _analyze_trajectory_steps(self, trajectory: List[TrajectoryStep], task: EvalTask) -> Dict[str, Any]:
        """Analyze individual trajectory steps using AI"""
        
        trajectory_summary = []
        for step in trajectory:
            trajectory_summary.append({
                "step_id": step.step_id,
                "action": step.action,
                "success": step.success,
                "duration": step.duration,
                "error": step.error_message
            })
        
        analysis_prompt = f"""
Analyze this agent trajectory for the task: "{task.prompt}"

Trajectory Steps:
{json.dumps(trajectory_summary, indent=2)}

For each step, rate its quality (0.0-1.0) considering:
1. Is the action appropriate for the task?
2. Does it move toward the goal?
3. Is it efficiently executed?
4. Does it handle errors well?

Respond with JSON:
{{
    "overall_quality": 0.85,
    "steps": [
        {{
            "step_id": "step1",
            "score": 0.9,
            "reasoning": "Good initial analysis"
        }}
    ],
    "trajectory_coherence": 0.8,
    "efficiency_score": 0.7
}}
"""
        
        try:
            response = await asyncio.get_event_loop().run_in_executor(
                None,
                lambda: self.anthropic_client.messages.create(
                    model="claude-3-5-sonnet-20241022",
                    max_tokens=2000,
                    messages=[{"role": "user", "content": analysis_prompt}]
                )
            )
            
            return json.loads(response.content[0].text)
            
        except Exception as e:
            logger.error(f"Error analyzing trajectory: {e}")
            return {
                "overall_quality": 0.5,
                "steps": [{"step_id": step.step_id, "score": 0.5, "reasoning": "Analysis failed"} 
                         for step in trajectory],
                "trajectory_coherence": 0.5,
                "efficiency_score": 0.5
            }
    
    async def _assess_final_state(self, session_id: str, sandbox_manager: ModalSandbox, 
                                 task: EvalTask) -> Dict[str, Any]:
        """Assess the final state of the repository"""
        
        # Get git diff
        diff_result = await sandbox_manager.execute_command(
            session_id, "git diff HEAD~1 HEAD || echo 'No changes'"
        )
        
        # Get list of modified files
        files_result = await sandbox_manager.execute_command(
            session_id, "git diff --name-only HEAD~1 HEAD || echo 'No changes'"
        )
        
        modified_files = files_result.get("stdout", "").strip().split('\n') if files_result.get("success") else []
        
        # Check if expected files were modified
        expected_files = set(task.expected_changes)
        actual_files = set(f for f in modified_files if f.strip())
        
        file_match_score = len(expected_files.intersection(actual_files)) / len(expected_files) if expected_files else 1.0
        
        # Basic completion indicators
        has_changes = bool(modified_files and any(f.strip() for f in modified_files))
        
        completion_score = 0.0
        if has_changes:
            completion_score += 0.5
        
        completion_score += 0.5 * file_match_score
        
        return {
            "completion_score": completion_score,
            "has_changes": has_changes,
            "file_match_score": file_match_score,
            "expected_files": list(expected_files),
            "actual_files": list(actual_files),
            "diff_size": len(diff_result.get("stdout", ""))
        }


class ContextEfficiencyEvaluator:
    """Evaluates how efficiently the agent identifies and uses relevant code context"""
    
    def __init__(self, anthropic_api_key: str):
        import anthropic
        self.anthropic_client = anthropic.Anthropic(api_key=anthropic_api_key)
    
    async def evaluate_context_efficiency(self, session_id: str, sandbox_manager: ModalSandbox, 
                                        task: EvalTask, accessed_files: List[str]) -> Tuple[float, Dict[str, Any]]:
        """Evaluate context efficiency based on file access patterns"""
        
        if not accessed_files:
            return 0.0, {"error": "No files accessed"}
        
        # Get repository structure
        repo_structure = await self._get_repo_structure(session_id, sandbox_manager)
        
        # Analyze file relevance
        relevance_analysis = await self._analyze_file_relevance(task, accessed_files, repo_structure)
        
        # Calculate efficiency metrics
        total_files = len(accessed_files)
        relevant_files = sum(1 for analysis in relevance_analysis["files"] if analysis["relevance_score"] > 0.5)
        
        precision = relevant_files / total_files if total_files > 0 else 0
        
        # Expected files analysis
        expected_files = set(task.expected_changes)
        accessed_files_set = set(accessed_files)
        
        recall = len(expected_files.intersection(accessed_files_set)) / len(expected_files) if expected_files else 1.0
        
        # F1 score for overall efficiency
        if precision + recall > 0:
            f1_score = 2 * (precision * recall) / (precision + recall)
        else:
            f1_score = 0.0
        
        # Context efficiency score (weighted toward precision to avoid noise)
        context_efficiency = 0.6 * precision + 0.4 * recall
        
        metrics = {
            "total_files_accessed": total_files,
            "relevant_files": relevant_files,
            "precision": precision,
            "recall": recall,
            "f1_score": f1_score,
            "relevance_analysis": relevance_analysis,
            "expected_files": list(expected_files),
            "accessed_files": accessed_files
        }
        
        return context_efficiency, metrics
    
    async def _get_repo_structure(self, session_id: str, sandbox_manager: ModalSandbox) -> Dict[str, Any]:
        """Get repository structure information"""
        
        # Get file tree
        tree_result = await sandbox_manager.execute_command(
            session_id, "find . -type f -name '*.py' | head -100"
        )
        
        # Get recent git history
        history_result = await sandbox_manager.execute_command(
            session_id, "git log --oneline -10 || echo 'No git history'"
        )
        
        return {
            "python_files": tree_result.get("stdout", "").strip().split('\n') if tree_result.get("success") else [],
            "recent_commits": history_result.get("stdout", "").strip().split('\n') if history_result.get("success") else []
        }
    
    async def _analyze_file_relevance(self, task: EvalTask, accessed_files: List[str], 
                                    repo_structure: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze relevance of accessed files using AI"""
        
        analysis_prompt = f"""
Analyze file access relevance for this coding task:

TASK: {task.prompt}
EXPECTED CHANGES: {task.expected_changes}
TASK CATEGORY: {task.category}

FILES ACCESSED: {accessed_files}
REPO STRUCTURE: {repo_structure["python_files"][:20]}  # First 20 files

For each accessed file, rate its relevance (0.0-1.0):
- 1.0: Essential for the task
- 0.7-0.9: Very relevant
- 0.4-0.6: Somewhat relevant
- 0.1-0.3: Minimally relevant
- 0.0: Not relevant

Respond with JSON:
{{
    "overall_efficiency": 0.85,
    "files": [
        {{
            "file_path": "path/to/file.py",
            "relevance_score": 0.9,
            "reasoning": "Core implementation file"
        }}
    ]
}}
"""
        
        try:
            response = await asyncio.get_event_loop().run_in_executor(
                None,
                lambda: self.anthropic_client.messages.create(
                    model="claude-3-5-sonnet-20241022",
                    max_tokens=2000,
                    messages=[{"role": "user", "content": analysis_prompt}]
                )
            )
            
            return json.loads(response.content[0].text)
            
        except Exception as e:
            logger.error(f"Error analyzing file relevance: {e}")
            return {
                "overall_efficiency": 0.5,
                "files": [{"file_path": f, "relevance_score": 0.5, "reasoning": "Analysis failed"} 
                         for f in accessed_files]
            }