{"folders": [{"name": "Agent", "path": "../apps/agent"}, {"name": "API", "path": "../apps/api"}, {"name": "Frontend", "path": "../apps/web"}, {"name": "Docs", "path": "../apps/docs"}, {"name": "Root", "path": "../"}], "extensions": {"recommendations": ["joshx.workspace-terminals"]}, "launch": {"version": "0.2.0", "configurations": [], "compounds": [{"name": "Launch Frontend and Backend", "configurations": ["Next.js: Chrome", "Python: FastAPI"]}]}}