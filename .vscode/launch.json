{"version": "0.2.0", "configurations": [{"name": "Attach to LangGraph", "type": "debugpy", "request": "attach", "connect": {"host": "0.0.0.0", "port": 5678}}, {"name": "Debug FastAPI", "type": "debugpy", "request": "launch", "program": "${workspaceFolder}/src/webapp.py", "console": "integratedTerminal", "justMyCode": true, "env": {"PYTHONPATH": "${workspaceFolder}"}}, {"name": "Debug FastAPI with <PERSON><PERSON><PERSON>", "type": "debugpy", "request": "launch", "module": "u<PERSON><PERSON>", "args": ["src.webapp:app", "--host", "0.0.0.0", "--port", "8000", "--reload"], "console": "integratedTerminal", "justMyCode": true, "env": {"PYTHONPATH": "${workspaceFolder}"}}, {"name": "Attach to FastAPI", "type": "debugpy", "request": "attach", "connect": {"host": "localhost", "port": 5679}, "pathMappings": [{"localRoot": "${workspaceFolder}", "remoteRoot": "."}]}]}